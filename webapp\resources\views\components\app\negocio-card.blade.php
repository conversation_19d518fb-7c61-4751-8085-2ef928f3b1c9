<div class="bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-2xl shadow-lg hover:shadow-xl transition duration-300 overflow-hidden">

    <a href="/negocios/{{$negocio->id}}">
        <img src="https://picsum.photos/200/300?rnd={{ md5(uniqid()) }}" alt="Negocio" class="w-full h-48 object-cover">
        
        <div class="p-6 backdrop-blur-lg grid grid-rows-[auto,auto,auto] gap-4">
            <!-- Encabezado: Nombre y Categoría -->
            <div>
                <h2 class="text-2xl font-bold text-gray-800">{{ $negocio->nombre }}</h2>
                <p class="text-white text-sm mt-2">{{ $negocio->categorias->first()->nombre }}</p>
            </div>
            <!-- Enlace "Ver más" con icono de lupa -->
            <div class="flex justify-center">
                <a href="#" class="flex items-center text-blue-600 hover:text-blue-800 font-semibold">
                    <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-4.35-4.35M11 19a8 8 0 100-16 8 8 0 000 16z"></path>
                    </svg>
                    Ver más
                </a>
            </div>
            <!-- Coordenadas en la parte inferior -->
            <div class="text-center">
                <span class="text-gray-700 text-sm">
                    📍 {{ $negocio->ubicacion['latitud'] ?? 'Lat' }}, {{ $negocio->ubicacion['longitud'] ?? 'Long' }}
                </span>
            </div>
        </div>
    </a>
    
</div>
