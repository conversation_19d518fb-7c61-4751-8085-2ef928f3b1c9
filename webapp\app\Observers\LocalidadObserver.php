<?php

namespace App\Observers;

use App\Models\Localidad;
use App\Models\Negocio;
use App\Models\Zona;
use App\Services\CacheService;

class LocalidadObserver
{
    protected CacheService $cache;

    public function __construct(CacheService $cache)
    {
        $this->cache = $cache;
    }

    public function created(Localidad $localidad)
    {
        $this->invalidate();
    }

    public function updated(Localidad $localidad)
    {
        $this->invalidate();
    }

    public function deleted(Localidad $localidad)
    {
        $this->invalidate();
    }

    public function restored(Localidad $localidad)
    {
        $this->invalidate();
    }

    public function forceDeleted(Localidad $localidad)
    {
        $this->invalidate();
    }

    protected function invalidate()
    {
        $this->cache->invalidateCache(Localidad::class, 'localidades_all');
        $this->cache->invalidateCache(Zona::class, 'zonas_all');
        $this->cache->invalidateCache(Negocio::class, 'negocios_all');
    }
}
