<?php

namespace App\Http\Controllers\Api;

use App\Models\Negocio;
use App\Models\Categoria;
use Illuminate\Http\Request;
use App\Services\CacheService;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
use App\Http\Requests\StoreCategoriaRequest;
use App\Http\Requests\UpdateCategoriaRequest;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class CategoriaController extends Controller
{
    use AuthorizesRequests;

    protected $cacheService;

    public function __construct(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // if (!$this->authorize('viewAny', Categoria::class)) {
        //     return response()->json(['error' => 'No autorizado'], 403);
        // }

        $categorias = Cache::rememberForever('categorias_all', function () {
            return Categoria::orderBy('order')->get();
        });

        return $this->cacheService->handleCacheableResponse(
            $categorias,
            function ($data) {
                return $data->map(function ($categoria) {
                    return [
                        'id' => $categoria->id,
                        'nombre' => $categoria->nombre,
                        'descripcion' => $categoria->descripcion,
                        'parent_id' => $categoria->parent_id,
                        'visible' => $categoria->visible,
                        'iconos' => $categoria->getMedia('iconos')->map(fn($media) => [
                            'id' => $media->id,
                            'url' => $media->getUrl(),
                            'thumb' => $media->getUrl('thumb'),
                            'name' => $media->name,
                        ]),
                        'orden' => $categoria->order,
                        'created_at' => $categoria->created_at,
                        'updated_at' => $categoria->updated_at,
                    ];
                });
            }
        );
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $categoria = Categoria::findOrFail($id);

            // if (!$this->authorize('view', $categoria)) {
            //     return response()->json(['error' => 'No autorizado'], 403);
            // }

            return $this->cacheService->handleCacheableResponse(
                $categoria,
                function ($data) {
                    return [
                        'id' => $data->id,
                        'nombre' => $data->nombre,
                        'descripcion' => $data->descripcion,
                        'parent_id' => $data->parent_id,
                        'visible' => $data->visible,
                        'iconos' => $data->getMedia('iconos')->map(fn($media) => [
                            'id' => $media->id,
                            'url' => $media->getUrl(),
                            'thumb' => $media->getUrl('thumb'),
                            'name' => $media->name,
                        ]),
                        'orden' => $data->order,
                        'created_at' => $data->created_at,
                        'updated_at' => $data->updated_at,
                    ];
                }
            );
        } catch (ModelNotFoundException $e) {
            return response()->json(['error' => 'Categoría no encontrada'], 404);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreCategoriaRequest $request)
    {
        if (!$request->authorize()) {
            return response()->json(['error' => 'No autorizado'], 403);
        }

        $validated = $request->validated();
        $categoria = Categoria::create($validated);

        $this->cacheService->invalidateCache(Categoria::class, 'categorias_all');
        $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');

        return response()->json($categoria, 201);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateCategoriaRequest $request, Categoria $categoria)
    {
        if (!$request->authorize()) {
            return response()->json(['error' => 'No autorizado'], 403);
        }

        $validated = $request->validated();
        $categoria->update($validated);

        $this->cacheService->invalidateCache(Categoria::class, 'categorias_all');
        $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');

        return response()->json($categoria);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Categoria $categoria)
    {
        if (!$this->authorize('delete', $categoria)) {
            return response()->json(['error' => 'No autorizado'], 403);
        }

        $categoria->delete();

        $this->cacheService->invalidateCache(Categoria::class, 'categorias_all');
        $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');

        return response()->noContent();
    }
}
