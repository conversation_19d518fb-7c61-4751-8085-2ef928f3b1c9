<?php
// webapp\tests\Feature\Web\EventoTest.php

namespace Tests\Feature\Web;

use Carbon\Carbon;
use Tests\BaseTest;
use App\Models\User;
use App\Models\Evento;
use Livewire\Livewire;
use App\Models\Negocio;
use App\Models\Categoria;
use Illuminate\Http\UploadedFile;
use Spatie\Permission\Models\Role;
use PHPUnit\Framework\Attributes\Group;
use Filament\Tables\Actions\DeleteAction;
use App\Filament\Resources\EventoResource;
use Database\Seeders\RolesPermissionSeeder;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

#[Group('evento')]
#[Group('webapp')]
#[Group('evento_webapp')]
class EventoTest extends BaseTest
{
    use RefreshDatabase;

    private array $usuarios;

    private Negocio $negocio;
    private Categoria $categoria;

    protected function setUp(): void
    {
        parent::setUp();

        app()->setLocale('es');

        $this->seed(RolesPermissionSeeder::class);

        foreach (Role::all() as $rol) {
            $user = User::factory()->create();
            $user->assignRole($rol->name);

            $this->usuarios[$rol->name] = [
                'usuario' => $user,
            ];
        }

        $this->categoria = Categoria::factory()->create();
        $this->negocio = Negocio::factory()
            ->conSuscripcion()
            ->conCategorias([$this->categoria->id])
            ->create([
                'user_id' => $this->usuarios['cliente']['usuario']->id,
            ]);
    }

    public function test_el_sistema_muestra_las_paginas_de_evento_correctamente_segun_el_rol_del_usuario(): void
    {
        $evento = Evento::factory()->create([
            'negocio_id' => $this->negocio->id,
        ]);

        // 🟢 Test positivos
        $test_roles = $this->_getRolesPositivos(['evento.list', 'evento.create', 'evento.update']);
        $test_roles[] = 'cliente';

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $this->get(EventoResource::getUrl('index'))->assertSuccessful();
            $this->get(EventoResource::getUrl('create'))->assertSuccessful();
            $this->get(EventoResource::getUrl('edit', ['record' => $evento->id]))->assertSuccessful();
        }

        // 🔴 Test negativos
        $test_roles = $this->_getRolesNegativos(['evento.list', 'evento.create', 'evento.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $this->get(EventoResource::getUrl('index'))->assertForbidden();
            $this->get(EventoResource::getUrl('create'))->assertForbidden();
            $this->get(EventoResource::getUrl('edit', ['record' => $evento->first()->id]))->assertForbidden();
        }
    }

    public function test_el_sistema_crea_un_nuevo_evento_solo_con_datos_validos(): void
    {
        // 🟢 Test positivos
        $test_roles = $this->_getRolesPositivos(['evento.create']);
        $i = 1;

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $newData = Evento::factory()->make([
                'nombre' => 'Evento ' . $i,
                'negocio_id' => $this->negocio->id
            ])->toArray();

            $fechaInicio = $newData['fecha_inicio'];
            $fechaFin = $newData['fecha_fin'];

            // Simular la carga de archivos
            $maxImagenes = 5;
            $files = [];
            for ($j = 0; $j < $maxImagenes; $j++) {
                $files[] = UploadedFile::fake()->image('imagen' . $j . '.jpg', 100, 100);
            }

            $test = Livewire::test(EventoResource\Pages\CreateEvento::class);

            $test
                ->fillForm([
                    'nombre' => $newData['nombre'],
                    'descripcion' => $newData['descripcion'],
                    'url' => $newData['url'],
                    'fecha_inicio' => $fechaInicio->format('Y-m-d H:i:s'),
                    'fecha_fin' => $fechaFin->format('Y-m-d H:i:s'),
                    'negocio_id' => $newData['negocio_id'],
                ])
                ->set('data.imagenes', $files)
                ->call('create');

            if ($test->errors()->isNotEmpty()) {
                dd($test->errors(), $fechaInicio->format('Y-m-d H:i:s'), $fechaFin->format('Y-m-d H:i:s'));
            } else {
                $test->assertHasNoFormErrors();
            }

            $this->assertDatabaseHas(Evento::class, [
                'nombre' => $newData['nombre'],
                'descripcion' => $newData['descripcion'],
                'url' => $newData['url'],
                'fecha_inicio' => $fechaInicio->format('Y-m-d H:i'),
                'fecha_fin' => $fechaFin->format('Y-m-d H:i'),
                'negocio_id' => $newData['negocio_id'],
            ]);

            $this->assertDatabaseCount(Evento::class, $i++);

            $evento = Evento::first();
            $this->assertTrue($evento->hasMedia('imagenes_eventos'));
            $this->assertEquals($maxImagenes, $evento->getMedia('imagenes_eventos')->count());

            // Evento::truncate();
        }

        // 🔴 Test negativos

        $test_roles = $this->_getRolesPositivos(['evento.create']);

        $test_cases = [
            // caso 1: campos vacíos
            [
                'payload' => [],
                'expectedInvalid' => ['nombre', 'fecha_inicio', 'fecha_fin', 'negocio_id']
            ],
            // Negocio no válido
            [
                'payload' => [
                    'negocio_id' => 999999,
                ],
                'expectedInvalid' => ['negocio_id']
            ],
            // Fecha de fin anterior a la de inicio
            [
                'payload' => [
                    'fecha_inicio' => now()->addDays(10),
                    'fecha_fin' => now()->addDays(5),
                ],
                'expectedInvalid' => ['fecha_fin']
            ],
            // Fecha de inicio anterior a la actual
            [
                'payload' => [
                    'fecha_inicio' => now()->subDays(10),
                    'fecha_fin' => now()->addDays(10),
                ],
                'expectedInvalid' => ['fecha_inicio']
            ],
            // Número de archivos subidos mayor que max, no se puede probar porque el limite solo se pone en el form del filament Resource Evento
            // [
            //     'payload' => [
            //         'imagenes' => $files[] = UploadedFile::fake()->image('imagen.jpg', 100, 100),
            //     ],
            //     'expectedInvalid' => ['imagenes']
            // ],
        ];

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $test = Livewire::test(EventoResource\Pages\CreateEvento::class);

            foreach ($test_cases as $case) {
                $test->fillForm($case['payload'])
                    ->set('data.imagenes', $case['payload']['imagenes'] ?? [])
                    ->call('create')
                    ->assertHasFormErrors($case['expectedInvalid']);
            }
        }
    }

    public function test_el_sistema_edita_un_evento_solo_con_datos_validos(): void
    {
        // 🟢 Test positivos
        $test_roles = $this->_getRolesPositivos(['evento.update']);
        // TODO: Agregar test para rol cliente
        // $test_roles[] = 'cliente';

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $evento = Evento::factory()->conImagenes()->create([
                'negocio_id' => $this->negocio->id,
            ]);

            $newData = Evento::factory()->make([
                'negocio_id' => $this->negocio->id
            ])->toArray();

            // Simular la carga de un archivo
            $file = UploadedFile::fake()->image('icono.jpg', 100, 100);

            // Verificar que tiene un icono inicialmente
            $this->assertTrue($evento->hasMedia('imagenes_eventos'));
            $initialMediaCount = $evento->getMedia('imagenes_eventos')->count();

            $test = Livewire::test(EventoResource\Pages\EditEvento::class, ['record' => $evento->id]);

            $test
                ->assertFormSet([
                    'nombre' => $evento->nombre,
                    'descripcion' => $evento->descripcion,
                    'url' => $evento->url,
                    'fecha_inicio' => $evento->fecha_inicio->format('Y-m-d H:i'),
                    'fecha_fin' => $evento->fecha_fin->format('Y-m-d H:i'),
                    'negocio_id' => $evento->negocio_id,
                ])
                ->fillForm([
                    'nombre' => $newData['nombre'],
                    'descripcion' => $newData['descripcion'],
                    'url' => $newData['url'],
                    'fecha_inicio' => $newData['fecha_inicio'],
                    'fecha_fin' => $newData['fecha_fin'],
                    'negocio_id' => $newData['negocio_id'],
                ])
                ->set('data.imagenes', $file)
                ->call('save')
                ->assertHasNoFormErrors()
            ;

            $this->assertTrue($evento->hasMedia('imagenes_eventos'));
            $this->assertEquals($initialMediaCount, $evento->getMedia('imagenes_eventos')->count());

            $this->assertDatabaseHas(Evento::class, [
                'nombre' => $newData['nombre'],
                'descripcion' => $newData['descripcion'],
                'url' => $newData['url'],
                'fecha_inicio' => $newData['fecha_inicio']->format('Y-m-d H:i'),
                'fecha_fin' => $newData['fecha_fin']->format('Y-m-d H:i'),
                'negocio_id' => $newData['negocio_id'],
            ]);

            $this->assertDatabaseCount(Evento::class, 1);

            Evento::truncate();
        }

        // 🔴 Test negativos
        $test_roles = $this->_getRolesPositivos(['evento.update']);
        $test_cases = [
            // caso 1: campos vacíos
            [
                'payload' => [],
                'expectedInvalid' => ['nombre', 'fecha_inicio', 'fecha_fin', 'negocio_id']
            ],
            // Negocio no válido
            [
                'payload' => [
                    'negocio_id' => 999999,
                ],
                'expectedInvalid' => ['negocio_id']
            ],
            // Fecha de fin anterior a la de inicio
            [
                'payload' => [
                    'fecha_inicio' => now()->addDays(10),
                    'fecha_fin' => now()->addDays(5),
                ],
                'expectedInvalid' => ['fecha_fin']
            ],
        ];

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $evento = Evento::factory()->create([
                'negocio_id' => $this->negocio->id,
            ]);

            $test = Livewire::test(EventoResource\Pages\EditEvento::class, ['record' => $evento->id]);

            foreach ($test_cases as $case) {
                $test->fillForm($case['payload'])
                    ->call('save')
                    ->assertHasFormErrors($case['expectedInvalid']);
            }
        }
    }

    public function test_el_sistema_elimina_correctamente_un_evento(): void
    {
        // 🟢 Test positivos

        $test_roles = $this->_getRolesPositivos(['evento.delete']);
        $test_roles[] = 'cliente';

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $evento = Evento::factory()->conImagenes()->create([
                'negocio_id' => $this->negocio->id,
            ]);

            $test = Livewire::test(EventoResource\Pages\ListEventos::class);
            $test
                ->assertTableActionVisible(DeleteAction::class, $evento)
                ->callTableAction(DeleteAction::class, $evento);

            $this->assertDatabaseCount(Evento::class, 0);
            $this->assertFalse($evento->hasMedia('imagenes_eventos'));
        }

        // 🔴 Test negativo
        /** @var User */
        $user = User::factory()->create();

        $user->assignRole('cliente');
        $this->actingAs($user);

        $test = Livewire::test(EventoResource\Pages\ListEventos::class);
        $test->assertTableActionHidden(DeleteAction::class, $evento);
    }

    #[Group('test')]
    public function _test_el_sistema_____(): void
    {

        // 🟢 Test positivos

        // $categoria = Zona::factory()->create();

        $test_roles = [];

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            // $test = Livewire::test(ListAplicaciones::class);
        }

        // 🔴 Test negativos

        $test_roles = [];

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            // $test = Livewire::test(ListAplicaciones::class);
        }
    }
}
