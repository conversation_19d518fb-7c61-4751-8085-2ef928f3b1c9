import 'package:flutter_cache_manager/flutter_cache_manager.dart';
// import 'dart:io';

class SvgCacheManager {
  static final SvgCacheManager _instance = SvgCacheManager._();

  factory SvgCacheManager() {
    return _instance;
  }

  SvgCacheManager._();

  final CacheManager cacheManager = CacheManager(
    Config(
      'svg_cache',
      stalePeriod: const Duration(days: 30),
      maxNrOfCacheObjects: 100,
    ),
  );

  Future<String?> getSvgFromCache(String url) async {
    try {
      // Intenta obtener el archivo de la caché
      final fileInfo = await cacheManager.getFileFromCache(url);

      // Si existe en la caché, lee el contenido
      if (fileInfo != null) {
        final file = fileInfo.file;
        final svgContent = await file.readAsString();
        return svgContent;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<String?> downloadAndCacheSvg(String url) async {
    try {
      // Descarga y guarda en caché
      final fileInfo = await cacheManager.downloadFile(url);
      final svgContent = await fileInfo.file.readAsString();
      return svgContent;
    } catch (e) {
      return null;
    }
  }

  // Obtiene SVG de caché o lo descarga si no existe
  Future<String?> getSvg(String url) async {
    // Primero intenta obtenerlo de la caché
    String? svgContent = await getSvgFromCache(url);

    // Si no está en caché y hay conexión, descárgalo
    svgContent ??= await downloadAndCacheSvg(url);

    return svgContent;
  }
}
