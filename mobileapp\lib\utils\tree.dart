import 'package:mia/utils/treeable.dart';

class Tree<T extends Treeable> {
  final T data;
  // final List<Tree<T>> children;
  List<Tree<dynamic>> children;

  Tree<T>? parent;

  Tree({required this.data, this.children = const [], this.parent}) {
    // Crear una copia mutable de la lista
    children = List.from(children);
  }

// Funciones para construir el árbol

  static Tree<T>? buildTree<T extends Treeable>(List<T> elementos, {T? raiz}) {
    // Si se proporciona una raíz, usarla. Si no, buscar elementos sin padre.
    List<T> elementosPrincipales = raiz != null
        ? [raiz]
        : elementos.where((elemento) => elemento.getIdPadre() == null).toList();

    if (elementosPrincipales.isEmpty) {
      return null; // No hay elementos, devuelve null
    }

    if (elementosPrincipales.length == 1 && raiz == null) {
      // Solo un elemento principal, no necesita nodo raíz virtual
      return _buildTreeRecursivamente<T>(elementosPrincipales[0], elementos);
    } else {
      // Múltiples elementos principales o raíz especificada, crear nodo raíz virtual
      final raizArbol = Tree<T>(
          data: raiz ?? (elementosPrincipales[0].createDummyNode() as T));

      for (final principal in elementosPrincipales) {
        final rama = _buildTreeRecursivamente(principal, elementos);
        if (rama != null) {
          raizArbol.children.add(rama);
          rama.parent = raizArbol; // Asigna el padre
        }
      }
      return raizArbol;
    }
  }

  static Tree<T>? _buildTreeRecursivamente<T extends Treeable>(
      T elemento, List<T> elementos) {
    final nodo = Tree<T>(data: elemento);

    final hijos =
        elementos.where((e) => e.getIdPadre() == elemento.getId()).toList();

    for (final hijo in hijos) {
      final subRama = _buildTreeRecursivamente(hijo, elementos);
      if (subRama != null) {
        nodo.children.add(subRama);
        subRama.parent = nodo; // Asigna el padre
      }
    }

    return nodo;
  }

  // Funciones para navegar por el árbol

  // Descendientes

  List<Tree<T>> get descendientes {
    List<Tree<T>> descendientes = [];
    _buscarDescendientesRecursivamente(descendientes);
    return descendientes;
  }

  void _buscarDescendientesRecursivamente(List<Tree<T>> descendientes) {
    for (final hijo in children) {
      descendientes.add(hijo as Tree<T>); // Casting seguro
      hijo._buscarDescendientesRecursivamente(descendientes);
    }
  }

  // Ancestros

  List<Tree<T>> get antecesores {
    List<Tree<T>> antecesores = [];
    _buscarAntecesoresRecursivamente(antecesores);
    return antecesores;
  }

  void _buscarAntecesoresRecursivamente(List<Tree<T>> antecesores) {
    if (parent != null) {
      antecesores.add(parent!);
      parent!._buscarAntecesoresRecursivamente(antecesores);
    }
  }

  // Hermanos

  List<Tree<T>> get siblings {
    if (parent == null) {
      // Si no tiene padre, busca otros nodos sin padre (hermanos "virtuales")
      return _buscarHermanosVirtuales();
    } else {
      // Si tiene padre, busca hermanos tradicionales
      return parent!.children
          .where((hermano) => hermano.data != data)
          .map((hermano) => hermano as Tree<T>)
          .toList();
    }
  }

  List<Tree<T>> _buscarHermanosVirtuales() {
    // Busca todos los nodos en el árbol que no tienen padre
    List<Tree<T>> hermanosVirtuales = [];
    _buscarHermanosRecursivamente(this, hermanosVirtuales);
    return hermanosVirtuales;
  }

  void _buscarHermanosRecursivamente(
      Tree<T> nodoActual, List<Tree<T>> hermanos) {
    if (nodoActual.parent == null && nodoActual != this) {
      hermanos.add(nodoActual);
    }

    for (final hijo in nodoActual.children) {
      _buscarHermanosRecursivamente(hijo as Tree<T>, hermanos);
    }
  }

  // Utilidades

  bool contiene(T elemento) {
    // 1. Verificar si el elemento actual es el que estamos buscando
    if (data == elemento) {
      return true;
    }

    // 2. Recorrer los hijos y buscar recursivamente en cada subárbol
    for (final hijo in children) {
      if (hijo.contiene(elemento)) {
        return true;
      }
    }

    // 3. Si no se encuentra en el nodo actual ni en ningún hijo, el elemento no está en el árbol
    return false;
  }
}
