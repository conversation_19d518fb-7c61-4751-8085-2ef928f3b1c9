# API REST

## Base URL

```
Desarrollo: http://localhost:8000/api
Producción: https://tu-dominio.com/api
```

## Autenticación

La API utiliza **Laravel Sanctum** con tokens Bearer.

### Login

```http
POST /login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Respuesta exitosa (200)**:
```json
{
  "token": "1|abc123def456...",
  "user": {
    "id": 1,
    "name": "Usuario Ejemplo",
    "email": "<EMAIL>"
  }
}
```

### Logout

```http
POST /logout
Authorization: Bearer {token}
```

### Uso del Token

Todas las rutas protegidas requieren el header:
```http
Authorization: Bearer {token}
```

## Endpoints

### Negocios

#### Listar todos los negocios

```http
GET /v1/negocios
```

**Respuesta (200)**:
```json
[
  {
    "id": 1,
    "nombre": "Restaurante Ejemplo",
    "descripcion": "Descripción del negocio",
    "direccion": "Calle Principal 123",
    "contacto": "+34 600 000 000",
    "ubicacion": {
      "latitud": "40.4168",
      "longitud": "-3.7038"
    },
    "horario": {
      "lunes": ["09:00-14:00", "17:00-21:00"],
      "martes": ["09:00-14:00", "17:00-21:00"]
    },
    "zona_id": 1,
    "zona": {
      "id": 1,
      "nombre": "Centro"
    },
    "categorias": [
      {
        "id": 1,
        "nombre": "Restaurantes",
        "iconos": [...]
      }
    ],
    "suscripcion": {
      "id": 1,
      "plan": "basic",
      "status": 1
    },
    "eventos": [...],
    "imagenes": [...],
    "logos": [...]
  }
]
```

#### Ver un negocio

```http
GET /v1/negocios/{id}
```

#### Crear negocio (requiere auth)

```http
POST /v1/negocios
Authorization: Bearer {token}
Content-Type: application/json

{
  "nombre": "Nuevo Negocio",
  "descripcion": "Descripción",
  "direccion": "Calle 123",
  "ubicacion": {
    "latitud": "40.4168",
    "longitud": "-3.7038"
  },
  "contacto": "+34 600 000 000",
  "zona_id": 1,
  "categorias": [1, 2]
}
```

#### Actualizar negocio (requiere auth)

```http
PUT /v1/negocios/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "nombre": "Nombre Actualizado",
  ...
}
```

#### Eliminar negocio (requiere auth)

```http
DELETE /v1/negocios/{id}
Authorization: Bearer {token}
```

### Categorías

#### Listar todas las categorías

```http
GET /v1/categorias
```

**Respuesta (200)**:
```json
[
  {
    "id": 1,
    "nombre": "Restaurantes",
    "descripcion": "Lugares para comer",
    "parent_id": null,
    "nivel": 0,
    "orden": 1,
    "visible": true,
    "permitir_eventos": true,
    "iconos": [
      {
        "id": 1,
        "file_name": "icono.svg",
        "url": "https://..."
      }
    ]
  }
]
```

#### Ver una categoría

```http
GET /v1/categorias/{id}
```

### Zonas

#### Listar todas las zonas

```http
GET /v1/zonas
```

**Respuesta (200)**:
```json
[
  {
    "id": 1,
    "nombre": "Centro",
    "descripcion": "Zona centro de la ciudad",
    "coordenadas": {
      "type": "Polygon",
      "coordinates": [...]
    },
    "localidad_id": 1
  }
]
```

#### Ver una zona

```http
GET /v1/zonas/{id}
```

### Localidades

#### Listar todas las localidades

```http
GET /v1/localidades
```

**Respuesta (200)**:
```json
[
  {
    "id": 1,
    "nombre": "Madrid",
    "ubicacion": {
      "lat": "40.4168",
      "lng": "-3.7038"
    },
    "limites": {
      "type": "Polygon",
      "coordinates": [...]
    }
  }
]
```

### Eventos

#### Listar todos los eventos

```http
GET /v1/eventos
```

**Respuesta (200)**:
```json
[
  {
    "id": 1,
    "nombre": "Promoción Especial",
    "descripcion": "Descripción del evento",
    "url": "https://...",
    "fecha_inicio": "2025-10-01 10:00:00",
    "fecha_fin": "2025-10-31 23:59:59",
    "negocio_id": 1,
    "imagenes": [...]
  }
]
```

#### Ver un evento

```http
GET /v1/eventos/{id}
```

#### Crear evento (requiere auth)

```http
POST /v1/eventos
Authorization: Bearer {token}
Content-Type: application/json

{
  "nombre": "Nuevo Evento",
  "descripcion": "Descripción",
  "fecha_inicio": "2025-10-01 10:00:00",
  "fecha_fin": "2025-10-31 23:59:59",
  "negocio_id": 1
}
```

## Caché HTTP

La API implementa caché HTTP con headers `Last-Modified` y `If-Modified-Since`.

### Flujo de Caché

1. **Primera petición**:
```http
GET /v1/negocios

Response:
200 OK
Last-Modified: Wed, 01 Oct 2025 12:00:00 GMT
[... datos ...]
```

2. **Peticiones subsiguientes**:
```http
GET /v1/negocios
If-Modified-Since: Wed, 01 Oct 2025 12:00:00 GMT

Response (si no hay cambios):
304 Not Modified

Response (si hay cambios):
200 OK
Last-Modified: Wed, 01 Oct 2025 13:00:00 GMT
[... datos actualizados ...]
```

## Códigos de Estado

| Código | Significado |
|--------|-------------|
| 200 | OK - Petición exitosa |
| 201 | Created - Recurso creado |
| 304 | Not Modified - Usar caché |
| 400 | Bad Request - Datos inválidos |
| 401 | Unauthorized - No autenticado |
| 403 | Forbidden - Sin permisos |
| 404 | Not Found - Recurso no encontrado |
| 422 | Unprocessable Entity - Validación fallida |
| 500 | Internal Server Error - Error del servidor |

## Errores

### Formato de Error

```json
{
  "message": "Mensaje de error",
  "errors": {
    "campo": ["Error específico del campo"]
  }
}
```

### Ejemplo de Error de Validación (422)

```json
{
  "message": "The given data was invalid.",
  "errors": {
    "nombre": ["El campo nombre es obligatorio."],
    "zona_id": ["La zona seleccionada no existe."]
  }
}
```

## Rate Limiting

La API implementa rate limiting:
- **Rutas públicas**: 60 peticiones/minuto
- **Rutas autenticadas**: 120 peticiones/minuto

Headers de respuesta:
```http
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
```

## Paginación

Algunos endpoints soportan paginación (actualmente no implementada, pero preparada para futuro):

```http
GET /v1/negocios?page=2&per_page=20
```

## Filtros

Algunos endpoints soportan filtros (futuro):

```http
GET /v1/negocios?zona_id=1&categoria_id=2
```

## Versionado

La API está versionada con el prefijo `/v1/`. Futuras versiones usarán `/v2/`, etc.

## CORS

La API permite CORS para dominios configurados en el backend.

## Ejemplos de Uso

### JavaScript/Fetch

```javascript
// Login
const response = await fetch('http://localhost:8000/api/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
});
const { token } = await response.json();

// Usar token
const negocios = await fetch('http://localhost:8000/api/v1/negocios', {
  headers: { 'Authorization': `Bearer ${token}` }
});
```

### Dart/Flutter

```dart
// Login
final response = await http.post(
  Uri.parse('http://localhost:8000/api/login'),
  headers: {'Content-Type': 'application/json'},
  body: jsonEncode({
    'email': '<EMAIL>',
    'password': 'password123',
  }),
);
final token = jsonDecode(response.body)['token'];

// Usar token
final negocios = await http.get(
  Uri.parse('http://localhost:8000/api/v1/negocios'),
  headers: {'Authorization': 'Bearer $token'},
);
```

### cURL

```bash
# Login
curl -X POST http://localhost:8000/api/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Usar token
curl http://localhost:8000/api/v1/negocios \
  -H "Authorization: Bearer 1|abc123..."
```

## Especificación OpenAPI

Para una especificación completa en formato OpenAPI/Swagger, ver:
- [openapi.yaml](./openapi.yaml) (próximamente)

