<?php

namespace App\Filament\Pages;

use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Utils;
use Filament\Pages\Page;
use Filament\Tables\Table;
use Filament\Actions\Action;
use App\Models\BackupFilesWrapper;
use App\Models\User;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\Auth;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Response;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Actions\Action as TableAction;

class Backups extends Page implements HasTable
{
    use InteractsWithTable;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.pages.backups';

    protected static ?string $navigationGroup = 'Administración';

    public static function canAccess(): bool
    {
        /** @var User */
        $user = Auth::guard('web')->user();

        return $user->can('system.admin') && Filament::getCurrentPanel()->getId() === "admin";
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('create_backup')
                ->label('Nuevo Backup')
                ->action(fn() => Artisan::call('backup:run'))
                ->after(fn() => $this->js('window.location.reload()')),

            Action::make('clean_backup')
                ->label('Limpiar antiguos')
                ->requiresConfirmation()
                ->action(fn() => Artisan::call('backup:clean'))
                ->after(fn() => $this->js('window.location.reload()')),
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(BackupFilesWrapper::query())
            ->columns([
                TextColumn::make('nombre'),
                TextColumn::make('fecha'),
                TextColumn::make('size')
                    ->label('Tamaño'),
            ])
            ->defaultSort('fecha', 'desc')
            ->actions([
                TableAction::make('download')
                    ->label('Descargar')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->action(function ($record) {
                        return $this->downloadBackupFile($record->nombre);
                    }),
                DeleteAction::make('delete')
                    ->action(fn($record) => Storage::disk('backup')->delete($record->nombre)),
            ]);
    }

    public function downloadBackupFile(string $filename)
    {
        $file_path = Storage::disk('backup')->path($filename);

        if (!Storage::disk('backup')->exists($filename)) {
            return response()->json(['error' => 'Archivo no encontrado'], 404);
        }

        $nombre = 'backup-' . config('app.name') . '-' . config('app.env') . '-' . now()->format('dmYHis') . '.zip';
        $filesize = filesize($file_path);

        // Configuración de Guzzle para descarga
        $client = new Client([
            'stream' => true,
            'headers' => [
                'Content-Type' => 'application/zip',
                'Content-Disposition' => 'attachment; filename="' . $nombre . '"',
                'Content-Length' => $filesize,
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0'
            ]
        ]);

        // Aumentar límite de memoria
        ini_set('memory_limit', '1G');

        // Transmitir archivo usando Guzzle
        return response()->stream(
            function () use ($file_path, $client) {
                $resource = Utils::tryFopen($file_path, 'rb');
                $body = Utils::streamFor($resource);

                while (!$body->eof()) {
                    echo $body->read(1024 * 1024); // Leer chunks de 1MB
                    flush();
                }

                $body->close();
            },
            200,
            [
                'Content-Type' => 'application/zip',
                'Content-Disposition' => 'attachment; filename="' . $nombre . '"',
                'Content-Length' => filesize($file_path)
            ]
        );
    }
}
