import 'package:flutter/material.dart';
import 'package:mia/models/zona.dart';

class FiltroNegocioZona extends StatelessWidget {
  final List<Zona> zonas;
  final int? selectedZonaId;
  final Function(int?) onZonaSelected;

  const FiltroNegocioZona({
    super.key,
    required this.zonas,
    required this.selectedZonaId,
    required this.onZonaSelected,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 48,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          // Opción "Todas las zonas"
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            child: ChoiceChip(
              label: const Text("Todas las zonas"),
              selected: selectedZonaId ==
                  null, // Seleccionado cuando no hay filtro activo
              onSelected: (yes) {
                if (yes) {
                  onZonaSelected(null); // Eliminar filtro de zona
                }
              },
            ),
          ),
          // Resto de zonas
          ...zonas.map((zona) {
            final sel = selectedZonaId == zona.id;
            return Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
              child: ChoiceChip(
                label: Text(zona.nombre),
                selected: sel,
                onSelected: (yes) => onZonaSelected(yes ? zona.id : null),
              ),
            );
          }),
        ],
      ),
    );
  }
}
