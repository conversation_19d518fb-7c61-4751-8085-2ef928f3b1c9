<?php

namespace App\Filament\Resources\NegocioResource\Pages;

use App\Models\User;
use Filament\Actions;
use App\Models\Negocio;
use App\Services\CacheService;
use Illuminate\Support\Facades\Auth;
use Filament\Tables\Actions\BulkAction;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use App\Filament\Resources\NegocioResource;

class ListNegocios extends ListRecords
{
    protected static string $resource = NegocioResource::class;

    protected CacheService $cacheService;

    public function boot(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    protected function getHeaderActions(): array
    {
        /** @var User */
        $user = Auth::user();
        if (!$user->can('negocio.create')) {
            return [];
        }

        return [
            Actions\CreateAction::make(),
        ];
    }

    protected function getTableBulkActions(): array
    {
        return [
            BulkAction::make('delete')
                ->label('Eliminar')
                ->action(function ($records) {
                    $records->each(function ($record) {
                        $record->delete();
                    });

                    Notification::make()
                        ->title('Negocios eliminados')
                        ->success()
                        ->send();

                    $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');
                })
                ->requiresConfirmation(),
        ];
    }
}
