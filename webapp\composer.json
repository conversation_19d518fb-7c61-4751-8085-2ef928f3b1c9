{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "calebporzio/sushi": "^2.5", "filament/filament": "^3.2", "filament/spatie-laravel-media-library-plugin": "^3.2", "laravel/framework": "^11.31", "laravel/jetstream": "^5.3", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "livewire/livewire": "^3.0", "lubusin/laravel-decomposer": "^1.3", "predis/predis": "^3.0", "sentry/sentry-laravel": "^4.14", "solution-forest/filament-tree": "^2.1", "spatie/laravel-backup": "^9.2", "spatie/laravel-medialibrary": "^11.12", "spatie/laravel-permission": "^6.10", "webbingbrasil/filament-maps": "3.x-dev", "ysfkaya/filament-phone-input": "^3.1"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.14", "fakerphp/faker": "^1.23", "laravel/pail": "^1.1", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.0.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Lubusin\\Decomposer\\Controllers\\": "vendor/lubusin/laravel-decomposer/src/controllers/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": [{"type": "vcs", "url": "https://github.com/webbingbrasil/filament-maps"}]}