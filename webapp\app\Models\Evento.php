<?php
// webapp\app\Models\Evento.php

namespace App\Models;

use App\Models\User;
use Spatie\Image\Enums\Fit;
use Spatie\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

class Evento extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'eventos';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'nombre',
        'descripcion',
        'url',
        'fecha_inicio',
        'fecha_fin',
        'negocio_id',
    ];

    protected $dates = [
        'fecha_inicio',
        'fecha_fin'
    ];

    /**
     * Get the negocio that owns the evento.
     */
    public function negocio(): BelongsTo
    {
        return $this->belongsTo(Negocio::class);
    }

    public function user(): HasOneThrough
    {
        return $this->hasOneThrough(
            User::class,     // Modelo final
            Negocio::class,  // Modelo intermedio
            'id',            // Clave foránea en Negocio (relacionada con Evento: negocio_id)
            'id',            // Clave primaria en User (relacionada con Negocio: user_id)
            'negocio_id',    // Clave foránea en Evento que referencia a Negocio
            'user_id'        // Clave foránea en Negocio que referencia a User
        );
    }


    // Media Library
    public function registerMediaConversions(?Media $media = null): void
    {
        $this
            ->addMediaConversion('thumb')
            ->fit(Fit::Contain, 200, 200)
            ->nonQueued();
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('imagenes_eventos')->useDisk('eventos');
    }

    // Scope para eventos con fecha de fin posterior a la actual
    public function scopeFuturos($query)
    {
        return $query->where('fecha_fin', '>=', now());
    }
}
