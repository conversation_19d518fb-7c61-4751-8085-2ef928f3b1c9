<?php

namespace Tests\Feature\Api;

use Tests\BaseTest;
use App\Models\User;
use App\Models\Zona;
use App\Models\Evento;
use App\Models\Negocio;
use App\Models\Categoria;
use Spatie\Permission\Models\Role;
use PHPUnit\Framework\Attributes\Group;
use Database\Seeders\RolesPermissionSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;

#[Group('api')]
#[Group('evento')]
class EventoApiTest extends BaseTest
{
    use RefreshDatabase;

    private array $usuarios;

    private Categoria $categoria;
    private Negocio $negocio;

    private string $api_slug = '/api/v1/eventos';

    protected function setUp(): void
    {
        parent::setUp();

        $this->seed(RolesPermissionSeeder::class);

        foreach (Role::all() as $rol) {
            /** @var User */
            $user = User::factory()->create();
            $user->assignRole($rol->name);

            $this->usuarios[$rol->name] = [
                'usuario' => $user,
                'token' => $user->createToken('auth_toke_' . $rol->name)->plainTextToken,
            ];
        }

        $this->categoria = Categoria::factory()->create();
        $this->negocio = Negocio::factory()
            ->conSuscripcion()
            ->conCategorias([$this->categoria->id])
            ->create([
                'user_id' => $this->usuarios['cliente']['usuario']->id,
            ]);
    }

    public function test_el_listado_de_eventos_es_accesible_publicamente(): void
    {
        $eventos = Evento::factory(3)->create([
            'negocio_id' => $this->negocio->id,
        ]);

        $response = $this->getJson("{$this->api_slug}");
        $response->assertStatus(200)
            ->assertJsonCount(3);
    }

    public function test_mostrar_un_evento_es_accesible_publicamente(): void
    {
        $evento = Evento::factory()->create([
            'negocio_id' => $this->negocio->id,
        ]);

        $response = $this->getJson("{$this->api_slug}/{$evento->id}");
        $response
            ->assertStatus(200)
            ->assertJson(['id' => $evento->id]);
    }

    public function test_solo_usuarios_con_permiso_pueden_crear_eventos(): void
    {
        // 🟢 Test positivos
        $test_roles = $this->_getRolesPositivos(['evento.create']);

        foreach ($test_roles as $rol) {
            $this->actingAs($this->usuarios[$rol]['usuario']);

            $data = Evento::factory()->make([
                'negocio_id' => $this->negocio->id,
            ])->toArray();

            $data['fecha_inicio'] = $data['fecha_inicio']->format('Y-m-d H:i:s');
            $data['fecha_fin'] = $data['fecha_fin']->format('Y-m-d H:i:s');

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->postJson("{$this->api_slug}", $data);

            $response->assertStatus(201)
                ->assertJsonStructure([
                    'id',
                    'nombre',
                    'descripcion',
                    'fecha_inicio',
                    'fecha_fin',
                    'created_at',
                    'updated_at'
                ])
                ->assertJson([
                    'nombre' => $data['nombre'],
                    'descripcion' => $data['descripcion'],
                    'fecha_inicio' => $data['fecha_inicio'],
                    'fecha_fin' => $data['fecha_fin'],
                ]);

            $this->assertDatabaseHas('eventos', [
                'nombre' => $data['nombre'],
                'descripcion' => $data['descripcion'],
                'fecha_inicio' => $data['fecha_inicio'],
                'fecha_fin' => $data['fecha_fin'],
                'negocio_id' => $data['negocio_id'],
            ]);
        }

        // 🔴 Test negativos

        $test_roles = $this->_getRolesNegativos(['evento.create']);

        foreach ($test_roles as $rol) {
            $this->actingAs($this->usuarios[$rol]['usuario']);

            $data = Evento::factory()->make([
                'negocio_id' => $this->negocio->id,
            ])->toArray();

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->postJson("{$this->api_slug}", $data);

            $response->assertStatus(403);
        }
    }

    public function test_el_sistema_no_crea_nuevo_evento_con_datos_inválidos(): void
    {
        // Casos de datos inválidos
        $testCases = [
            // Caso 1: Enviar datos vacíos
            [
                'payload' => [],
                'expectedInvalid' => ['nombre', 'fecha_inicio', 'fecha_fin', 'negocio_id']
            ],
            // Negocio no válido
            [
                'payload' => [
                    'negocio_id' => 999999,
                ],
                'expectedInvalid' => ['negocio_id']
            ],
            // Fecha de fin anterior a la de inicio
            [
                'payload' => [
                    'fecha_inicio' => now()->addDays(10),
                    'fecha_fin' => now()->addDays(5),
                ],
                'expectedInvalid' => ['fecha_fin']
            ],
            // Fecha de inicio anterior a la actual
            [
                'payload' => [
                    'fecha_inicio' => now()->subDays(10),
                    'fecha_fin' => now()->addDays(10),
                ],
                'expectedInvalid' => ['fecha_inicio']
            ],
            // Formato de fecha incorrecto
            [
                'payload' => [
                    'fecha_inicio' => 'invalid',
                    'fecha_fin' => 'invalid',
                ],
                'expectedInvalid' => ['fecha_inicio', 'fecha_fin']
            ],
        ];

        $rol = $this->_getRolesPositivos(['evento.create']);

        if (is_array($rol)) {
            $rol = $rol[0] ?? null;
        }

        if (!$rol) {
            return;
        }

        $token = $this->usuarios[$rol]['token'];

        foreach ($testCases as $case) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->postJson("{$this->api_slug}", $case['payload']);
        }

        if (isset($case['expectedForbidden']) && $case['expectedForbidden']) {
            $response->assertForbidden(); // Verificar acceso denegado
        } else {
            $response->assertInvalid($case['expectedInvalid']);
        }
    }

    public function test_solo_usuarios_con_permiso_pueden_editar_eventos(): void
    {
        $evento = Evento::factory()->create([
            'negocio_id' => $this->negocio->id,
        ]);

        // 🟢 Test positivos

        $test_roles = $this->_getRolesPositivos(['evento.update']);
        $test_roles[] = 'cliente';

        foreach ($test_roles as $rol) {
            $this->actingAs($this->usuarios[$rol]['usuario']);

            $evento->refresh();
            $data = Evento::factory()->make([
                'negocio_id' => $this->negocio->id,
            ])->toArray();

            $data['fecha_inicio'] = $data['fecha_inicio']->format('Y-m-d H:i:s');
            $data['fecha_fin'] = $data['fecha_fin']->format('Y-m-d H:i:s');

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->putJson("{$this->api_slug}/{$evento->id}", $data);

            $response->assertStatus(200)
                ->assertJsonStructure([
                    'id',
                    'nombre',
                    'descripcion',
                    'fecha_inicio',
                    'fecha_fin',
                    'created_at',
                    'updated_at'
                ])
                ->assertJson([
                    'nombre' => $data['nombre'],
                    'descripcion' => $data['descripcion'],
                    'fecha_inicio' => $data['fecha_inicio'],
                    'fecha_fin' => $data['fecha_fin'],
                ]);

            $this->assertDatabaseHas('eventos', [
                'nombre' => $data['nombre'],
                'descripcion' => $data['descripcion'],
                'fecha_inicio' => $data['fecha_inicio'],
                'fecha_fin' => $data['fecha_fin'],
                'negocio_id' => $data['negocio_id'],
            ]);
        }

        // 🔴 Test negativos

        $test_roles = $this->_getRolesNegativos(['evento.update']);

        // Cambiamos el usuario cliente para comprobar que no puede editar un evento de otro negocio

        $auxUser = User::factory()->create();
        $auxUser->assignRole('cliente');

        $this->usuarios['cliente']['usuario'] = $auxUser;
        $this->usuarios['cliente']['token'] = $auxUser->createToken('auth_token_cliente')->plainTextToken;


        foreach ($test_roles as $rol) {
            $this->actingAs($this->usuarios[$rol]['usuario']);

            $data = Evento::factory()->make([
                'negocio_id' => $this->negocio->id,
            ])->toArray();

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->putJson("{$this->api_slug}/{$evento->id}", $data);

            $response->assertStatus(403);
        }
    }

    public function test_solo_usuarios_con_permiso_pueden_borrar_eventos(): void
    {
        // 🟢 Test positivos

        $test_roles = $this->_getRolesPositivos(['evento.delete']);
        $test_roles[] = 'cliente';

        foreach ($test_roles as $rol) {
            $this->actingAs($this->usuarios[$rol]['usuario']);

            $evento = Evento::factory()->create([
                'negocio_id' => $this->negocio->id,
            ]);

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->deleteJson("{$this->api_slug}/{$evento->id}");

            $response->assertNoContent();

            // Verificar que el evento no existe en la base de datos
            $this->assertDatabaseMissing(Evento::class, [
                'id' => $evento->id,
            ]);
        }

        // 🔴 Test negativos

        $test_roles = $this->_getRolesNegativos(['evento.delete']);

        // Cambiamos el usuario cliente para comprobar que no puede borrar un evento de otro negocio
        $auxUser = User::factory()->create();
        $auxUser->assignRole('cliente');

        $this->usuarios['cliente']['usuario'] = $auxUser;
        $this->usuarios['cliente']['token'] = $auxUser->createToken('auth_token_cliente')->plainTextToken;

        $evento = Evento::factory()->create([
            'negocio_id' => $this->negocio->id,
        ]);

        foreach ($test_roles as $rol) {
            $this->actingAs($this->usuarios[$rol]['usuario']);

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->deleteJson("{$this->api_slug}/{$evento->id}");

            $this->assertTrue($response->status() === 403 || $response->status() === 404);

            $this->assertDatabaseCount(Evento::class, 1);
        }
    }
}
