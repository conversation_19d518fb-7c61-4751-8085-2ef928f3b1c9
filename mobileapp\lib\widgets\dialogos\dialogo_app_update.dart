import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class DialogoAppUpdate extends StatefulWidget {
  final bool forceUpdate;
  final String? updateUrl;

  const DialogoAppUpdate({
    super.key,
    required this.forceUpdate,
    required this.updateUrl,
  });

  @override
  State<DialogoAppUpdate> createState() => _DialogoAppUpdateState();
}

class _DialogoAppUpdateState extends State<DialogoAppUpdate> {
  Future<void> _launchURL() async {
    // Verificar si updateUrl es nula o está vacía
    if (widget.updateUrl == null || widget.updateUrl!.isEmpty) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No se encontró URL de actualización.')),
      );
      return;
    }

    final Uri uri = Uri.parse(widget.updateUrl!);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('No se pudo abrir la URL de actualización.')),
      );
    }
  }

  Future<void> _onActualizarPressed() async {
    await _launchURL();
    if (!mounted) return;
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Actualización disponible'),
      content: Text(
        widget.forceUpdate
            ? 'Esta actualización es obligatoria. Por favor, actualiza la aplicación para continuar usando la app.'
            : 'Hay una nueva actualización disponible. ¿Deseas actualizar?',
      ),
      actions: [
        if (!widget.forceUpdate)
          TextButton(
            onPressed: () {
              if (mounted) Navigator.of(context).pop();
            },
            child: const Text('Más tarde'),
          ),
        TextButton(
          onPressed: _onActualizarPressed,
          child: const Text('Actualizar'),
        ),
      ],
    );
  }
}
