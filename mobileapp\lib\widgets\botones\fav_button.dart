// lib/widgets/botones/fav_button.dart

import 'package:flutter/material.dart';
import 'package:mia/models/negocio.dart';
import 'package:provider/provider.dart';
import 'package:mia/config/colors.dart';
import 'package:mia/providers/favorites_provider.dart';

class FavButton extends StatefulWidget {
  final Negocio negocio;
  final double size;
  final IconData activeIcon;
  final IconData inactiveIcon;
  final Duration animationDuration;
  final FavoritesProvider? favoritesProvider;
  final EdgeInsetsGeometry? padding; // Añadido parámetro de padding

  const FavButton({
    super.key,
    required this.negocio,
    this.size = 32.0,
    this.activeIcon = Icons.favorite,
    this.inactiveIcon = Icons.favorite_border,
    this.animationDuration = const Duration(milliseconds: 300),
    this.favoritesProvider,
    this.padding, // Permite personalizar el padding o dejarlo nulo para eliminarlo
  });

  @override
  State<FavButton> createState() => _FavButtonState();
}

class _FavButtonState extends State<FavButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late FavoritesProvider _favoritesProvider;
  bool _isUsingExternalProvider = false;
  bool _isFavorite = false;

  @override
  void initState() {
    super.initState();

    // Inicializar la animación
    _controller = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
        CurvedAnimation(parent: _controller, curve: Curves.elasticInOut));

    // Verificar si se está usando un proveedor de favoritos externo
    _isUsingExternalProvider = widget.favoritesProvider != null;
    if (_isUsingExternalProvider) {
      _favoritesProvider = widget.favoritesProvider!;
      _isFavorite = _favoritesProvider.isFavorite(widget.negocio.id);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Only fetch from context if we're not using an external provider
    if (!_isUsingExternalProvider) {
      _favoritesProvider = Provider.of<FavoritesProvider>(context);
    }
  }

  @override
  void didUpdateWidget(FavButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Update the provider reference if it changed
    if (widget.favoritesProvider != oldWidget.favoritesProvider) {
      _isUsingExternalProvider = widget.favoritesProvider != null;
      if (_isUsingExternalProvider) {
        _favoritesProvider = widget.favoritesProvider!;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // If using external provider, don't watch context changes
    final FavoritesProvider favProv = _isUsingExternalProvider
        ? _favoritesProvider
        : context.watch<FavoritesProvider>();

    final isFav = favProv.isFavorite(widget.negocio.id);

    // Si cambió el estado, anima
    if (isFav != _isFavorite) {
      _isFavorite = isFav;
      _controller.reset();
      _controller.forward();
    }

    // Usar GestureDetector en lugar de IconButton para eliminar el padding por defecto
    return GestureDetector(
      onTap: () => favProv.toggleFavorite(widget.negocio.id),
      child: Padding(
        padding: widget.padding ??
            EdgeInsets.zero, // Usa padding personalizado o cero
        child: AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Icon(
                isFav ? widget.activeIcon : widget.inactiveIcon,
                key: ValueKey<bool>(isFav),
                color: isFav
                    ? AppColors.current.errorColor
                    : AppColors.current.textColor,
                size: widget.size,
              ),
            );
          },
        ),
      ),
    );
  }
}
