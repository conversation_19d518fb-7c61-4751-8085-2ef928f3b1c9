// test/models/evento_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mia/models/evento.dart';
import '../../helpers/base_test_helper.dart';
import 'utils/model_test_utils.dart';
import 'package:intl/date_symbol_data_local.dart';

void main() async {
  TestWidgetsFlutterBinding.ensureInitialized();
  await BaseTest.initialize();

  group('Evento Model Tests', () {
    ModelTestUtils.runCommonModelTests<Evento>(
      validFixturePaths: [
        '../../fixture/json/evento/valid_evento_completo.json',
        '../../fixture/json/evento/valid_evento_minimo.json',
        '../../fixture/json/evento/valid_evento_sin_imagenes.json',
      ],
      invalidFixturePaths: [
        '../../fixture/json/evento/invalid_evento_missing_id.json',
        '../../fixture/json/evento/invalid_evento_missing_nombre.json',
        '../../fixture/json/evento/invalid_evento_missing_fecha_inicio.json',
        '../../fixture/json/evento/invalid_evento_missing_fecha_fin.json',
        '../../fixture/json/evento/invalid_evento_missing_negocio_id.json',
        '../../fixture/json/evento/invalid_evento_fechas_incorrectas.json',
        '../../fixture/json/evento/invalid_evento_imagenes_incorrectas.json',
      ],
      fromJson: Evento.fromJson,
      toJson: (evento) => evento.toJson(),
      getExpectedValues: (fixturePath) => _getExpectedValues(fixturePath),
      customModel: {
        'id': 99,
        'nombre': 'Evento de Prueba',
        'descripcion': 'Descripción de prueba',
        'url': 'https://test-url.com',
        'fecha_inicio': '2025-04-10T10:00:00',
        'fecha_fin': '2025-04-10T18:00:00',
        'negocio_id': 42,
      },
      requiredFields: [
        'id',
        'nombre',
        'fecha_inicio',
        'fecha_fin',
        'negocio_id'
      ],
    );

    _runSpecificTests();
  });
}

void _runSpecificTests() {
  group('Funciones específicas de Evento:', () {
    setUpAll(() async {
      await initializeDateFormatting('es_ES', null);
    });

    test('isOngoing retorna true para eventos en curso', () {
      final ahora = DateTime.now();
      final inicio = ahora.subtract(const Duration(days: 1));
      final fin = ahora.add(const Duration(days: 1));

      final evento = Evento(
        id: 1,
        nombre: 'Evento en curso',
        descripcion: 'Test',
        url: '',
        fechaInicio: inicio.toIso8601String(),
        fechaFin: fin.toIso8601String(),
        negocioId: 1,
      );

      expect(evento.isOngoing(), isTrue);
    });

    group('Pruebas unitarias getHorarioString:', () {
      test('getHorarioString para evento que inicia y termina el mismo día',
          () {
        // Caso 1: Sáb 14 Jun 12:30 - 23:00
        final start = DateTime.parse('2025-06-14T12:30:00');
        final end = DateTime.parse('2025-06-14T23:00:00');
        final evento = Evento(
          id: 2,
          nombre: 'Evento mismo día',
          descripcion: 'Test',
          url: '',
          fechaInicio: start.toIso8601String(),
          fechaFin: end.toIso8601String(),
          negocioId: 1,
        );

        final horario = evento.getHorarioString();
        expect(horario, equals('Sáb 14 Jun 12:30 - 23:00'));
      });

      test(
          'getHorarioString para evento que termina el día siguiente antes de las 07:00',
          () {
        // Caso 2: Sáb 14 Jun 20:00 - 06:30 (Dom)
        final start = DateTime.parse('2025-06-14T20:00:00');
        final end = DateTime.parse('2025-06-15T06:30:00');
        final evento = Evento(
          id: 3,
          nombre: 'Evento madrugada',
          descripcion: 'Test',
          url: '',
          fechaInicio: start.toIso8601String(),
          fechaFin: end.toIso8601String(),
          negocioId: 1,
        );

        final horario = evento.getHorarioString();
        expect(horario, equals('Sáb 14 Jun 20:00 - 06:30 (Dom)'));
      });

      test('getHorarioString para evento multi-día', () {
        // Caso 3: Sáb 14 Jun 12:30 - Lun 16 Jun 20:00
        final start = DateTime.parse('2025-06-14T12:30:00');
        final end = DateTime.parse('2025-06-16T20:00:00');
        final evento = Evento(
          id: 4,
          nombre: 'Evento multi-día',
          descripcion: 'Test',
          url: '',
          fechaInicio: start.toIso8601String(),
          fechaFin: end.toIso8601String(),
          negocioId: 1,
        );

        final horario = evento.getHorarioString();
        expect(horario, equals('Sáb 14 Jun 12:30 - Lun 16 Jun 20:00'));
      });
    });
  });
}

Map<String, dynamic> _getExpectedValues(String fixturePath) {
  switch (fixturePath.split('/').last) {
    case 'valid_evento_completo.json':
      return {
        'id': 1,
        'nombre': 'Festival de Verano',
        'descripcion': 'Gran festival de música en la playa',
        'url': 'https://miafestival.com',
        'fechaInicio': '2025-07-15T18:00:00',
        'fechaFin': '2025-07-18T23:00:00',
        'negocioId': 5,
      };
    case 'valid_evento_minimo.json':
      return {
        'id': 2,
        'nombre': 'Exposición de Arte',
        'descripcion': '',
        'url': '',
        'fechaInicio': '2025-09-10T10:00:00',
        'fechaFin': '2025-09-20T19:00:00',
        'negocioId': 8,
      };
    case 'valid_evento_sin_imagenes.json':
      return {
        'id': 3,
        'nombre': 'Concierto Acústico',
        'descripcion': 'Concierto íntimo en el café central',
        'url': 'https://cafecentralconciertos.com/acustico',
        'fechaInicio': '2025-05-25T20:30:00',
        'fechaFin': '2025-05-25T23:00:00',
        'negocioId': 12,
      };

    default:
      throw Exception('Fixture de Evento no definido: $fixturePath');
  }
}
