<?php

namespace App\Filament\Resources\LocalidadResource\Pages;

use App\Models\Zona;
use Filament\Actions;
use App\Models\Negocio;
use App\Models\Localidad;
use App\Services\CacheService;
use Filament\Tables\Actions\BulkAction;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use App\Filament\Resources\LocalidadResource;

class ListLocalidades extends ListRecords
{
    protected static string $resource = LocalidadResource::class;

    protected CacheService $cacheService;

    public function boot(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    protected function getTableBulkActions(): array
    {
        return [
            BulkAction::make('delete')
                ->label('Eliminar')
                ->action(function ($records) {
                    $records->each(function ($record) {
                        $record->delete();
                    });

                    Notification::make()
                        ->title('Localidades eliminadas')
                        ->success()
                        ->send();

                    $this->cacheService->invalidateCache(Localidad::class, 'localidades_all');
                    $this->cacheService->invalidateCache(Zona::class, 'zonas_all');
                    $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');
                })
                ->requiresConfirmation(),
        ];
    }
}
