// test/models/media_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mia/models/media.dart';
import '../../helpers/base_test_helper.dart';
import 'utils/model_test_utils.dart';

void main() async {
  TestWidgetsFlutterBinding.ensureInitialized();
  await BaseTest.initialize();

  group('Media Model Tests', () {
    ModelTestUtils.runCommonModelTests<Media>(
      validFixturePaths: [
        '../../fixture/json/media/valid_media_complete.json',
        '../../fixture/json/media/valid_media_minimal.json',
        '../../fixture/json/media/valid_media_different_domains.json',
      ],
      invalidFixturePaths: [
        '../../fixture/json/media/invalid_media_missing_id.json',
        '../../fixture/json/media/invalid_media_missing_name.json',
        '../../fixture/json/media/invalid_media_missing_url.json',
        '../../fixture/json/media/invalid_media_missing_thumb.json',
        '../../fixture/json/media/invalid_media_wrong_id_type.json',
        '../../fixture/json/media/invalid_media_null_values.json',
      ],
      fromJson: Media.fromJson,
      toJson: (media) => media.toJson(),
      getExpectedValues: (fixturePath) => _getExpectedValues(fixturePath),
      customModel: {
        'id': 99,
        'name': 'Media de Prueba',
        'url': 'https://********/mia/storage/test.jpg',
        'thumb': 'https://********/mia/storage/thumbnails/test_thumb.jpg',
      },
      requiredFields: ['id', 'name', 'url', 'thumb'],
    );
  });
}

Map<String, dynamic> _getExpectedValues(String fixturePath) {
  switch (fixturePath.split('/').last) {
    case 'valid_media_complete.json':
      return {
        'id': 1,
        'nombre': 'Imagen Principal',
        'url': 'https://********/mia/storage/imagenes/principal.jpg',
        'thumb':
            'https://********/mia/storage/imagenes/thumbnails/principal_thumb.jpg',
      };
    case 'valid_media_minimal.json':
      return {
        'id': 2,
        'nombre': 'Logo',
        'url': 'https://********/mia/storage/imagenes/logo.png',
        'thumb':
            'https://********/mia/storage/imagenes/thumbnails/logo_thumb.png',
      };
    case 'valid_media_different_domains.json':
      return {
        'id': 3,
        'nombre': 'Imagen Externa',
        'url': 'https://********/mia/storage/external/image.jpg',
        'thumb':
            'https://********/mia/storage/external/thumbnails/image_thumb.jpg',
      };
    default:
      throw Exception('Fixture de Media no definido: $fixturePath');
  }
}
