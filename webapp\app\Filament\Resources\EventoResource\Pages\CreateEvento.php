<?php

namespace App\Filament\Resources\EventoResource\Pages;

use Carbon\Carbon;
use App\Models\User;
use Filament\Actions;
use App\Models\Evento;
use App\Services\CacheService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;
use App\Http\Requests\StoreEventoRequest;
use Illuminate\Support\Facades\Validator;
use App\Filament\Resources\EventoResource;
use App\Models\Negocio;
use Filament\Resources\Pages\CreateRecord;

class CreateEvento extends CreateRecord
{
    protected static string $resource = EventoResource::class;

    protected CacheService $cacheService;

    public function boot(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('edit', ['record' => $this->record]);
    }

    protected function handleRecordCreation(array $data): Model
    {
        /** @var User */
        $user = Auth::user();

        if (!$user->can('evento.create')) {
            abort(403, 'No tienes permiso para crear un evento.');
        }

        $request = new StoreEventoRequest();
        $validator = Validator::make($data, $request->rules(), $request->messages());

        try {
            $validatedData = $validator->validate();

            $model = static::getModel()::create($validatedData);

            $this->cacheService->invalidateCache(Evento::class, 'eventos_futuros');
            $this->cacheService->invalidateCache(Evento::class, 'eventos_all');

            $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');

            return $model;
        } catch (\Illuminate\Validation\ValidationException $e) {
            Notification::make()
                ->title('Error al crear el evento.')
                // Mostrar en el body una lista de los errores de validación a partir de StoreEventoRequest::messages()
                ->body(function () use ($e) {
                    $errors = $e->validator->errors();
                    $errorMessages = [];
                    foreach ($errors->all() as $error) {
                        $errorMessages[] = $error;
                    }
                    return implode('<br>', $errorMessages);
                })
                ->danger()
                ->send();
            throw $e;
        }
    }
}
