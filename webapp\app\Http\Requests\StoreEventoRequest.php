<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreEventoRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('create', \App\Models\Evento::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'nombre' => 'required|string|max:255',
            'descripcion' => 'nullable|string',
            'url' => 'nullable|string',
            'fecha_inicio' => ['required', Rule::date()->afterOrEqual(now()->format('Y-m-d H:i'))],
            'fecha_fin' => ['required', Rule::date()->afterOrEqual(now()->format('Y-m-d H:i')), 'after:fecha_inicio'],
            'negocio_id' => 'required|exists:negocios,id',
        ];
    }

    public function messages(): array
    {
        return [
            'nombre.required' => 'El nombre del evento es obligatorio.',
            'nombre.max' => 'El nombre del evento es demasiado largo.',
            'descripcion.max' => 'La descripción del evento es demasiado larga.',
            'fecha_inicio.required' => 'La fecha de inicio del evento es obligatoria.',
            'fecha_inicio.date' => 'La fecha de inicio del evento debe ser una fecha válida (YYYY-MM-DD HH:MM:SS).',
            'fecha_fin.required' => 'La fecha de fin del evento es obligatoria.',
            'fecha_fin.date' => 'La fecha de fin del evento debe ser una fecha válida (YYYY-MM-DD HH:MM:SS).',
            'fecha_fin.after' => 'La fecha de fin del evento debe ser posterior a la fecha de inicio.',
            'negocio_id.exists' => 'El negocio seleccionado no es válido.',
        ];
    }
}
