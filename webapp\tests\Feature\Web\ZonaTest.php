<?php

namespace Tests\Feature\Web;

use Tests\BaseTest;
use App\Models\User;
use App\Models\Zona;
use Livewire\Livewire;
use App\Models\Localidad;
use Spatie\Permission\Models\Role;
use PHPUnit\Framework\Attributes\Group;
use App\Filament\Resources\ZonaResource;
use Filament\Tables\Actions\DeleteAction;
use Database\Seeders\RolesPermissionSeeder;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Filament\Resources\ZonaResource\Pages\EditZona;
use App\Filament\Resources\ZonaResource\Pages\ListZonas;
use App\Filament\Resources\ZonaResource\Pages\CreateZona;

#[Group('zona')]
#[Group('webapp')]
#[Group('zona_webapp')]
class ZonaTest extends BaseTest
{
    use RefreshDatabase;

    private array $usuarios;

    // Configuración inicial para cada prueba
    protected function setUp(): void
    {
        parent::setUp();

        $this->seed(RolesPermissionSeeder::class);

        foreach (Role::all() as $rol) {
            $user = User::factory()->create();
            $user->assignRole($rol->name);

            $this->usuarios[$rol->name] = [
                'usuario' => $user,
            ];
        }
    }


    public function test_el_sistema_muestra_las_paginas_de_zona_correctamente_segun_el_rol_del_usuario(): void
    {

        // 🟢 Test positivos

        $zona = Zona::factory()->create();

        $test_roles = $this->_getRolesPositivos(['zona.list', 'zona.create', 'zona.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $this->get(ZonaResource::getUrl('index'))->assertSuccessful();
            $this->get(ZonaResource::getUrl('create'))->assertSuccessful();
            $this->get(ZonaResource::getUrl('edit', ['record' => $zona->id]))->assertSuccessful();
        }

        // 🔴 Test negativos

        $test_roles = $this->_getRolesNegativos(['zona.list', 'zona.create', 'zona.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $this->get(ZonaResource::getUrl('index'))->assertForbidden();
            $this->get(ZonaResource::getUrl('create'))->assertForbidden();
            $this->get(ZonaResource::getUrl('edit', ['record' => $zona->id]))->assertForbidden();
        }
    }


    public function test_el_sistema_crea_una_nueva_zona_solo_con_datos_validos(): void
    {

        // 🟢 Test positivos

        $test_roles = $this->_getRolesPositivos(['zona.create']);

        $i = 0;
        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $test = Livewire::test(CreateZona::class);

            $newData = Zona::factory()->make()->toArray();

            $this->assertDatabaseCount(Zona::class, $i++);

            $test
                ->fillForm([
                    'nombre' => $newData['nombre'],
                    'descripcion' => $newData['descripcion'],
                    'coordenadas' => $newData['coordenadas'],
                    'localidad_id' => $newData['localidad_id'],
                ])
                ->call('create')
                ->assertHasNoFormErrors()
            ;

            $this->assertDatabaseCount(Zona::class, $i);
            $this->assertDatabaseHas(Zona::class, [
                'nombre' => $newData['nombre'],
                'descripcion' => $newData['descripcion'],
                'coordenadas' => json_encode($newData['coordenadas']),
                'localidad_id' => $newData['localidad_id'],
            ]);
        }

        // 🔴 Test negativos

        $test_roles = $this->_getRolesPositivos(['zona.create']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $test_cases = [
                // caso 1: campos vacíos
                [
                    'payload' => [],
                    'expectedInvalid' => ['nombre', 'localidad_id']
                ],
            ];

            $test = Livewire::test(CreateZona::class);

            foreach ($test_cases as $case) {
                $test->fillForm($case['payload'])
                    ->call('create')
                    ->assertHasFormErrors($case['expectedInvalid']);
            }
        }
    }


    public function test_el_sistema_edita_una_zona_solo_con_datos_validos(): void
    {

        // 🟢 Test positivos

        $zona = Zona::factory()->create();

        $test_roles = $this->_getRolesPositivos(['zona.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $zona->refresh();
            $newData = Zona::factory()->make()->toArray();
            $localidad = Localidad::factory()->create();

            $test = Livewire::test(EditZona::class, ['record' => $zona->id]);

            $test
                ->assertFormSet([
                    'nombre' => $zona->nombre,
                    'descripcion' => $zona->descripcion,
                    'coordenadas' => $zona->coordenadas,
                    'localidad_id' => $zona->localidad_id,
                ])
                ->fillForm([
                    'nombre' => $newData['nombre'],
                    'descripcion' => $newData['descripcion'],
                    'coordenadas' => $newData['coordenadas'],
                    'localidad_id' => $localidad->id,
                ])
                ->call('save')
                ->assertHasNoFormErrors();

            $this->assertDatabaseCount(Zona::class, 1);
            $this->assertDatabaseHas(Zona::class, [
                'nombre' => $newData['nombre'],
                'descripcion' => $newData['descripcion'],
                'coordenadas' => json_encode($newData['coordenadas']),
                'localidad_id' => $localidad->id,
            ]);
        }

        // 🔴 Test negativos

        $test_roles = $this->_getRolesPositivos(['zona.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $zona->refresh();

            $test_cases = [
                // caso 1: campos vacíos
                [
                    'payload' => [],
                    'expectedInvalid' => ['nombre', 'localidad_id']
                ],
            ];

            $test = Livewire::test(EditZona::class, ['record' => $zona->id]);

            foreach ($test_cases as $case) {
                $test
                    ->assertFormSet([
                        'nombre' => $zona->nombre,
                        'descripcion' => $zona->descripcion,
                        'coordenadas' => $zona->coordenadas,
                        'localidad_id' => $zona->localidad_id,
                    ])
                    ->fillForm($case['payload'])
                    ->call('save')
                    ->assertHasFormErrors($case['expectedInvalid']);
            }

            $this->assertDatabaseCount(Zona::class, 1);
            $this->assertDatabaseHas(Zona::class, [
                'nombre' => $zona->nombre,
                'descripcion' => $zona->descripcion,
                'coordenadas' => json_encode($zona->coordenadas),
                'localidad_id' => $zona->localidad_id,
            ]);
        }
    }


    public function test_el_sistema_elimina_correctamente_una_zona(): void
    {

        // 🟢 Test positivos

        $test_roles = $this->_getRolesPositivos(['zona.delete']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $zona = Zona::factory()->create();

            $test = Livewire::test(ListZonas::class);

            $test
                ->assertTableActionVisible(DeleteAction::class, $zona->id)
                ->callTableAction(DeleteAction::class, $zona->id);

            $this->assertDatabaseCount(Zona::class, 0);
        }

        // 🔴 Test negativos

        $test_roles = [];

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            // $test = Livewire::test(ListAplicaciones::class);
        }
    }

    #[Group('test')]
    public function _test_el_sistema_____(): void
    {

        // 🟢 Test positivos

        // $categoria = Zona::factory()->create();

        $test_roles = [];

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            // $test = Livewire::test(ListAplicaciones::class);
        }

        // 🔴 Test negativos

        $test_roles = [];

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            // $test = Livewire::test(ListAplicaciones::class);
        }
    }
}
