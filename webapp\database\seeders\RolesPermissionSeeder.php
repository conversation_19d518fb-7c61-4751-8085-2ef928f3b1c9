<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class RolesPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Verifica y crea los roles si no existen
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $ownerRole = Role::firstOrCreate(['name' => 'owner']);
        $clienteRole = Role::firstOrCreate(['name' => 'cliente']);
        $usuarioRole = Role::firstOrCreate(['name' => 'usuario']);

        // Creación de permisos CRUD para modelo

        // Localidad
        $localidadListPermission = Permission::firstOrCreate(['name' => 'localidad.list']);
        $localidadReadPermission = Permission::firstOrCreate(['name' => 'localidad.read']);
        $localidadCreatePermission = Permission::firstOrCreate(['name' => 'localidad.create']);
        $localidadUpdatePermission = Permission::firstOrCreate(['name' => 'localidad.update']);
        $localidadDeletePermission = Permission::firstOrCreate(['name' => 'localidad.delete']);
        $localidadForceDeletePermission = Permission::firstOrCreate(['name' => 'localidad.force-delete']);
        $localidadRestorePermission = Permission::firstOrCreate(['name' => 'localidad.restore']);

        // Zona
        $zonaListPermission = Permission::firstOrCreate(['name' => 'zona.list']);
        $zonaReadPermission = Permission::firstOrCreate(['name' => 'zona.read']);
        $zonaCreatePermission = Permission::firstOrCreate(['name' => 'zona.create']);
        $zonaUpdatePermission = Permission::firstOrCreate(['name' => 'zona.update']);
        $zonaDeletePermission = Permission::firstOrCreate(['name' => 'zona.delete']);
        $zonaForceDeletePermission = Permission::firstOrCreate(['name' => 'zona.force-delete']);
        $zonaRestorePermission = Permission::firstOrCreate(['name' => 'zona.restore']);

        // Categoria
        $categoriaListPermission = Permission::firstOrCreate(['name' => 'categoria.list']);
        $categoriaReadPermission = Permission::firstOrCreate(['name' => 'categoria.read']);
        $categoriaCreatePermission = Permission::firstOrCreate(['name' => 'categoria.create']);
        $categoriaUpdatePermission = Permission::firstOrCreate(['name' => 'categoria.update']);
        $categoriaDeletePermission = Permission::firstOrCreate(['name' => 'categoria.delete']);
        $categoriaForceDeletePermission = Permission::firstOrCreate(['name' => 'categoria.force-delete']);
        $categoriaRestorePermission = Permission::firstOrCreate(['name' => 'categoria.restore']);

        // Negocio
        $negocioListPermission = Permission::firstOrCreate(['name' => 'negocio.list']);
        $negocioReadPermission = Permission::firstOrCreate(['name' => 'negocio.read']);
        $negocioCreatePermission = Permission::firstOrCreate(['name' => 'negocio.create']);
        $negocioUpdatePermission = Permission::firstOrCreate(['name' => 'negocio.update']);
        $negocioDeletePermission = Permission::firstOrCreate(['name' => 'negocio.delete']);
        $negocioForceDeletePermission = Permission::firstOrCreate(['name' => 'negocio.force-delete']);
        $negocioRestorePermission = Permission::firstOrCreate(['name' => 'negocio.restore']);

        // Evento
        $eventoListPermission = Permission::firstOrCreate(['name' => 'evento.list']);
        $eventoReadPermission = Permission::firstOrCreate(['name' => 'evento.read']);
        $eventoCreatePermission = Permission::firstOrCreate(['name' => 'evento.create']);
        $eventoUpdatePermission = Permission::firstOrCreate(['name' => 'evento.update']);
        $eventoDeletePermission = Permission::firstOrCreate(['name' => 'evento.delete']);
        $eventoForceDeletePermission = Permission::firstOrCreate(['name' => 'evento.force-delete']);
        $eventoRestorePermission = Permission::firstOrCreate(['name' => 'evento.restore']);

        // Usuarios
        $usersListPermission = Permission::firstOrCreate(['name' => 'user.list']);
        $usersReadPermission = Permission::firstOrCreate(['name' => 'user.read']);
        $usersCreatePermission = Permission::firstOrCreate(['name' => 'user.create']);
        $usersUpdatePermission = Permission::firstOrCreate(['name' => 'user.update']);
        $usersDeletePermission = Permission::firstOrCreate(['name' => 'user.delete']);
        $usersForceDeletePermission = Permission::firstOrCreate(['name' => 'user.force-delete']);
        $usersRestorePermission = Permission::firstOrCreate(['name' => 'user.restore']);
        $userAdminUsersPermission = Permission::firstOrCreate(['name' => 'user.admin-users']);


        // Permisos de sistema
        $systemAccessPanelPermission = Permission::firstOrCreate(['name' => 'system.access-panel']);
        $systemSettingsPermission = Permission::firstOrCreate(['name' => 'system.settings']);
        $systemAdminPermission = Permission::firstOrCreate(['name' => 'system.admin']);
        $systemUsersPermission = Permission::firstOrCreate(['name' => 'system.users']);
        $systemRolesPermission = Permission::firstOrCreate(['name' => 'system.roles']);
        $systemPermissionsPermission = Permission::firstOrCreate(['name' => 'system.permissions']);
        $systemAdminDashboardPermission = Permission::firstOrCreate(['name' => 'system.admin-dashboard']);


        // Asignación de permisos a roles
        $adminRole = Role::findByName('admin');
        $adminRole->givePermissionTo(Permission::all());

        $ownerRole = Role::findByName('owner');
        $ownerRole->givePermissionTo([
            // Categoria
            $categoriaListPermission,
            $categoriaReadPermission,
            $categoriaCreatePermission,
            $categoriaUpdatePermission,
            $categoriaDeletePermission,
            $categoriaForceDeletePermission,
            $categoriaRestorePermission,

            // Zona
            $zonaListPermission,
            $zonaReadPermission,
            $zonaCreatePermission,
            $zonaUpdatePermission,
            $zonaDeletePermission,
            $zonaForceDeletePermission,
            $zonaRestorePermission,

            // Localidad
            $localidadListPermission,
            $localidadReadPermission,
            $localidadCreatePermission,
            $localidadUpdatePermission,
            $localidadDeletePermission,
            $localidadForceDeletePermission,
            $localidadRestorePermission,

            // Negocio
            $negocioListPermission,
            $negocioReadPermission,
            $negocioCreatePermission,
            $negocioUpdatePermission,
            $negocioDeletePermission,
            $negocioForceDeletePermission,
            $negocioRestorePermission,

            // Evento
            $eventoListPermission,
            $eventoReadPermission,
            $eventoCreatePermission,
            $eventoUpdatePermission,
            $eventoDeletePermission,
            $eventoForceDeletePermission,
            $eventoRestorePermission,

            // Usuarios
            $usersListPermission,
            $usersReadPermission,
            $usersCreatePermission,
            $usersUpdatePermission,
            $usersDeletePermission,
            $usersForceDeletePermission,
            $usersRestorePermission,

            // Permisos de sistema
            $systemAccessPanelPermission,
            $systemAdminDashboardPermission,
        ]);

        $clienteRole = Role::findByName('cliente');
        $clienteRole->givePermissionTo([
            // Negocio
            $negocioListPermission,
            $negocioReadPermission,

            // Evento
            $eventoListPermission,
            $eventoReadPermission,
            $eventoCreatePermission,

            // Permisos de sistema
            $systemAccessPanelPermission,
        ]);

        $usuarioRole = Role::findByName('usuario');
        $usuarioRole->givePermissionTo([]);
    }
}
