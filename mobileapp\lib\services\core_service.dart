// lib/services/core_service.dart

import 'dart:io';
import 'dart:math';
import 'package:intl/intl.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:latlong2/latlong.dart';
import 'package:mia/config/colors.dart';
import 'package:mia/config/environment.dart';
import 'package:mia/config/map_schemes.dart';
import 'package:mia/models/negocio.dart';
import 'package:mia/providers/settings_provider.dart';
import 'package:mia/widgets/tag.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

class CoreService {
  /// Muestra un mensaje de error en la parte inferior de la pantalla.
  ///
  /// @param mensaje El mensaje de error a mostrar.
  ///
  static void muestraError(String mensaje) {
    Fluttertoast.showToast(
      msg: mensaje,
      toastLength: Toast.LENGTH_LONG,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: Colors.red,
      textColor: Colors.white,
      fontSize: 16.0,
    );
  }

  /// Construye una lista de etiquetas (tags) basada en una lista de cadenas.
  ///
  /// @param tags Lista de cadenas que representan las etiquetas.
  /// @param backgroundColorDefault Color de fondo predeterminado para las etiquetas.
  /// @param textColorDefault Color del texto predeterminado para las etiquetas.
  /// @param appStyle Estilo de la fuente para las etiquetas.
  /// @return Un widget [Wrap] que contiene las etiquetas generadas.
  ///
  static Wrap buildTagList({
    List<String>? tags,
    Color? backgroundColorDefault,
    Color? backgroundColorActive,
    Color? textColorDefault,
    Color? textColorActive,
    Color? borderColorDefault,
    Color? borderColorActive,
    double? borderWidth,
    String? appStyle,
  }) {
    return Wrap(
      spacing: 1.0,
      runSpacing: 2.0,
      children: List.generate(
          tags?.length ?? 0,
          (index) => Tag(
                label: tags?[index] ?? '',
                description: '',
                backgroundColorDefault:
                    backgroundColorDefault ?? AppColors.current.textColor,
                backgroundColorActive: backgroundColorActive,
                textColorDefault:
                    textColorDefault ?? AppColors.current.tertiaryColor,
                textColorActive: textColorActive,
                borderColorDefault: borderColorDefault,
                borderColorActive: borderColorActive,
                borderWidth: borderWidth,
                appStyle: appStyle ?? 's',
              )),
    );
  }

  /// Ubicación
  /// Convierte la ubicación del [Negocio] en lat/long, o fallback si no existe.
  static LatLng getLatLng(Negocio negocio) {
    if (negocio.ubicacion == null) {
      return LatLng(36.9990019, -6.5478919);
    }
    final lat = double.parse(negocio.ubicacion!['latitud'] ?? '36.9990019');
    final lng = double.parse(negocio.ubicacion!['longitud'] ?? '-6.5478919');
    return LatLng(lat, lng);
  }

  static double calcularDistanciaEnMetros(
      double lat1, double lon1, double lat2, double lon2) {
    const double radioTierra = 6371000; // Radio de la Tierra en metros

    // Convertir grados a radianes
    double lat1Rad = lat1 * (pi / 180);
    double lon1Rad = lon1 * (pi / 180);
    double lat2Rad = lat2 * (pi / 180);
    double lon2Rad = lon2 * (pi / 180);

    // Diferencia de latitudes y longitudes
    double dLat = lat2Rad - lat1Rad;
    double dLon = lon2Rad - lon1Rad;

    // Fórmula de Haversine
    double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(lat1Rad) * cos(lat2Rad) * sin(dLon / 2) * sin(dLon / 2);
    double c = 2 * atan2(sqrt(a), sqrt(1 - a));

    // Distancia en metros
    return radioTierra * c;
  }

  /// Fechas
  static String datetimeToLocale(String datetime,
      {String format = 'dd/MM/yyyy HH:mm', String locale = 'es_ES'}) {
    DateTime parsedDate = DateTime.parse(datetime);
    String formattedDate = DateFormat(format, locale).format(parsedDate);

    // Capitalizar la primera letra de cada palabra
    List<String> words = formattedDate.split(' ');
    for (int i = 0; i < words.length; i++) {
      if (words[i].isNotEmpty) {
        words[i] =
            words[i][0].toUpperCase() + words[i].substring(1).toLowerCase();
      }
    }

    return words.join(' ');
  }

  static bool isTimeInRange(TimeOfDay current, TimeOfDay start, TimeOfDay end) {
    // Convierte los TimeOfDay a minutos desde medianoche para comparación
    int currentMinutes = current.hour * 60 + current.minute;
    int startMinutes = start.hour * 60 + start.minute;
    int endMinutes = end.hour * 60 + end.minute;

    // Maneja el caso de horarios que cruzan la medianoche
    if (endMinutes < startMinutes) {
      endMinutes += 24 * 60; // Añade 24 horas en minutos
      if (currentMinutes < startMinutes) {
        currentMinutes += 24 * 60; // Ajusta el tiempo actual
      }
    }

    return currentMinutes >= startMinutes && currentMinutes < endMinutes;
  }

  static TimeOfDay parseTimeOfDay(String timeString) {
    List<String> parts = timeString.split(':');
    return TimeOfDay(
      hour: int.parse(parts[0]),
      minute: int.parse(parts[1]),
    );
  }

  /// Fechas de negocio

  /// Transformaciones

  static String capitalize(String text) {
    return '${text[0].toUpperCase()}${text.substring(1)}';
  }

  /// Lanzadores

  static Future<void> launchPhoneCall(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);

    try {
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
      } else {
        CoreService.muestraError('No se pudo llamar');
      }
    } catch (e) {
      CoreService.muestraError('Excepción: No se pudo llamar');
      if (kDebugMode) {
        debugPrint(e.toString());
      }
    }
  }

  static Future<void> launchWeb(String url) async {
    final Uri uri = Uri.parse(url);
    if (kDebugMode) {
      debugPrint(uri.toString());
    }

    try {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        CoreService.muestraError('No se pudo abrir la URL');
      }
    } catch (e) {
      CoreService.muestraError('Excepción: No se pudo abrir la URL');
      if (kDebugMode) {
        debugPrint(e.toString());
      }
    }
  }

  static Future<void> launchMap(Negocio negocio) async {
    final String lat = negocio.ubicacion!['latitud'] ?? '';
    final String lng = negocio.ubicacion!['longitud'] ?? '';
    if (lat.isEmpty || lng.isEmpty) {
      CoreService.muestraError('Ubicación no disponible');
      return;
    }

    // lee la preferencia guardada
    final prefs = await SharedPreferences.getInstance();
    final raw = prefs.getString(SettingsProvider.mapKey);

    late final MapPlatform mp;
    if (raw != null && raw.contains('apple')) {
      mp = MapPlatform.apple;
    } else if (raw != null && raw.contains('google')) {
      mp = MapPlatform.google;
    } else if (Platform.isIOS) {
      // 3a) iOS por defecto :contentReference[oaicite:5]{index=5}
      mp = MapPlatform.apple;
    } else {
      // 3b) Android (y resto) por defecto
      mp = MapPlatform.google;
    }

    if (mp == MapPlatform.apple) {
      CoreService.launchWeb('https://maps.apple.com/?daddr=$lat,$lng&dirflg=d');
    } else {
      CoreService.launchWeb(
          'https://www.google.com/maps/dir/?api=1&destination=$lat,$lng');
    }
  }

  /// Scroll

  static void scrollToKey(ScrollController scrollController, GlobalKey key) {
    // Buscamos el RenderObject del widget "Nuestras recomendaciones"
    final context = key.currentContext;
    if (context == null) return;

    // Calculamos la posición del widget en el scroll
    // localToGlobal => posición relativa a la pantalla
    final box = context.findRenderObject() as RenderBox;
    final offset = box.localToGlobal(Offset.zero);

    // Queremos animar el scroll de SingleChildScrollView
    // offset.dy = posición en la pantalla
    // offsetScroll = scrollController.offset + offset.dy
    //   - MediaQuery.of(context).padding.top (opcional si quieres restar el padding superior)
    final offsetScroll = scrollController.offset +
        offset.dy -
        MediaQuery.of(context).padding.top -
        kToolbarHeight; // Opcional: si tienes AppBar y quieres mostrarlo justo debajo

    // Animamos el scroll
    scrollController.animateTo(
      offsetScroll,
      duration: const Duration(milliseconds: 600),
      curve: Curves.easeInOut,
    );
  }

  static bool checkIfKeyIsVisible(GlobalKey key) {
    final context = key.currentContext;
    if (context == null) return false;

    final box = context.findRenderObject() as RenderBox?;
    if (box == null) return false;

    // Convertimos la posición local a coordenadas globales
    final offset = box.localToGlobal(Offset.zero);

    // offset.dy < 0 => significa que la parte superior de key
    // está por encima del tope de la pantalla (fuera de vista).
    final bool isOutOfView = offset.dy < 0;

    return !isOutOfView;
  }

  /// Log
  static void logIfDebug(String message) {
    if (EnvironmentConfig.isLocal || EnvironmentConfig.isStaging) {
      debugPrint(message);
    }
  }

  /// Permisos
  static Future<void> openAppSystemSettings(BuildContext context) async {
    // Definir título y mensaje según plataforma :contentReference[oaicite:3]{index=3}
    final String title =
        Platform.isIOS ? 'Activa Ubicación' : 'Permiso de ubicación necesario';
    final String message = Platform.isIOS
        ? 'Ve a Ajustes → Privacidad y seguridad → Servicios de localización → Mia y selecciona "Mientras se usa la app".'
        : 'Para mostrarte tu ubicación en el mapa, necesitamos acceso a la ubicación. '
            'Por favor, activa el permiso en los ajustes de la aplicación.';

    // Mostrar el diálogo inmediatamente :contentReference[oaicite:4]{index=4}
    return showDialog<void>(
      context: context,
      builder: (BuildContext ctx) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: <Widget>[
            // En Android/iOS ofrecemos cerrar el diálogo
            TextButton(
              onPressed: () => Navigator.of(ctx).pop(),
              child: const Text('Cancelar'),
            ),
            // Botón que abre Ajustes tras cerrar el diálogo :contentReference[oaicite:5]{index=5}
            TextButton(
              onPressed: () {
                Navigator.of(ctx).pop();
                openAppSettings();
              },
              child: const Text('Ir a ajustes'),
            ),
          ],
        );
      },
    );
  }

  /// Ubicación
}
