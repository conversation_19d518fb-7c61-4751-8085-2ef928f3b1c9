import 'package:flutter/foundation.dart';

enum Environment { local, staging, production, test }

class EnvironmentConfig {
  static Environment get environment {
    if (kReleaseMode) return Environment.production;

    final flavor = const String.fromEnvironment('FLAVOR');
    switch (flavor) {
      case 'test':
        return Environment.test;
      case 'staging':
        return Environment.staging;
      case 'production':
        return Environment.production;
      default:
        return Environment.local;
    }
  }

  static bool get isProduction => environment == Environment.production;
  static bool get isStaging => environment == Environment.staging;
  static bool get isLocal => environment == Environment.local;
  static bool get isTest => environment == Environment.test;
}
