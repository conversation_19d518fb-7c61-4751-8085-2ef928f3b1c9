<?php

namespace App\Console\Commands;

use App\Models\Zona;
use App\Models\Evento;
use App\Models\Negocio;
use App\Models\Categoria;
use App\Models\Localidad;
use App\Models\AppVersion;
use App\Services\CacheService;
use Illuminate\Console\Command;

class ClearCache extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:clear-all';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Limpia la caché de la aplicación y actualiza los timestamps';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $service = app(CacheService::class);
        $service->invalidateCache(Categoria::class, 'categorias_all');
        $service->invalidateCache(Negocio::class, 'negocios_all');
        $service->invalidateCache(Zona::class, 'zonas_all');
        $service->invalidateCache(Localidad::class, 'localidades_all');
        $service->invalidateCache(Evento::class, 'eventos_all');
        $service->invalidateCache(Evento::class, 'eventos_futuros');
        $service->invalidateCache(AppVersion::class, 'app_versions_all');
        $this->info('Caché limpiada y timestamps actualizados.');
    }
}
