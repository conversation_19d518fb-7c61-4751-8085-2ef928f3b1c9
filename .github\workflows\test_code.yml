name: Tests de código automatizados

on:
   push:
      branches: [main]
   # pull_request:
   #    branches: [main]

jobs:
   tests:
      runs-on: ubuntu-latest
      strategy:
         matrix:
            php: [8.3]
      name: PHP ${{ matrix.php }}

      steps:
         - name: Checkout
           uses: actions/checkout@v3

         - name: Cache PHP dependencies
           uses: actions/cache@v3
           with:
              path: vendor
              key: dependencies-php-${{ matrix.php }}-composer-${{ hashFiles('**/composer.lock') }}

         - name: Setup PHP
           uses: shivammathur/setup-php@v2
           with:
              php-version: ${{ matrix.php }}
              extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, gd
              coverage: none

         - name: Copy ENV Laravel Configuration for CI
           run: php -r "file_exists('.env') || copy('.env.example', '.env');"
           working-directory: ./webapp

         - name: Install dependencies
           run: composer install --no-ansi --no-interaction --no-scripts --no-suggest --no-progress --prefer-dist
           working-directory: ./webapp

         - name: Install NPM dependencies
           run: npm install
           working-directory: ./webapp

         - name: Compile assets
           run: npm run build
           working-directory: ./webapp

         - name: Generate key
           run: php artisan key:generate
           working-directory: ./webapp

         - name: Directory Permissions
           run: chmod -R 777 storage bootstrap/cache
           working-directory: ./webapp

         - name: Create Database
           run: |
              mkdir -p database
              touch database/database.sqlite
           working-directory: ./webapp

         - name: Execute tests (Unit and Feature tests) via PHPUnit
           env:
              DB_CONNECTION: sqlite
              DB_DATABASE: database/database.sqlite
              CI: true
              APP_ENV: testing
              APP_DEBUG: true
           run: php artisan test
           working-directory: ./webapp
