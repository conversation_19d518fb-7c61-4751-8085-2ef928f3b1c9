<?php

namespace App\Filament\Resources\PagoSuscripcionResource\Pages;

use Filament\Actions;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Validator;
use Filament\Resources\Pages\CreateRecord;
use App\Http\Requests\StorePagoSuscripcionRequest;
use App\Filament\Resources\PagoSuscripcionResource;

class CreatePagoSuscripcion extends CreateRecord
{
    protected static string $resource = PagoSuscripcionResource::class;

    // crea la funcion handleRecordCreation para validar los datos antes de crear el registro
    protected function handleRecordCreation(array $data): Model
    {
        $request = new StorePagoSuscripcionRequest();
        $validator = Validator::make($data, $request->rules(), $request->messages());
        try {
            $validatedData = $validator->validate();
            $model = static::getModel()::create($validatedData);

            return $model;
        } catch (\Illuminate\Validation\ValidationException $e) {
            Notification::make()
                ->title('Error al crear el pago.')
                // Mostrar en el body una lista de los errores de validación a partir de StorePagoSuscripcionRequest::messages()
                ->body(function () use ($e) {
                    $errors = $e->validator->errors();
                    $errorMessages = [];
                    foreach ($errors->all() as $error) {
                        $errorMessages[] = $error;
                    }
                    return implode('<br>', $errorMessages);
                })
                ->danger()
                ->send();
            throw $e;
        }
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
