<?php

namespace App\Http\Requests;

use App\Models\Evento;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class UpdateEventoRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $id = $this->route('evento');
        $evento = Evento::findOrFail($id);

        return $this->user()->can('update', $evento);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'nombre' => 'sometimes|string|max:255',
            'descripcion' => 'nullable|string',
            'url' => 'nullable|string',
            'fecha_inicio' => ['sometimes', 'date'],
            'fecha_fin' => ['sometimes', 'date', 'after:fecha_inicio'],
            'negocio_id' => 'sometimes|exists:negocios,id',
        ];
    }

    public function messages(): array
    {
        return [
            'nombre.max' => 'El nombre del evento es demasiado largo.',
            'descripcion.max' => 'La descripción del evento es demasiado larga.',
            'fecha_inicio.date' => 'La fecha de inicio del evento debe ser una fecha válida.',
            'fecha_fin.after' => 'La fecha de fin del evento debe ser posterior a la fecha de inicio.',
            'negocio_id.exists' => 'El negocio seleccionado no es válido.',
        ];
    }
}
