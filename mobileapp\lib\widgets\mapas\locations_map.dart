// Mantener este comentario con el nombre de archivo
// mobileapp\lib\widgets\locations_map.dart

import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:flutter_map_animations/flutter_map_animations.dart';
import 'package:latlong2/latlong.dart' as latlng;
import 'package:mia/models/negocio.dart';
import 'package:mia/services/core_service.dart';
import 'package:mia/services/mapas/map_service.dart';
import 'package:mia/services/mapas/map_tooltip_service.dart';
import 'package:mia/widgets/mapas/acciones_locations_map.dart';
import 'package:mia/widgets/mapas/marcador_negocio.dart';

class LocationsMap extends StatefulWidget {
  /// Lista de negocios para mostrar en el mapa como marcadores.
  final List<Negocio>? negocios;

  final Negocio? selectedNegocio;

  /// Controlador opcional del mapa (para zoom, etc.).
  final AnimatedMapController mapController;

  /// Altura del widget.
  final double height;

  /// Opciones de zoom.
  final double? zoom;
  final double? maxZoom;
  final double? minZoom;

  /// Funciones de callback
  final void Function(Negocio)? onMarkerTap;
  final void Function()? onMapTap;

  /// Otras opciones
  final bool? showActions;
  final bool unboundedMap;

  /// Mostrar la ubicación del usuario en el mapa.
  final bool showUserLocation;

  /// Coordenada actual del usuario (o null si no disponible)
  final latlng.LatLng? userLocation;

  /// Centro opcional para el mapa (tiene prioridad sobre el cálculo automático)
  final latlng.LatLng? center;

  const LocationsMap(
      {super.key,
      this.negocios,
      this.selectedNegocio,
      required this.mapController,
      required this.height,
      this.showUserLocation = false,
      this.userLocation,
      this.maxZoom,
      this.minZoom,
      this.zoom,
      this.onMarkerTap,
      this.onMapTap,
      this.showActions,
      this.center,
      required this.unboundedMap});

  @override
  State<LocationsMap> createState() => _LocationsMapState();
}

class _LocationsMapState extends State<LocationsMap> {
  double currentZoom = 15.0;
  final GlobalKey _mapKey = GlobalKey();
  final _tooltipService = MapaTooltipService();

  @override
  void initState() {
    super.initState();

    // Inicializar el zoom actual
    currentZoom = widget.zoom ?? 15.0;

    // Añadir listener para cambios en la cámara
    widget.mapController.mapController.mapEventStream.listen((event) {
      if (event is MapEventMove) {
        setState(() {
          currentZoom = event.camera.zoom;
          // Ocultar tooltip si hay uno visible al mover el mapa
          _tooltipService.ocultarTooltip();
        });
      }
    });
  }

  @override
  void dispose() {
    _tooltipService.ocultarTooltip();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Convertimos la lista de negocios a una lista (puede estar vacía)
    final List<Negocio> negociosList = widget.negocios ?? [];
    _tooltipService.ubicacionUsuario = widget.userLocation;

    // Determinamos el centro del mapa:
    // si hay negocios, usamos el primero; si no, una ubicación por defecto.
    final latlng.LatLng center = widget.center ??
        (negociosList.isNotEmpty
            ? negociosList.length == 1
                ? CoreService.getLatLng(negociosList.first)
                : MapService.getCenter(negociosList)
            : latlng.LatLng(36.9990019, -6.5478919));

    final double markerSize = 40;

    // Construimos la lista de marcadores a partir de [negociosList].
    final markers = <Marker>[];

    for (var negocio in negociosList) {
      final bool isSelected = (widget.selectedNegocio != null &&
          widget.selectedNegocio!.id == negocio.id);

      // Crear el widget de marcador de negocio
      final marcadorNegocio = MarcadorNegocio(
        negocio: negocio,
        markerSize: markerSize,
        isSelected: isSelected,
        onMarkerTap: widget.onMarkerTap,
        mapKey: _mapKey,
        tooltipService: _tooltipService,
      );

      // Añadir los marcadores que genera este widget, pasando el contexto
      markers.addAll(marcadorNegocio.getMarkers(context));
    }

    // Marcador de posición del usuario
    if (widget.showUserLocation && widget.userLocation != null) {
      markers.add(
        Marker(
          width: markerSize,
          height: markerSize,
          point: widget.userLocation!, // coordenada del usuario
          alignment: Alignment.center,
          child: Icon(
            Icons.my_location, // icono típico "punto azul"
            color: Colors.blue,
            size: markerSize * 0.8, // ajústalo al tamaño que quieras
          ),
        ),
      );
    }

    return SizedBox(
      height: widget.height,
      key: _mapKey,
      child: Stack(
        children: [
          FlutterMap(
            mapController: widget.mapController.mapController,
            options: MapOptions(
                interactionOptions: widget.showActions ?? true
                    ? InteractionOptions(
                        flags: InteractiveFlag.all & ~InteractiveFlag.rotate,
                      )
                    : InteractionOptions(flags: InteractiveFlag.none),
                initialCenter: center,
                initialZoom: currentZoom,
                maxZoom: widget.maxZoom ?? 17.0,
                minZoom: widget.minZoom ?? 12.0,
                cameraConstraint: widget.unboundedMap
                    ? CameraConstraint.unconstrained()
                    : CameraConstraint.containCenter(
                        bounds: LatLngBounds(
                            // Extremos
                            latlng.LatLng(37.0328554, -6.5732570),
                            latlng.LatLng(36.9739728, -6.5152259))),
                onTap: (tapPosition, point) {
                  _tooltipService
                      .ocultarTooltip(); // Ocultar tooltip si se toca el mapa
                  widget.onMapTap?.call();
                }),
            children: [
              // Capa base de OpenStreetMap
              TileLayer(
                urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                userAgentPackageName:
                    'es.lbcdev.mia', // Cambia según tu proyecto
              ),
              // Capa de marcadores
              MarkerLayer(
                markers: markers,
              ),
            ],
          ),
          if (widget.showActions ?? true)
            AccionesLocationsMap(widget: widget, negociosList: negociosList)
        ],
      ),
    );
  }
}
