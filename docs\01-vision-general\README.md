# Visión General del Proyecto MIA

## Descripción del Proyecto

MIA es una plataforma digital para la promoción y gestión de negocios locales. Consta de:

1. **Backend Laravel**: API REST y panel de administración
2. **Aplicación Móvil Flutter**: App nativa para iOS y Android
3. **Sistema de Gestión**: Panel Filament para administradores y propietarios

## Arquitectura General

```
┌─────────────────────────────────────────────────────────────┐
│                     APLICACIÓN MÓVIL                         │
│                    (Flutter/Dart)                            │
│  - Consulta de negocios                                      │
│  - Mapas interactivos                                        │
│  - Favoritos y filtros                                       │
└────────────────────┬────────────────────────────────────────┘
                     │
                     │ HTTP/REST API
                     │ (Laravel Sanctum)
                     │
┌────────────────────▼────────────────────────────────────────┐
│                    BACKEND LARAVEL                           │
│                                                              │
│  ┌──────────────────────────────────────────────────────┐  │
│  │              API REST (routes/api.php)               │  │
│  │  - Autenticación                                     │  │
│  │  - CRUD de recursos                                  │  │
│  │  - Versionado (v1)                                   │  │
│  └──────────────────────────────────────────────────────┘  │
│                                                              │
│  ┌──────────────────────────────────────────────────────┐  │
│  │         PANEL FILAMENT (routes/web.php)              │  │
│  │  - Dashboard administrativo                          │  │
│  │  - Gestión de recursos                               │  │
│  │  - Widgets y estadísticas                            │  │
│  └──────────────────────────────────────────────────────┘  │
│                                                              │
│  ┌──────────────────────────────────────────────────────┐  │
│  │              CAPA DE NEGOCIO                         │  │
│  │  - Modelos Eloquent                                  │  │
│  │  - Políticas de autorización                         │  │
│  │  - Observers                                         │  │
│  │  - Services                                          │  │
│  └──────────────────────────────────────────────────────┘  │
│                                                              │
└────────────────────┬────────────────────────────────────────┘
                     │
                     │
┌────────────────────▼────────────────────────────────────────┐
│                   BASE DE DATOS                              │
│                   (MySQL/PostgreSQL)                         │
│  - Negocios, Categorías, Zonas                              │
│  - Usuarios, Roles, Permisos                                │
│  - Suscripciones, Pagos                                     │
│  - Eventos                                                   │
└─────────────────────────────────────────────────────────────┘
```

## Stack Tecnológico

### Backend
| Tecnología | Versión | Propósito |
|------------|---------|-----------|
| PHP | 8.2+ | Lenguaje base |
| Laravel | 11.x | Framework web |
| Filament | 3.2+ | Panel de administración |
| Laravel Sanctum | 4.0+ | Autenticación API |
| Spatie Permission | 6.10+ | Sistema de permisos |
| Spatie Media Library | 11.12+ | Gestión de archivos |
| Laravel Jetstream | 5.3+ | Autenticación web |
| Sentry | 4.14+ | Monitoreo de errores |

### Frontend
| Tecnología | Versión | Propósito |
|------------|---------|-----------|
| Flutter | 3.x | Framework móvil |
| Dart | 3.x | Lenguaje |
| Provider | 6.1+ | Gestión de estado |
| HTTP | 1.3+ | Cliente HTTP |
| Flutter Map | 7.0+ | Mapas interactivos |
| Geolocator | 14.0+ | Geolocalización |
| Shared Preferences | 2.5+ | Almacenamiento local |
| Cached Network Image | 3.4+ | Caché de imágenes |

### Base de Datos
- **Motor**: MySQL/PostgreSQL
- **ORM**: Eloquent (Laravel)
- **Migraciones**: Laravel Migrations
- **Seeders**: Laravel Seeders

## Flujo de Datos

### 1. Flujo de Consulta (App Móvil → Backend)

```
Usuario Móvil
    ↓
Abre la app
    ↓
ApiService.fetchData()
    ↓
GET /api/v1/negocios
    ↓
NegocioController@index
    ↓
Cache::remember('negocios_all')
    ↓
Negocio::with(['categorias', 'zona', 'eventos', 'media'])
    ↓
JSON Response
    ↓
Modelo Dart (Negocio.fromJson)
    ↓
Provider actualiza estado
    ↓
UI se actualiza
```

### 2. Flujo de Creación (Panel Admin → Backend)

```
Usuario Admin
    ↓
Filament Form
    ↓
NegocioResource::create()
    ↓
StoreNegocioRequest (validación)
    ↓
NegocioController@store
    ↓
Negocio::create()
    ↓
NegocioObserver@created
    ↓
Cache::forget('negocios_all')
    ↓
Suscripcion::create()
    ↓
Redirect + Notificación
```

### 3. Flujo de Autenticación

```
Usuario
    ↓
POST /api/login
    ↓
AuthController@login
    ↓
User::where('email', $email)->first()
    ↓
Hash::check($password)
    ↓
$user->createToken('auth_token')
    ↓
Response: { token, user }
    ↓
LocalStorageService.setToken()
    ↓
Requests subsiguientes incluyen:
Authorization: Bearer {token}
```

## Entidades Principales

### Jerarquía de Datos

```
Localidad (Municipio)
    └── Zona (Área geográfica)
        └── Negocio (Establecimiento)
            ├── Categorías (N:M)
            ├── Eventos (1:N)
            ├── Suscripción (1:1)
            │   └── Pagos (1:N)
            ├── Media (Imágenes/Logos)
            └── Usuario (Propietario)
```

### Relaciones Clave

- **Negocio ↔ Categoría**: Muchos a Muchos (un negocio puede tener múltiples categorías)
- **Negocio → Zona**: Muchos a Uno (un negocio pertenece a una zona)
- **Zona → Localidad**: Muchos a Uno (una zona pertenece a una localidad)
- **Negocio → Suscripción**: Uno a Uno (un negocio tiene una suscripción)
- **Suscripción → Pagos**: Uno a Muchos (una suscripción tiene múltiples pagos)
- **Negocio → Eventos**: Uno a Muchos (un negocio puede tener múltiples eventos)
- **Usuario → Negocios**: Uno a Muchos (un usuario puede gestionar múltiples negocios)

## Sistema de Caché

### Backend (Laravel)
- **Driver**: Redis/File
- **Estrategia**: Cache Forever con invalidación manual
- **Keys principales**:
  - `negocios_all`: Lista completa de negocios
  - `categorias_all`: Lista de categorías
  - `zonas_all`: Lista de zonas
  - `eventos_futuros`: Eventos próximos

### Frontend (Flutter)
- **Driver**: SharedPreferences + HTTP Cache
- **Estrategia**: Last-Modified headers
- **Almacenamiento**:
  - Datos JSON en SharedPreferences
  - Imágenes en CachedNetworkImage
  - Configuración de usuario

## Seguridad

### Backend
- **Autenticación**: Laravel Sanctum (tokens)
- **Autorización**: Spatie Permissions + Policies
- **Validación**: Form Requests
- **CSRF**: Protección en rutas web
- **Rate Limiting**: Throttle middleware

### Frontend
- **Tokens**: Almacenados en SharedPreferences
- **HTTPS**: Todas las comunicaciones
- **Validación**: Validación local antes de enviar

## Entornos

### Desarrollo
- Backend: `http://localhost:8000`
- Base de datos: Local MySQL
- Caché: File driver

### Producción
- Backend: HTTPS con dominio
- Base de datos: MySQL en servidor
- Caché: Redis
- Monitoreo: Sentry
- Backups: Automáticos diarios

## Próximos Pasos

Para profundizar en cada área:
- [Backend Laravel](../02-backend/README.md)
- [Frontend Flutter](../03-frontend/README.md)
- [Base de Datos](../04-database/README.md)
- [API](../05-api/README.md)
- [Testing](../06-testing/README.md)

