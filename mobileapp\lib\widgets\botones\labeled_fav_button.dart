// lib/widgets/botones/labeled_fav_button.dart

import 'package:flutter/material.dart';
import 'package:mia/models/negocio.dart';
import 'package:mia/widgets/botones/fav_button.dart';
import 'package:provider/provider.dart';
import 'package:mia/config/colors.dart';
import 'package:mia/providers/favorites_provider.dart';

/// A favorite button that includes a descriptive text label
/// which changes based on the favorite status.
class LabeledFavButton extends StatelessWidget {
  /// The negocio to toggle favorite status for.
  final Negocio negocio;

  /// The size of the icon. Defaults to 24.0.
  final double iconSize;

  /// The icon to display when active (favorited).
  final IconData activeIcon;

  /// The icon to display when inactive (not favorited).
  final IconData inactiveIcon;

  /// Duration of the toggle animation. Defaults to 300ms.
  final Duration animationDuration;

  /// Optional custom favorites provider. If null, the widget will use
  /// the provider from the widget tree context.
  final FavoritesProvider? favoritesProvider;

  /// Text to show when the item is not a favorite.
  final String addToFavoritesText;

  /// Text to show when the item is a favorite.
  final String removeFromFavoritesText;

  /// Style for the button text.
  final TextStyle? textStyle;

  /// Spacing between icon and text.
  final double spacing;

  const LabeledFavButton({
    super.key,
    required this.negocio,
    this.iconSize = 24.0,
    this.activeIcon = Icons.favorite,
    this.inactiveIcon = Icons.favorite_border,
    this.animationDuration = const Duration(milliseconds: 300),
    this.favoritesProvider,
    this.addToFavoritesText = 'Añadir a favoritos',
    this.removeFromFavoritesText = 'Quitar de favoritos',
    this.textStyle,
    this.spacing = 8.0,
  });

  @override
  Widget build(BuildContext context) {
    final FavoritesProvider favProv =
        favoritesProvider ?? Provider.of<FavoritesProvider>(context);

    final isFav = favProv.isFavorite(negocio.id);
    final buttonText = isFav ? removeFromFavoritesText : addToFavoritesText;

    return TextButton.icon(
      onPressed: () => favProv.toggleFavorite(negocio.id),
      style: TextButton.styleFrom(
        padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      ),
      icon: FavButton(
        negocio: negocio,
        size: iconSize,
        activeIcon: activeIcon,
        inactiveIcon: inactiveIcon,
        animationDuration: animationDuration,
        favoritesProvider: favoritesProvider,
      ),
      label: AnimatedSwitcher(
        duration: animationDuration,
        transitionBuilder: (child, animation) {
          return FadeTransition(
            opacity: animation,
            child: SlideTransition(
              position: Tween<Offset>(
                begin: Offset(0.2, 0.0),
                end: Offset.zero,
              ).animate(animation),
              child: child,
            ),
          );
        },
        child: Text(
          buttonText,
          key: ValueKey<String>(buttonText),
          style: textStyle ??
              Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: isFav
                        ? AppColors.current.errorColor
                        : AppColors.current.textColor,
                  ),
        ),
      ),
    );
  }
}
