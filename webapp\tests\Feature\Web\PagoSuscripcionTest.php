<?php

namespace Tests\Feature\Web;

use Tests\BaseTest;
use App\Models\User;
use Livewire\Livewire;
use App\Models\Negocio;
use App\Models\Suscripcion;
use Carbon\Carbon;
use App\Models\PagoSuscripcion;
use Spatie\Permission\Models\Role;
use PHPUnit\Framework\Attributes\Group;
use Database\Seeders\RolesPermissionSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Filament\Resources\PagoSuscripcionResource;

#[Group('pago_suscripcion')]
#[Group('webapp')]
#[Group('pago_suscripcion_webapp')]
class PagoSuscripcionTest extends BaseTest
{
    use RefreshDatabase;

    private array $usuarios;

    private Negocio $negocio;
    private Suscripcion $suscripcion;

    protected function setUp(): void
    {
        parent::setUp();

        app()->setLocale('es');

        $this->seed(RolesPermissionSeeder::class);

        foreach (Role::all() as $rol) {
            $user = User::factory()->create();
            $user->assignRole($rol->name);

            $this->usuarios[$rol->name] = [
                'usuario' => $user,
            ];
        }

        $this->negocio = Negocio::factory()
            ->conSuscripcion()
            ->create([
                'user_id' => $this->usuarios['cliente']['usuario']->id,
            ]);

        $this->suscripcion = $this->negocio->suscripcion;
    }

    public function test_el_sistema_muestra_las_paginas_de_pago_suscripcion_correctamente_segun_el_rol_del_usuario(): void
    {

        // 🟢 Test positivos

        $pago = PagoSuscripcion::factory()->create([
            'suscripcion_id' => $this->suscripcion->id,
        ]);

        $test_roles = $this->_getRolesPositivos(['system.admin-dashboard']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $this->get(PagoSuscripcionResource::getUrl('index'))->assertSuccessful();
            $this->get(PagoSuscripcionResource::getUrl('create'))->assertSuccessful();
            $this->get(PagoSuscripcionResource::getUrl('edit', ['record' => $pago->id]))->assertSuccessful();
        }

        // 🔴 Test negativos

        $test_roles = $this->_getRolesNegativos(['system.admin-dashboard']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $this->get(PagoSuscripcionResource::getUrl('index'))->assertForbidden();
            $this->get(PagoSuscripcionResource::getUrl('create'))->assertForbidden();
            $this->get(PagoSuscripcionResource::getUrl('edit', ['record' => $pago->id]))->assertForbidden();
        }
    }

    public function test_el_sistema_crea_un_nuevo_pago_solo_con_datos_validos(): void
    {
        // 🟢 Test positivos

        $test_roles = $this->_getRolesPositivos(['system.admin-dashboard']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $test = Livewire::test(PagoSuscripcionResource\Pages\CreatePagoSuscripcion::class);

            $newData = PagoSuscripcion::factory()->make()->toArray();

            $test
                ->fillForm($newData)
                ->call('create')
                ->assertHasNoFormErrors();

            $this->assertDatabaseHas(PagoSuscripcion::class, [
                'suscripcion_id' => $newData['suscripcion_id'],
                'metodo_pago' => $newData['metodo_pago'],
                'importe' => $newData['importe'],
                'fecha_pago' => Carbon::parse($newData['fecha_pago'])->format('Y-m-d H:i:s'),
                'transaccion_id' => $newData['transaccion_id'],
                'estado' => $newData['estado'],
            ]);
        }

        // 🔴 Test negativos

        // $test_roles = $this->_getRolesNegativos(['system.admin-dashboard']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $test = Livewire::test(PagoSuscripcionResource\Pages\CreatePagoSuscripcion::class);

            $test_cases = [
                // caso 1: campos vacíos
                [
                    'payload' => [],
                    'expectedInvalid' => ['suscripcion_id', 'metodo_pago', 'importe', 'fecha_pago']
                ],
            ];

            foreach ($test_cases as $case) {
                $test->fillForm($case['payload'])
                    ->call('create')
                    ->assertHasFormErrors($case['expectedInvalid']);
            }
        }
    }

    public function test_el_sistema_edita_un_pago_solo_con_datos_validos(): void
    {
        // 🟢 Test positivos

        $pago = PagoSuscripcion::factory()->create([
            'suscripcion_id' => $this->suscripcion->id,
        ]);

        $test_roles = $this->_getRolesPositivos(['system.admin-dashboard']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $test = Livewire::test(PagoSuscripcionResource\Pages\EditPagoSuscripcion::class, ['record' => $pago->id]);

            $newData = PagoSuscripcion::factory()->make()->toArray();

            $test
                ->fillForm($newData)
                ->call('save')
                ->assertHasNoFormErrors();

            $this->assertDatabaseHas(PagoSuscripcion::class, [
                'suscripcion_id' => $newData['suscripcion_id'],
                'metodo_pago' => $newData['metodo_pago'],
                'importe' => $newData['importe'],
                'fecha_pago' => Carbon::parse($newData['fecha_pago'])->format('Y-m-d H:i:s'),
                'transaccion_id' => $newData['transaccion_id'],
                'estado' => $newData['estado'],
            ]);
        }

        // 🔴 Test negativos

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $test = Livewire::test(PagoSuscripcionResource\Pages\EditPagoSuscripcion::class, ['record' => $pago->id]);

            $test_cases = [
                // caso 1: campos vacíos
                [
                    'payload' => [],
                    'expectedInvalid' => ['suscripcion_id', 'metodo_pago', 'importe', 'fecha_pago']
                ],
            ];

            foreach ($test_cases as $case) {
                $test->fillForm($case['payload'])
                    ->call('save')
                    ->assertHasFormErrors($case['expectedInvalid']);
            }
        }
    }
}
