<?php

use App\Enums\EstadoSuscripcion;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('suscripciones', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('negocio_id');
            $table->string('plan')->nullable();             // Ejemplo: 'basic', 'premium'
            $table->integer('status')->default(EstadoSuscripcion::ACTIVE);
            $table->dateTime('started_at')->nullable();
            $table->dateTime('ends_at')->nullable();
            $table->timestamps();

            $table->foreign('negocio_id')
                ->references('id')
                ->on('negocios')
                ->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('suscripciones');
    }
};
