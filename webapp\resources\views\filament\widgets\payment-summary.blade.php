{{-- webapp\resources\views\filament\widgets\payment-summary.blade.php --}}
<x-filament-widgets::widget>
    <x-filament::section>
        <div class="bg-white rounded-xl border border-gray-200 p-6">
            <h2 class="text-xl font-bold mb-6 text-gray-900 border-b border-gray-200 pb-3">
                Resumen de Pagos 
                {{-- Calcular la temporada actual --}}
                @php
                    $hoy = now();
                    $inicioTemporada = $hoy->month >= 4 ? $hoy->year : $hoy->year - 1;
                    $finTemporada = $inicioTemporada + 1;
                @endphp
                {{-- Mostrar la temporada actual --}}
                <span class="text-sm text-gray-500 font-medium">
                    Temporada: {{ $inicioTemporada }} - {{ $finTemporada }}
                </span>
            </h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                <!-- <PERSON>gos Pendientes -->
                <div class="text-center">
                    <div class="flex justify-center mb-3">
                        <div class="p-3 bg-yellow-100 rounded-full">
                            <x-heroicon-o-clock class="w-6 h-6 text-yellow-600" />
                        </div>
                    </div>
                    <div class="text-2xl font-bold text-yellow-800 mb-1">{{ $pagosPendientes }}</div>
                    <div class="text-sm text-gray-600 font-medium mb-2">Pendientes</div>
                    <div class="text-lg font-semibold text-yellow-700">
                        {{ number_format($importePendiente, 2) }}€
                    </div>
                </div>

                <!-- Pagos Completados -->
                <div class="text-center">
                    <div class="flex justify-center mb-3">
                        <div class="p-3 bg-green-100 rounded-full">
                            <x-heroicon-o-check-circle class="w-6 h-6 text-green-600" />
                        </div>
                    </div>
                    <div class="text-2xl font-bold text-green-800 mb-1">{{ $pagosCompletados }}</div>
                    <div class="text-sm text-gray-600 font-medium mb-2">Completados</div>
                    <div class="text-lg font-semibold text-green-700">
                        {{ number_format($importeCompletado, 2) }}€
                    </div>
                </div>

                <!-- Total de Pagos -->
                <div class="text-center">
                    <div class="flex justify-center mb-3">
                        <div class="p-3 bg-blue-100 rounded-full">
                            <x-heroicon-c-square-3-stack-3d class="w-6 h-6 text-blue-600" />
                        </div>
                    </div>
                    <div class="text-2xl font-bold text-blue-800 mb-1">{{ $pagosTotales }}</div>
                    <div class="text-sm text-gray-600 font-medium mb-2">Total</div>
                    <div class="text-lg font-semibold text-blue-700">
                        {{ number_format($importeTotal, 2) }}€
                    </div>
                </div>

                <!-- Suscripciones sin pagos en temporada actual -->
                <div class="text-center">
                    <div class="flex justify-center mb-3">
                        <div class="p-3 bg-red-100 rounded-full">
                            <x-heroicon-o-x-circle class="w-6 h-6 text-red-600" />
                        </div>
                    </div>
                    <div class="text-2xl font-bold text-red-800 mb-1">{{ $numSuscripcionesSinPagosEnTemporadaActual }}</div>
                    <div class="text-sm text-gray-600 font-medium mb-2">Sin pagos</div>
                    <div class="text-lg font-semibold text-red-700">
                        {{ number_format($importeDeSuscripcionesSinPagosEnTemporadaActual, 2) }}€
                    </div>
                </div>

                <!-- Importe Esperado -->
                <div class="text-center">
                    <div class="flex justify-center mb-3">
                        <div class="p-3 bg-blue-100 rounded-full">
                            <x-heroicon-c-square-3-stack-3d class="w-6 h-6 text-blue-600" />
                        </div>
                    </div>
                    <div class="text-2xl font-bold text-blue-800 mb-1 invisible" >{{ $pagosTotales }}</div>
                    <div class="text-sm text-gray-600 font-medium mb-2">Esperado</div>
                    <div class="text-lg font-semibold text-blue-700">
                        {{ number_format($importeEsperado, 2) }}€
                    </div>
                </div>
            </div>


            {{-- <!-- Línea separadora y estadísticas adicionales (opcional) -->
            <div class="mt-6 pt-4 border-t border-gray-200">
                <div class="flex justify-between items-center text-sm text-gray-500">
                    <span>Actualizado: {{ now()->format('d/m/Y H:i') }}</span>
                    <span>Estado del sistema: 
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Activo
                        </span>
                    </span>
                </div>
            </div> --}}
        </div>
    </x-filament::section>
</x-filament-widgets::widget>