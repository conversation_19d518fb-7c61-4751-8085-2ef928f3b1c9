<?php

namespace Tests;

use Tests\TestCase;
use App\Models\User;
use Spatie\Permission\Models\Role;

abstract class BaseTest extends TestCase
{
    /**
     * Roles con unos permisos dados
     *
     * @param array $permisos Array con los nombres de los permisos
     * @return array Array con los nombres de los roles
     */
    protected function _getRolesPositivos(array $permisos): array
    {
        if (empty($permisos)) {
            return [];
        }

        $roles = Role::all();
        $rolesConTodosLosPermisos = [];

        foreach ($roles as $role) {
            $permisosDelRol = $role->permissions->pluck('name')->toArray();
            $tienePermisos = true;

            foreach ($permisos as $permiso) {
                if (!in_array($permiso, $permisosDelRol)) {
                    $tienePermisos = false;
                    break;
                }
            }

            if ($tienePermisos) {
                $rolesConTodosLosPermisos[] = $role->name;
            }
        }

        return $rolesConTodosLosPermisos;
    }

    /**
     * Roles sin ninguno de unos permisos dados
     *
     * @param array $permisos Array con los nombres de los permisos
     * @return array Array con los nombres de los roles
     */
    protected function _getRolesNegativos(array $permisos): array
    {
        if (empty($permisos)) {
            return [];
        }

        return Role::whereDoesntHave('permissions', function ($query) use ($permisos) {
            $query->whereIn('name', $permisos);
        })
            ->get()->pluck('name')->toArray()
        ;
    }

    /**
     * Roles sin alguno de unos permisos dados
     *
     * @param array $permisos Array con los nombres de los permisos
     * @return array Array con los nombres de los roles
     */
    protected function _getRolesNegativosSinAlgunPermiso(array $permisos): array
    {
        if (empty($permisos)) {
            return [];
        }

        $rolesPositivos = $this->_getRolesPositivos($permisos);
        $todosLosRoles = Role::pluck('name')->toArray();

        return array_values(array_diff($todosLosRoles, $rolesPositivos));
    }


    /**
     * Crea un usuario con un rol dado o sin roles
     *
     * @param string |null $roleName Nombre del rol o null para sin roles
     * @return User Usuario creado
     */
    protected function createUserWithRole(string | null $roleName): User
    {
        $user = User::factory()->create();
        if ($roleName) {
            $user->assignRole($roleName);
        }

        return $user;
    }
}
