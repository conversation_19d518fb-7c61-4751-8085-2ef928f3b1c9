<?php

namespace App\Filament\Resources\ZonaResource\Pages;

use App\Models\Zona;
use App\Models\Negocio;
use App\Services\CacheService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Model;
use App\Filament\Resources\ZonaResource;
use Filament\Resources\Pages\CreateRecord;

class CreateZona extends CreateRecord
{
    protected static string $resource = ZonaResource::class;


    protected CacheService $cacheService;

    public function boot(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    protected function handleRecordCreation(array $data): Model
    {
        $zona = static::getModel()::create($data);

        $this->cacheService->invalidateCache(Zona::class, 'zonas_all');
        $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');

        return $zona;
    }
}
