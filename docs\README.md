# Documentación del Proyecto MIA

> 💡 **Referencia Rápida**: Si buscas comandos y patrones comunes, consulta [QUICK_REFERENCE.md](./QUICK_REFERENCE.md)

## Índice General

Esta documentación proporciona una guía completa del proyecto MIA, una aplicación de gestión de negocios locales con backend Laravel y aplicación móvil Flutter.

### 📚 Estructura de la Documentación

#### 1. [Visión General](./01-vision-general/README.md)

-  Descripción del proyecto
-  Arquitectura general
-  Stack tecnológico
-  Flujo de datos

#### 2. [Backend - Laravel](./02-backend/README.md)

-  Estructura del proyecto
-  Modelos y relaciones
-  API REST
-  Sistema de permisos
-  Panel de administración (Filament)

#### 3. [Frontend - Flutter](./03-frontend/README.md)

-  Estructura del proyecto
-  Arquitectura de la aplicación
-  Providers y estado
-  Servicios
-  Widgets principales

#### 4. [Base de Datos](./04-database/README.md)

-  Esquema de base de datos
-  Relaciones entre entidades
-  Migraciones importantes

#### 5. [API](./05-api/README.md)

-  Especificación OpenAPI
-  Endpoints disponibles
-  Autenticación
-  Ejemplos de uso

#### 6. [Testing](./06-testing/README.md)

-  Estrategia de testing
-  Plantillas de tests backend
-  Plantillas de tests frontend
-  Guía para escribir nuevos tests

#### 7. [Guías de Desarrollo](./07-guias/README.md)

-  Cómo añadir un nuevo modelo
-  Cómo añadir un nuevo endpoint
-  Cómo crear un nuevo widget
-  Mejores prácticas

## 🚀 Inicio Rápido

### Backend (Laravel)

```bash
cd webapp
composer install
php artisan migrate
php artisan db:seed
php artisan serve
```

### Frontend (Flutter)

```bash
cd mobileapp
flutter pub get
flutter run
```

## 📋 Requisitos del Sistema

### Backend

-  PHP 8.2+
-  Composer
-  MySQL/PostgreSQL
-  Redis (opcional, para caché)

### Frontend

-  Flutter 3.x
-  Dart 3.x
-  Android Studio / Xcode

## 🔑 Conceptos Clave

### Entidades Principales

-  **Negocio**: Establecimientos comerciales
-  **Categoría**: Clasificación jerárquica de negocios
-  **Zona**: Áreas geográficas dentro de localidades
-  **Localidad**: Municipios o ciudades
-  **Evento**: Promociones o eventos de negocios
-  **Suscripción**: Planes de pago de negocios
-  **Usuario**: Usuarios del sistema con roles

### Flujo de Trabajo

1. Los usuarios se registran y obtienen roles
2. Los propietarios crean negocios y suscripciones
3. Los negocios se publican en la app móvil
4. Los usuarios finales consultan negocios por categoría/zona
5. Los administradores gestionan todo desde el panel Filament

## 📱 Características Principales

### Backend

-  ✅ API REST completa
-  ✅ Sistema de autenticación con Sanctum
-  ✅ Panel de administración con Filament
-  ✅ Sistema de permisos granular (Spatie)
-  ✅ Gestión de medios (imágenes)
-  ✅ Caché inteligente
-  ✅ Backups automáticos

### Frontend

-  ✅ Interfaz nativa Flutter
-  ✅ Mapas interactivos
-  ✅ Sistema de favoritos
-  ✅ Filtros avanzados
-  ✅ Caché local
-  ✅ Modo oscuro/claro
-  ✅ Geolocalización

## 🎯 Objetivos de la Temporada 2

Esta documentación está diseñada para:

1. Facilitar el retorno al proyecto después de períodos de inactividad
2. Servir como referencia para LLMs al generar código
3. Minimizar la introducción de errores
4. Acelerar el desarrollo de nuevas funcionalidades
5. Mantener consistencia en el código

## 📞 Soporte

Para dudas o problemas, consulta las guías específicas en cada sección o revisa los ejemplos de código en el proyecto.

---

**Última actualización**: 2025-10-01
**Versión del proyecto**: Temporada 2
