import 'package:flutter/material.dart';
import 'package:mia/models/evento.dart';
import 'package:mia/widgets/app_scaffold.dart';

class EventoPage extends StatefulWidget {
  final Evento evento;

  const EventoPage({super.key, required this.evento});

  @override
  State<EventoPage> createState() => _EventoPageState();
}

class _EventoPageState extends State<EventoPage> {
  @override
  Widget build(BuildContext context) {
    return AppScaffold(
        floatingActionButton: FloatingActionButton(
          shape:
              ShapeBorder.lerp(const CircleBorder(), const CircleBorder(), 0.5),
          onPressed: () {
            Navigator.pop(context);
          },
          child: const Icon(Icons.arrow_back),
        ),
        child: Container());
  }
}
