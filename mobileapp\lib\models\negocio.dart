// import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
// import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:latlong2/latlong.dart';
import 'package:mia/models/categoria.dart';
import 'package:mia/models/evento.dart';
import 'package:mia/models/media.dart';
import 'package:mia/services/core_service.dart';
import 'package:mia/models/zona.dart';

class Negocio {
  final int id;
  final String nombre;
  final String? descripcion;
  final String direccion;
  final String contacto;
  final Map<String, String>? contactosSecundarios;
  final List<Categoria>? categorias;
  final Zona? zona;
  final List<Evento>? eventos;

  final List<Media>? imagenes;
  final List<Media>? logos;

  final Map<String, String>? ubicacion;
  final Map<String, List<String>>? horario;
  final Map<String, String>? enlacesPropios;
  final Map<String, String>? enlacesSociales;
  final Map<String, Map<String, String>>? precios;

  final Map<String, dynamic> suscripcion;

  Negocio({
    required this.id,
    required this.nombre,
    this.descripcion,
    required this.direccion,
    required this.contacto,
    this.contactosSecundarios,
    this.categorias,
    this.zona,
    this.imagenes,
    this.logos,
    this.ubicacion,
    this.horario,
    this.enlacesPropios,
    this.enlacesSociales,
    this.precios,
    required this.suscripcion,
    this.eventos,
  });

  factory Negocio.fromJson(Map<String, dynamic> json) {
    try {
      // Validar campos requeridos
      if (!json.containsKey('id') || json['id'] == null) {
        throw Exception('El campo id es requerido');
      }
      if (!json.containsKey('nombre') || json['nombre'] == null) {
        throw Exception('El campo nombre es requerido');
      }
      if (!json.containsKey('direccion') || json['direccion'] == null) {
        throw Exception('El campo direccion es requerido');
      }
      if (!json.containsKey('contacto') || json['contacto'] == null) {
        throw Exception('El campo contacto es requerido');
      }
      if (!json.containsKey('suscripcion') || json['suscripcion'] == null) {
        throw Exception('El campo suscripcion es requerido');
      }

      // Validar tipos básicos
      if (json['id'] is! int) {
        throw Exception('El campo id debe ser un entero');
      }
      if (json['nombre'] is! String) {
        throw Exception('El campo nombre debe ser una cadena');
      }
      if (json['direccion'] is! String) {
        throw Exception('El campo direccion debe ser una cadena');
      }
      if (json['contacto'] is! String) {
        throw Exception('El campo contacto debe ser una cadena');
      }
      if (json['suscripcion'] is! Map<String, dynamic>) {
        throw Exception('El campo suscripcion debe ser un objeto');
      }

      // Procesar campos opcionales

      // Categorias
      List<Categoria>? categorias;
      if (json['categorias'] != null) {
        categorias = (json['categorias'] as List)
            .map((item) => Categoria.fromJson(item))
            .toList();
      }

      // Zona
      Zona? zona;
      if (json['zona'] != null) {
        zona = Zona.fromJson(json['zona']);
      }

      // Eventos
      List<Evento>? eventos;
      if (json['eventos'] != null) {
        eventos = (json['eventos'] as List)
            .map((item) => Evento.fromJson(item))
            .toList();
      }

      // Imagenes
      List<Media>? imagenes;
      if (json['imagenes'] != null) {
        imagenes = (json['imagenes'] as List)
            .map((item) => Media.fromJson(item))
            .toList();
      }

      // Logos
      List<Media>? logos;
      if (json['logos'] != null) {
        logos = (json['logos'] as List)
            .map((item) => Media.fromJson(item))
            .toList();
      }

      // Adaptar horario:
      Map<String, List<String>>? horario;
      if (json['horario'] != null) {
        horario = (json['horario'] as Map).map((k, v) {
          List<String> dayHorario;
          if (v is List) {
            dayHorario = v.map((item) => item.toString()).toList();
          } else if (v is String) {
            // Si es una cadena vacía o no se cumple lo esperado, se asigna una lista vacía.
            dayHorario = v.trim().isEmpty ? [] : [v];
          } else {
            // En cualquier otro caso, se puede asignar una lista vacía o lanzar una excepción
            dayHorario = [];
          }
          return MapEntry(k.toString(), dayHorario);
        });
      }

      // Procesar enlaces_propios
      Map<String, String>? enlacesPropios;
      if (json['enlaces_propios'] != null) {
        if (json['enlaces_propios'] is List) {
          // Si llega como lista (por ejemplo, lista vacía), se asigna un mapa vacío.
          enlacesPropios = {};
        } else if (json['enlaces_propios'] is Map) {
          enlacesPropios = Map.from(json['enlaces_propios'])
              .map((k, v) => MapEntry(k.toString(), v.toString()));
        }
      }

      // Adaptar contactosSecundarios:
      Map<String, String>? contactosSecundarios;
      if (json['contactos_secundarios'] != null) {
        if (json['contactos_secundarios'] is List) {
          var lista = json['contactos_secundarios'] as List;
          contactosSecundarios = {};
          for (var item in lista) {
            // Se utiliza 'titulo' como clave; en caso de nulo se pone un valor por defecto.
            String key =
                item['titulo'] != null && item['titulo'].toString().isNotEmpty
                    ? item['titulo'].toString()
                    : 'sin_titulo';
            String value = item['contacto']?.toString() ?? '';
            contactosSecundarios[key] = value;
          }
        } else {
          contactosSecundarios = Map.from(json['contactos_secundarios'])
              .map((k, v) => MapEntry(k.toString(), v.toString()));
        }
      }

      // Adaptar enlacesSociales:
      Map<String, String>? enlacesSociales;
      if (json['enlaces_sociales'] != null) {
        if (json['enlaces_sociales'] is List) {
          var lista = json['enlaces_sociales'] as List;
          enlacesSociales = {};
          for (var item in lista) {
            String key = item['plataforma']?.toString() ?? '';
            String value = item['url']?.toString() ?? '';
            if (key.isNotEmpty) {
              enlacesSociales[key] = value;
            }
          }
        } else {
          enlacesSociales = Map.from(json['enlaces_sociales'])
              .map((k, v) => MapEntry(k.toString(), v.toString()));
        }
      }

      // Adaptar precios:
      Map<String, Map<String, String>>? precios;
      if (json['precios'] != null) {
        if (json['precios'] is List) {
          var lista = json['precios'] as List;
          precios = {};
          for (var item in lista) {
            String categoria = item['categoria']?.toString() ?? '';
            Map<String, String> productos = {};
            if (item['productos'] != null && item['productos'] is Map) {
              productos = (item['productos'] as Map).map(
                (k, v) => MapEntry(k.toString(), v.toString()),
              );
            }
            if (categoria.isNotEmpty) {
              precios[categoria] = productos;
            }
          }
        } else {
          precios = (json['precios'] as Map).map((k, v) => MapEntry(
                k.toString(),
                (v as Map)
                    .map((k2, v2) => MapEntry(k2.toString(), v2.toString())),
              ));
        }
      }

      return Negocio(
        id: json['id'],
        nombre: json['nombre'],
        descripcion: json['descripcion'],
        direccion: json['direccion'],
        contacto: json['contacto'],
        contactosSecundarios: contactosSecundarios,
        categorias: categorias,
        zona: zona,
        imagenes: imagenes,
        logos: logos,
        ubicacion: json['ubicacion']?.cast<String, String>(),
        // horario: json['horario'] != null
        //     ? (json['horario'] as Map).map((k, v) {
        //         List<String> dayHorario;
        //         if (v is List) {
        //           dayHorario = v.map((item) => item.toString()).toList();
        //         } else if (v is String) {
        //           // Si es una cadena vacía o no se cumple lo esperado, se asigna una lista vacía.
        //           dayHorario = v.trim().isEmpty ? [] : [v];
        //         } else {
        //           // En cualquier otro caso, se puede asignar una lista vacía o lanzar una excepción
        //           dayHorario = [];
        //         }
        //         return MapEntry(k.toString(), dayHorario);
        //       })
        //     : null,
        horario: horario,
        enlacesPropios: enlacesPropios,
        enlacesSociales: enlacesSociales,
        precios: precios,
        suscripcion: json['suscripcion'],
        eventos: eventos,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error al procesar el JSON para Negocio: $json');
        print('Error: $e');
      }
      if (e is! Exception) {
        throw Exception('Error al procesar JSON: $e');
      }
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'id': id,
      'nombre': nombre,
      'direccion': direccion,
      'contacto': contacto,
      'suscripcion': suscripcion,
      // 'enlaces_sociales': enlacesSociales,
      // 'precios': precios,
    };

    if (descripcion != null) data['descripcion'] = descripcion;

    if (contactosSecundarios != null) {
      data['contactos_secundarios'] = contactosSecundarios;
    }

    if (categorias != null) {
      data['categorias'] = categorias!.map((e) => e.toJson()).toList();
    }

    if (zona != null) {
      data['zona'] = zona!.toJson();
    }

    if (eventos != null) {
      data['eventos'] = eventos!.map((e) => e.toJson()).toList();
    }

    if (imagenes != null) {
      data['imagenes'] = imagenes!.map((e) => e.toJson()).toList();
    }

    if (logos != null) {
      data['logos'] = logos!.map((e) => e.toJson()).toList();
    }

    if (ubicacion != null) data['ubicacion'] = ubicacion;
    if (horario != null) data['horario'] = horario;
    if (enlacesPropios != null) data['enlaces_propios'] = enlacesPropios;
    if (enlacesSociales != null) data['enlaces_sociales'] = enlacesSociales;
    if (precios != null) data['precios'] = precios;

    return data;
  }

/*
  static Map<String, String>? _adaptarEnlacesSocialesParaImportar(
      Map<String, dynamic>? json) {
    Map<String, String>? enlacesSociales;
    List<Map<String, dynamic>> links =
        List<Map<String, dynamic>>.from(json?['enlaces_sociales'] ?? []);
    for (var link in links) {
      enlacesSociales ??= {}; // Inicializar si es nulo
      enlacesSociales[link['plataforma'] ?? ''] = link['url'] ?? '';
    }

    return enlacesSociales;
  }

  static Map<String, Map<String, String>>? _adaptarPreciosParaImportar(
      Map<String, dynamic>? json) {
    Map<String, Map<String, String>>? precios;
    List<Map<String, dynamic>> preciosList =
        List<Map<String, dynamic>>.from(json?['precios'] ?? []);
    for (var precio in preciosList) {
      precios ??= {}; // Inicializar si es nulo
      precios[precio['categoria'] ?? ''] =
          Map<String, String>.from(precio['productos'] ?? {});
    }

    return precios;
  }
*/
  @override
  String toString() {
    String categoriasStr = categorias != null
        ? categorias!
            .map((cat) => '{ id: ${cat.id}, nombre: ${cat.nombre} }')
            .join(', ')
        : '';
    String negocioStr =
        'Negocio(id: $id, nombre: $nombre, descripcion: $descripcion, direccion: $direccion, contacto: $contacto, contactosSecundarios: $contactosSecundarios, categorias: {$categoriasStr}, zona: $zona, imagenes: $imagenes, logos: $logos, ubicacion: $ubicacion, horario: $horario, enlacesPropios: $enlacesPropios, enlacesSociales: $enlacesSociales, precios: $precios)';

    return negocioStr;
  }

  bool isOpenNow({DateTime? dateTime}) {
    final currentTime = dateTime ?? DateTime.now();

    if (horario == null || horario!.isEmpty) return false;

    // Se obtiene el tramo horario actual, considerando tramos del día actual o del día anterior
    String? tramoHorarioActual = getTramoHorario(dateTime: currentTime);

    // Si se detecta un tramo actual, se considera que el negocio está abierto
    return tramoHorarioActual != null;
  }

  String? getTramoHorario({DateTime? dateTime}) {
    if (horario == null || horario!.isEmpty) return null;

    final current = (dateTime ?? DateTime.now()).toLocal();
    final nowTime = TimeOfDay.fromDateTime(current);
    final nowMinutes = nowTime.hour * 60 + nowTime.minute;

    // 1. Verificar el horario del día actual
    String today = DateFormat('EEEE', 'es_ES').format(current).toLowerCase();
    if (horario!.containsKey(today)) {
      for (String rango in horario![today] ?? []) {
        List<String> parts = rango.split('-');
        if (parts.length != 2) continue;

        String startStr = parts[0].trim();
        String endStr = parts[1].trim();
        if (startStr.isEmpty || endStr.isEmpty) continue;

        TimeOfDay startTime = CoreService.parseTimeOfDay(startStr);
        TimeOfDay endTime = CoreService.parseTimeOfDay(endStr);
        int startMinutes = startTime.hour * 60 + startTime.minute;
        int endMinutes = endTime.hour * 60 + endTime.minute;

        // Caso especial: rango 24 horas (por ejemplo, "00:00-00:00")
        if (startMinutes == endMinutes) return rango;

        // Si el rango cruza la medianoche y el tiempo de consulta es menor que la hora de inicio,
        // se ignora este tramo en el día actual (ese tramo se evaluará en el día anterior)
        if (endMinutes <= startMinutes && nowMinutes < startMinutes) {
          continue;
        }

        // Evaluar el rango con la función auxiliar. En este caso se considera que CoreService.isTimeInRange
        // retorna true solo si el tiempo actual está estrictamente dentro del tramo, es decir,
        // que si el tiempo actual es menor que el inicio, retorna false.
        if (CoreService.isTimeInRange(nowTime, startTime, endTime)) {
          return rango;
        }
      }
    }

    // 2. Verificar el horario del día anterior (solo se consideran tramos que cruzan la medianoche)
    DateTime yesterday = current.subtract(const Duration(days: 1));
    String yesterdayName =
        DateFormat('EEEE', 'es_ES').format(yesterday).toLowerCase();

    if (horario!.containsKey(yesterdayName)) {
      for (String rango in horario![yesterdayName] ?? []) {
        List<String> parts = rango.split('-');
        if (parts.length != 2) continue;

        String startStr = parts[0].trim();
        String endStr = parts[1].trim();
        if (startStr.isEmpty || endStr.isEmpty) continue;

        TimeOfDay startTime = CoreService.parseTimeOfDay(startStr);
        TimeOfDay endTime = CoreService.parseTimeOfDay(endStr);
        int startMinutes = startTime.hour * 60 + startTime.minute;
        int endMinutes = endTime.hour * 60 + endTime.minute;

        // Solo se evalúan los rangos que cruzan la medianoche
        if (endMinutes <= startMinutes) {
          // En este caso, se espera que el tiempo actual (del día en curso) sea menor o igual que la hora de cierre,
          // de modo que si por ejemplo el cierre es a las 03:00 (180 minutos) y la hora actual es 03:00, se considere abierto.
          if (nowMinutes <= endMinutes) {
            return rango;
          }
        }
      }
    }

    // Si no se detecta un tramo activo, se retorna null
    return null;
  }

  String? getAllHorariosHoy() {
    if (horario == null || horario!.isEmpty) return null;

    // Obtener la fecha y hora actual
    DateTime now = DateTime.now().toLocal();
    // Obtener el día actual
    String today = DateFormat('EEEE', 'es_ES').format(now).toLowerCase();

    // Lista para almacenar todos los tramos horarios aplicables hoy
    List<String> tramosHoy = [];

    // Añadir los tramos propios del día actual
    if (horario!.containsKey(today)) {
      tramosHoy.addAll(horario![today] ?? []);
    }

    // Verificar tramos del día anterior que se extienden hasta hoy
    DateTime yesterday = now.subtract(const Duration(days: 1));
    String yesterdayName =
        DateFormat('EEEE', 'es_ES').format(yesterday).toLowerCase();

    if (horario!.containsKey(yesterdayName)) {
      // Calcular el total de minutos transcurridos en el día actual
      int nowMinutes = now.hour * 60 + now.minute;

      for (String rango in horario![yesterdayName] ?? []) {
        List<String> horas = rango.split('-');
        if (horas.length != 2) continue;

        String horarioInicio = horas[0].trim();
        String horarioFin = horas[1].trim();
        if (horarioInicio.isEmpty || horarioFin.isEmpty) continue;

        TimeOfDay inicioTime = CoreService.parseTimeOfDay(horarioInicio);
        TimeOfDay finTime = CoreService.parseTimeOfDay(horarioFin);

        // Convertir los horarios a minutos para compararlos
        int inicioMinutos = inicioTime.hour * 60 + inicioTime.minute;
        int finMinutos = finTime.hour * 60 + finTime.minute;

        // Si el rango cruza la medianoche (el fin es menor o igual que el inicio)
        // verificamos si el tiempo actual (en minutos) es menor que el fin.
        if (finMinutos <= inicioMinutos && nowMinutes < finMinutos) {
          // Se añade el tramo solo si estamos dentro del periodo extendido hasta hoy
          tramosHoy.add(
              "$horarioInicio-$horarioFin (desde ${_capitalize(yesterdayName)})");
        }
      }
    }

    // Si no hay tramos para hoy, devolver null
    if (tramosHoy.isEmpty) return null;

    // Devolver todos los tramos concatenados
    return tramosHoy.join(', ');
  }

  double? calcularDistancia(LatLng coordenadas) {
    if (ubicacion == null) {
      return null; // No se puede calcular la distancia si no hay ubicación registrada
    }

    try {
      // Obtener las coordenadas del negocio usando el método existente
      LatLng negocioCoordenadas = CoreService.getLatLng(this);

      // Llamar al método para calcular la distancia en metros
      return CoreService.calcularDistanciaEnMetros(
          negocioCoordenadas.latitude,
          negocioCoordenadas.longitude,
          coordenadas.latitude,
          coordenadas.longitude);
    } catch (e) {
      if (kDebugMode) {
        print('Error al calcular distancia: $e');
      }
      return null;
    }
  }

// Función auxiliar para capitalizar la primera letra
  String _capitalize(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1);
  }
}
