<?php

namespace Database\Factories;

use App\Models\Negocio;
use App\Models\Suscripcion;
use App\Enums\TipoSuscripcion;
use App\Enums\EstadoSuscripcion;
use Illuminate\Database\Eloquent\Factories\Factory;

class SuscripcionFactory extends Factory
{
    protected $model = Suscripcion::class;

    public function definition(): array
    {
        return [
            'negocio_id' => Negocio::factory(), // Por defecto crea un Negocio
            'plan' => $this->faker->randomElement(TipoSuscripcion::cases())->value,
            'status' => $this->faker->boolean(85)
                ? EstadoSuscripcion::ACTIVE
                : $this->faker->randomElement(
                    array_filter(EstadoSuscripcion::cases(), fn($estado) => $estado !== EstadoSuscripcion::ACTIVE)
                ),
            'started_at' => now(),
            'ends_at' => $this->faker->dateTimeBetween('+1 month', '+6 months'),
        ];
    }
}
