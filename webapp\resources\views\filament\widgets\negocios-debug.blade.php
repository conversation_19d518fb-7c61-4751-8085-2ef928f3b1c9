@php
if ($_flag) {
    dd("RENDER despues de ejecutar funcion refreshNegociosDebug()", $negocios, $filters);
}
@endphp
<x-filament-widgets::widget>
    <div class="p-4 space-y-2 bg-black border rounded">
        <h2 class="text-lg font-bold text-white">Filtros</h2>
        <pre class="text-white">{{ json_encode($filters, JSON_PRETTY_PRINT) }}</pre>
        <h2 class="text-lg font-bold text-white">Debug - Negocios Filtrados</h2>
        <br>
        <br>
        @if($negocios != null)
            @foreach($negocios as $negocio)
            {{-- <div class="text-white">{{ $negocio }} </div> --}}
                <div class="text-white">{{ $negocio['nombre'] }} ({{ $negocio['zona']['nombre'] }}) 
                < {{ $negocio['categoria']['nombre'] }} >
                => {{ $negocio['ubicacion']['latitud'] }}, {{ $negocio['ubicacion']['longitud'] }} </div>
            @endforeach
        @else
            <p class="text-white">No hay negocios</p>
        @endif
    </div>
</x-filament-widgets::widget>