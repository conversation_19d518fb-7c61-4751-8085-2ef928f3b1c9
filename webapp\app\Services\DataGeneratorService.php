<?php

namespace App\Services;

use Faker\Factory as Faker;

class DataGeneratorService
{
    protected $faker;
    private static $dataPath = __DIR__ . '/dummy_data/';

    /**
     * Lee un archivo JSON y retorna su contenido decodificado
     */
    private static function readJsonFile(string $filename): array
    {
        $filepath = self::$dataPath . $filename;

        if (!file_exists($filepath)) {
            throw new \Exception("El archivo {$filename} no existe en " . self::$dataPath);
        }

        $content = file_get_contents($filepath);
        $data = json_decode($content, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception("Error al decodificar JSON de {$filename}: " . json_last_error_msg());
        }

        return $data;
    }

    public static function getRolesEsenciales(): array
    {
        return [
            'owner',
            'admin',
            'cliente',
            'usuario',
        ];
    }

    public static function getPermisosEsenciales(): array
    {
        return self::readJsonFile('permisos_esenciales.json');
    }

    public static function getLocalidades(): array
    {
        return self::readJsonFile('localidades.json');
    }

    public static function getZonas(string $localidad = "Matalascañas"): array
    {
        return self::readJsonFile('zonas.json')[$localidad] ?? [];
    }

    public static function getCategorias(): array
    {
        return self::readJsonFile('categorias.json');
    }

    public static function getNombreNegocio(string $categoria): array
    {
        $negociosData = self::readJsonFile('negocios.json');

        if (isset($negociosData[$categoria])) {
            return $negociosData[$categoria];
        }

        return ['Negocio Ficticio'];
    }

    public static function getNombreEvento(string $categoria): array
    {
        $eventosData = self::readJsonFile('eventos.json');

        if (isset($eventosData[$categoria])) {
            return $eventosData[$categoria];
        }

        return ['Evento Ficticio'];
    }

    /**
     * Genera un punto aleatorio dentro de un polígono usando bounding box + ray casting.
     */
    public static function getRandomPointInPolygon(string $localidad = "Matalascañas"): array
    {
        $localidades = self::getLocalidades();

        // Reindexa el array por el campo 'nombre'
        $localidades = array_column($localidades, null, 'nombre');

        $polygon = $localidades[$localidad]['polygon'] ?? $localidades['Matalascañas']['polygon'];

        // 1) Calcular bounding box
        $lats = array_column($polygon, 0);
        $lngs = array_column($polygon, 1);

        $minLat = min($lats);
        $maxLat = max($lats);
        $minLng = min($lngs);
        $maxLng = max($lngs);

        $faker = Faker::create();

        // 2) Repetir hasta encontrar un punto dentro del polígono
        do {
            $lat = $faker->randomFloat(6, $minLat, $maxLat);
            $lng = $faker->randomFloat(6, $minLng, $maxLng);
        } while (!self::pointInPolygon([$lat, $lng], $polygon));

        return [$lat, $lng];
    }

    /**
     * Verifica si un punto (lat, lng) está dentro de un polígono (array de [lat, lng])
     * usando el algoritmo de ray casting.
     */
    private static function pointInPolygon(array $point, array $polygon): bool
    {
        [$lat, $lng] = $point;
        $inside = false;
        $numVertices = count($polygon);

        for ($i = 0, $j = $numVertices - 1; $i < $numVertices; $j = $i++) {
            $latI = $polygon[$i][0];
            $lngI = $polygon[$i][1];
            $latJ = $polygon[$j][0];
            $lngJ = $polygon[$j][1];

            $intersect = (($lngI > $lng) !== ($lngJ > $lng)) &&
                ($lat < ($latJ - $latI) * ($lng - $lngI) / ($lngJ - $lngI) + $latI);
            if ($intersect) {
                $inside = !$inside;
            }
        }

        return $inside;
    }
}
