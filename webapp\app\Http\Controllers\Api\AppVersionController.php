<?php

namespace App\Http\Controllers\Api;

use App\Models\AppVersion;
use App\Services\CacheService;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
use App\Http\Requests\StoreAppVersionRequest;
use App\Http\Requests\UpdateAppVersionRequest;

class AppVersionController extends Controller
{

    protected $cacheService;

    public function __construct(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $versiones = Cache::rememberForever('app_versions_all', function () {
            return AppVersion::all();
        });

        return $this->cacheService->handleCacheableResponse(
            $versiones,
            function ($data) {
                return $data;
            }
        );
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreAppVersionRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(AppVersion $appVersion)
    {
        return $this->cacheService->handleCacheableResponse(
            $appVersion,
            function ($data) {
                return $data;
            }
        );
    }

    public function getIosVersion()
    {
        $version = AppVersion::where('platform', 'ios')->first();

        return $this->show($version);
    }

    public function getAndroidVersion()
    {
        $version = AppVersion::where('platform', 'android')->first();

        return $this->show($version);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AppVersion $appVersion)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateAppVersionRequest $request, AppVersion $appVersion)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AppVersion $appVersion)
    {
        //
    }
}
