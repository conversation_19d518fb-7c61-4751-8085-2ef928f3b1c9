<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Zona extends Model
{
    /** @use HasFactory<\Database\Factories\ZonaFactory> */
    use HasFactory;

    protected $fillable = ['nombre', 'descripcion', 'coordenadas', 'localidad_id'];

    protected $casts = [
        'coordenadas' => 'array',
    ];

    public function negocios(): HasMany
    {
        return $this->hasMany(Negocio::class);
    }

    public function localidad()
    {
        return $this->belongsTo(Localidad::class);
    }
}
