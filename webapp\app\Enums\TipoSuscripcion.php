<?php

namespace App\Enums;

enum TipoSuscripcion: string
{
    case FREE = 'free';
    case BASIC = 'basic';
    // case PREMIUM = 'premium';
    // case LIFETIME = 'lifetime';

    public function message(): string
    {
        return match ($this) {
            self::FREE => 'Suscripción gratuita',
            self::BASIC => 'Suscripción básica',
            // self::PREMIUM => 'Suscripción premium',
            // self::LIFETIME => 'Suscripción de por vida',
        };
    }

    public function label(): string
    {
        return match ($this) {
            self::FREE => 'Gratis',
            self::BASIC => 'Básica',
            // self::PREMIUM => 'Premium',
            // self::LIFETIME => 'Lifetime',
        };
    }

    public function isBillable(): bool
    {
        return match ($this) {
            self::FREE => false,
            self::BASIC => true,
            // self::PREMIUM => true,
            // self::LIFETIME => false,
        };
    }

    public function price(): float
    {
        return match ($this) {
            self::FREE => 0.0,
            self::BASIC => 80.0,
            // self::PREMIUM => 120.0,
            // self::LIFETIME => 0.0,
        };
    }

    public static function getAsOptions(): array
    {
        $options = [];
        foreach (self::cases() as $case) {
            $options[$case->value] = $case->label();
        }

        return $options;
    }
}
