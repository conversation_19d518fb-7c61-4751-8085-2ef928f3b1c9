import 'package:flutter/foundation.dart';
import 'package:mia/models/media.dart';
import 'package:mia/services/global_data_service.dart';
import 'package:mia/utils/treeable.dart';

class Categoria implements Treeable {
  final int id;
  final String nombre;
  final String? descripcion;
  final int? parentId;
  final int nivel;
  final int orden;

  final List<Media>? iconos;

  Categoria({
    required this.id,
    required this.nombre,
    this.descripcion,
    this.iconos,
    this.parentId,
    required this.nivel, // Nivel requerido en el constructor
    required this.orden,
  });

  @override
  Treeable createDummyNode() =>
      Categoria(id: -1, nombre: 'N/A', descripcion: '', nivel: 0, orden: 0);

  @override
  int getId() => id;

  @override
  int? getIdPadre() => parentId;

// Modificación a la función Categoria.fromJson
  factory Categoria.fromJson(Map<String, dynamic> json) {
    try {
      // Validar campos requeridos primero
      if (!json.containsKey('id') || json['id'] == null) {
        throw Exception('El campo id es requerido');
      }
      if (!json.containsKey('nombre') || json['nombre'] == null) {
        throw Exception('El campo nombre es requerido');
      }

      // Validar tipos
      if (json['id'] is! int) {
        throw Exception('El campo id debe ser un entero');
      }
      if (json['nombre'] is! String) {
        throw Exception('El campo nombre debe ser una cadena');
      }

      // Obtener parent_id y calcular nivel
      int? parentId = json['parent_id'];

      // Si nivel está explícitamente en el JSON, úsalo; de lo contrario, calcula
// Determinar el nivel: usar el valor del JSON si existe, o calcularlo
      int nivel;
      if (json.containsKey('nivel') && json['nivel'] != null) {
        if (json['nivel'] is! int) {
          throw Exception('El campo nivel debe ser un entero');
        }
        nivel = json['nivel'];
      } else {
        // Si no hay nivel, calcularlo basado en parent_id
        nivel = _calcularNivel(parentId);
      }

      int orden = json['order'] ?? 0;

      // Deserialización de imágenes (ahora opcional)
      List<Media>? iconos;
      if (json['iconos'] != null) {
        iconos = (json['iconos'] as List)
            .map((data) => Media.fromJson(data))
            .toList();
      }

      return Categoria(
        id: json['id'],
        nombre: json['nombre'],
        descripcion: json['descripcion'] ?? '',
        orden: orden,
        parentId: parentId,
        iconos: iconos,
        nivel: nivel,
      );
    } catch (e) {
      if (e is Exception) {
        // Si ya es Exception, relanzarla
        if (kDebugMode) {
          print('Error al procesar el JSON para Categoria: $json');
          print('Error: $e');
        }
        rethrow;
      } else {
        // Si no es Exception, convertirla a Exception
        if (kDebugMode) {
          print('Error al procesar el JSON para Categoria: $json');
          print('Error: $e');
        }
        throw Exception('Error al procesar JSON: $e');
      }
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nombre': nombre,
      'descripcion': descripcion,
      'parent_id': parentId,
      'nivel': nivel,
      'orden': orden,
    };
  }

  static Categoria? find(List<Categoria> categorias, int id) {
    try {
      return categorias.firstWhere((cat) => cat.id == id);
    } catch (e) {
      return null;
    }
  }

  Categoria? padre(List<Categoria> categorias) {
    if (parentId == null) {
      return null; // No tiene padre
    }
    return find(categorias, parentId!);
  }

  Map<int, Categoria> antecesores(List<Categoria> categorias) {
    Map<int, Categoria> antecesoresMap = {};
    _buscarAntecesoresRecursivamente(categorias, antecesoresMap);
    return antecesoresMap;
  }

  void _buscarAntecesoresRecursivamente(
      List<Categoria> categorias, Map<int, Categoria> antecesoresMap) {
    if (parentId != null) {
      final padre = find(categorias, parentId!);
      if (padre != null) {
        antecesoresMap[padre.nivel] = padre; // Añade el padre al mapa
        padre._buscarAntecesoresRecursivamente(
            categorias, antecesoresMap); // Recursión para el abuelo, etc.
      }
    }
  }

  List<Categoria> hijos(List<Categoria> categorias) {
    return categorias.where((cat) => cat.parentId == id).toList();
  }

  Map<int, List<Categoria>> descendientes(List<Categoria> categorias) {
    Map<int, List<Categoria>> descendientesMap = {};
    _buscarDescendientesRecursivamente(
        categorias, descendientesMap, nivel + 1); // Empezar un nivel más abajo
    return descendientesMap;
  }

  void _buscarDescendientesRecursivamente(List<Categoria> categorias,
      Map<int, List<Categoria>> descendientesMap, int nivelActual) {
    final hijos = categorias.where((cat) => cat.parentId == id).toList();

    if (hijos.isNotEmpty) {
      descendientesMap
          .putIfAbsent(nivelActual, () => [])
          .addAll(hijos); // Añadir hijos al mapa
    }

    for (final hijo in hijos) {
      hijo._buscarDescendientesRecursivamente(categorias, descendientesMap,
          nivelActual + 1); // Recursión para nietos, etc.
    }
  }

  List<Categoria> hermanos(List<Categoria> categorias) {
    if (parentId == null) {
      return categorias
          .where((cat) => cat.parentId == null && cat.id != id)
          .toList();
    } else {
      return categorias
          .where((cat) => cat.parentId == parentId && cat.id != id)
          .toList();
    }
  }

  static int _calcularNivel(int? parentId) {
    if (parentId == null) {
      return 1; // Nivel 1 si no tiene padre
    }

    // Busca la categoría padre en GlobalDataService para calcular el nivel
    final padre = GlobalDataService().categorias?.firstWhere(
          (cat) => cat.id == parentId,
          orElse: () => Categoria(
            id: -1,
            nombre: "Categoria no encontrada",
            descripcion: "",
            nivel: 1,
            orden: 0,
          ),
        );

    if (padre != null && padre.id != -1) {
      return padre.nivel + 1; // Nivel del padre + 1
    } else {
      return 1; // Si no se encuentra el padre, asume nivel 1
    }
  }

  @override
  String toString() {
    return 'Categoria(id: $id, nombre: $nombre, descripcion: $descripcion, parentId: $parentId, nivel: $nivel, iconos: $iconos)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Categoria && other.id == id && other.nombre == nombre;
  }

  @override
  int get hashCode => Object.hash(id, nombre);
}
