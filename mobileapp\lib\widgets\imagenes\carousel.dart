import 'package:flutter/material.dart';
import 'package:mia/config/colors.dart';
import 'package:mia/models/media.dart';
import 'package:mia/services/core_service.dart';
import 'package:mia/widgets/imagenes/blurred_image_with_overlay.dart';

class Carousel extends StatefulWidget {
  final List<Media> imagenes;
  final List<String>? tags;

  const Carousel({super.key, required this.imagenes, this.tags});

  @override
  State<Carousel> createState() => _CarouselState();
}

class _CarouselState extends State<Carousel> {
  late PageController _pageController;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentPage = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Carrusel de imágenes con Stack
        Stack(
          children: [
            SizedBox(
              height: 200.0, // Puedes ajustar la altura según lo necesites
              child: PageView.builder(
                controller: _pageController,
                itemCount: widget.imagenes.length,
                onPageChanged: _onPageChanged,
                itemBuilder: (context, index) {
                  return BlurredImageWithOverlay(
                    imagen: widget.imagenes[index],
                    height: 200.0,
                  );
                },
              ),
            ),
            Positioned(
              bottom: 10.0, // Puedes ajustar esta distancia
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  widget.imagenes.length,
                  (index) => Container(
                    margin: EdgeInsets.symmetric(horizontal: 4.0),
                    width: _currentPage == index ? 12.0 : 8.0,
                    height: _currentPage == index ? 12.0 : 8.0,
                    decoration: BoxDecoration(
                      color: _currentPage == index
                          ? AppColors.current.secondaryColor
                          : AppColors.current.textColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ),
            ),
            Positioned(
                top: 1.0, // Puedes ajustar esta distancia
                left: 0,
                right: 0,
                child: CoreService.buildTagList(
                    tags: widget.tags,
                    backgroundColorDefault: AppColors.current.primaryColor,
                    textColorDefault: AppColors.current.textColor)),
          ],
        ),
      ],
    );
  }
}
