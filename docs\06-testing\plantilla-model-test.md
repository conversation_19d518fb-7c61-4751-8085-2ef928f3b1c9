# Plantilla: Test de Modelo (Frontend Flutter)

## Plantilla Base

```dart
import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:mia/models/{model_name}.dart';
import '../../helpers/base_test_helper.dart';
import 'utils/model_test_utils.dart';

void main() async {
  TestWidgetsFlutterBinding.ensureInitialized();
  await BaseTest.initialize();

  group('{ModelName} Model Tests', () {
    // ========================================
    // TESTS COMUNES (usando ModelTestUtils)
    // ========================================
    
    ModelTestUtils.runCommonModelTests<{ModelName}>(
      validFixturePaths: [
        '{model_name}/valid_{model_name}_completo.json',
        '{model_name}/valid_{model_name}_minimo.json',
        '{model_name}/valid_{model_name}_parcial.json',
      ],
      invalidFixturePaths: [
        '{model_name}/invalid_{model_name}_missing_id.json',
        '{model_name}/invalid_{model_name}_missing_nombre.json',
        // ... más casos inválidos
      ],
      fromJson: {ModelName}.fromJson,
      toJson: (model) => model.toJson(),
      getExpectedValues: (fixturePath) => _getExpectedValues(fixturePath),
      customModel: {
        'id': 1,
        'nombre': 'Test {ModelName}',
        // ... campos mínimos requeridos
      },
      requiredFields: ['id', 'nombre'], // Campos obligatorios
    );

    // ========================================
    // TESTS ESPECÍFICOS DEL MODELO
    // ========================================
    
    _runSpecificTests();
  });
}

// ========================================
// HELPER: Valores esperados por fixture
// ========================================

Map<String, dynamic> _getExpectedValues(String fixturePath) {
  if (fixturePath.contains('completo')) {
    return {
      'id': 1,
      'nombre': 'Nombre Completo',
      // ... todos los campos del fixture completo
    };
  } else if (fixturePath.contains('minimo')) {
    return {
      'id': 2,
      'nombre': 'Nombre Mínimo',
      // ... solo campos requeridos
    };
  } else if (fixturePath.contains('parcial')) {
    return {
      'id': 3,
      'nombre': 'Nombre Parcial',
      // ... algunos campos opcionales
    };
  }
  
  return {};
}

// ========================================
// TESTS ESPECÍFICOS
// ========================================

void _runSpecificTests() {
  group('Serialización de campos específicos', () {
    
    test('Debe parsear campo_complejo correctamente', () {
      final json = {
        'id': 1,
        'nombre': 'Test',
        'campo_complejo': {
          'subcampo1': 'valor1',
          'subcampo2': 'valor2',
        },
      };

      final model = {ModelName}.fromJson(json);

      expect(model.campoComplejo, isNotNull);
      expect(model.campoComplejo!['subcampo1'], equals('valor1'));
      expect(model.campoComplejo!['subcampo2'], equals('valor2'));
    });

    test('Debe manejar campo_complejo nulo', () {
      final json = {
        'id': 1,
        'nombre': 'Test',
        'campo_complejo': null,
      };

      final model = {ModelName}.fromJson(json);

      expect(model.campoComplejo, isNull);
    });

    test('Debe parsear lista_de_items correctamente', () {
      final json = {
        'id': 1,
        'nombre': 'Test',
        'items': [
          {'id': 1, 'nombre': 'Item 1'},
          {'id': 2, 'nombre': 'Item 2'},
        ],
      };

      final model = {ModelName}.fromJson(json);

      expect(model.items, isNotNull);
      expect(model.items!.length, equals(2));
      expect(model.items![0].id, equals(1));
      expect(model.items![1].nombre, equals('Item 2'));
    });

    test('Debe manejar lista_de_items vacía', () {
      final json = {
        'id': 1,
        'nombre': 'Test',
        'items': [],
      };

      final model = {ModelName}.fromJson(json);

      expect(model.items, isNotNull);
      expect(model.items!.length, equals(0));
    });
  });

  group('Validación de tipos', () {
    
    test('Debe rechazar id como string', () {
      final json = {
        'id': '1', // String en lugar de int
        'nombre': 'Test',
      };

      expect(() => {ModelName}.fromJson(json), throwsA(isA<TypeError>()));
    });

    test('Debe rechazar nombre como número', () {
      final json = {
        'id': 1,
        'nombre': 123, // Número en lugar de string
      };

      expect(() => {ModelName}.fromJson(json), throwsA(isA<TypeError>()));
    });
  });

  group('Casos edge', () {
    
    test('Debe manejar strings vacíos', () {
      final json = {
        'id': 1,
        'nombre': '',
        'descripcion': '',
      };

      final model = {ModelName}.fromJson(json);

      expect(model.nombre, equals(''));
      expect(model.descripcion, equals(''));
    });

    test('Debe manejar valores null en campos opcionales', () {
      final json = {
        'id': 1,
        'nombre': 'Test',
        'descripcion': null,
        'campo_opcional': null,
      };

      final model = {ModelName}.fromJson(json);

      expect(model.descripcion, isNull);
      expect(model.campoOpcional, isNull);
    });

    test('Debe manejar JSON con campos extra', () {
      final json = {
        'id': 1,
        'nombre': 'Test',
        'campo_no_esperado': 'valor',
        'otro_campo_extra': 123,
      };

      // No debe lanzar excepción
      expect(() => {ModelName}.fromJson(json), returnsNormally);
      
      final model = {ModelName}.fromJson(json);
      expect(model.id, equals(1));
      expect(model.nombre, equals('Test'));
    });
  });

  group('Serialización bidireccional', () {
    
    test('fromJson -> toJson debe ser idempotente', () {
      final originalJson = {
        'id': 1,
        'nombre': 'Test',
        'descripcion': 'Descripción de prueba',
      };

      final model = {ModelName}.fromJson(originalJson);
      final serializedJson = model.toJson();
      final modelFromSerialized = {ModelName}.fromJson(serializedJson);

      expect(modelFromSerialized.id, equals(model.id));
      expect(modelFromSerialized.nombre, equals(model.nombre));
      expect(modelFromSerialized.descripcion, equals(model.descripcion));
    });

    test('toJson debe incluir todos los campos no nulos', () {
      final model = {ModelName}.fromJson({
        'id': 1,
        'nombre': 'Test',
        'descripcion': 'Descripción',
      });

      final json = model.toJson();

      expect(json.containsKey('id'), isTrue);
      expect(json.containsKey('nombre'), isTrue);
      expect(json.containsKey('descripcion'), isTrue);
    });

    test('toJson debe omitir campos nulos (si aplica)', () {
      final model = {ModelName}.fromJson({
        'id': 1,
        'nombre': 'Test',
        'descripcion': null,
      });

      final json = model.toJson();

      // Dependiendo de la implementación
      // expect(json.containsKey('descripcion'), isFalse);
      // O
      // expect(json['descripcion'], isNull);
    });
  });

  group('Relaciones con otros modelos', () {
    
    test('Debe parsear relación anidada correctamente', () {
      final json = {
        'id': 1,
        'nombre': 'Test',
        'relacion': {
          'id': 10,
          'nombre': 'Relación Test',
        },
      };

      final model = {ModelName}.fromJson(json);

      expect(model.relacion, isNotNull);
      expect(model.relacion!.id, equals(10));
      expect(model.relacion!.nombre, equals('Relación Test'));
    });

    test('Debe manejar relación nula', () {
      final json = {
        'id': 1,
        'nombre': 'Test',
        'relacion': null,
      };

      final model = {ModelName}.fromJson(json);

      expect(model.relacion, isNull);
    });
  });
}
```

## Estructura de Fixtures

Crea los siguientes archivos JSON en `test/fixture/json/{model_name}/`:

### valid_{model_name}_completo.json
```json
{
  "id": 1,
  "nombre": "Nombre Completo",
  "descripcion": "Descripción completa del modelo",
  "campo_opcional": "Valor opcional",
  "campo_complejo": {
    "subcampo1": "valor1",
    "subcampo2": "valor2"
  },
  "items": [
    {"id": 1, "nombre": "Item 1"},
    {"id": 2, "nombre": "Item 2"}
  ],
  "relacion": {
    "id": 10,
    "nombre": "Relación"
  }
}
```

### valid_{model_name}_minimo.json
```json
{
  "id": 2,
  "nombre": "Nombre Mínimo"
}
```

### valid_{model_name}_parcial.json
```json
{
  "id": 3,
  "nombre": "Nombre Parcial",
  "descripcion": "Solo algunos campos opcionales"
}
```

### invalid_{model_name}_missing_id.json
```json
{
  "nombre": "Sin ID"
}
```

### invalid_{model_name}_missing_nombre.json
```json
{
  "id": 1
}
```

### invalid_{model_name}_id_type.json
```json
{
  "id": "uno",
  "nombre": "ID con tipo incorrecto"
}
```

## Checklist de Tests

Al crear tests para un nuevo modelo, asegúrate de cubrir:

### Serialización Básica
- [ ] Deserializa JSON válido completo
- [ ] Deserializa JSON válido mínimo
- [ ] Deserializa JSON válido parcial
- [ ] Rechaza JSON con campos requeridos faltantes
- [ ] Rechaza JSON con tipos incorrectos

### Campos Específicos
- [ ] Parsea campos complejos (Maps, Lists)
- [ ] Maneja campos nulos correctamente
- [ ] Maneja listas vacías
- [ ] Maneja strings vacíos

### Validación de Tipos
- [ ] Rechaza tipos incorrectos en campos requeridos
- [ ] Maneja conversiones de tipos (si aplica)

### Casos Edge
- [ ] Maneja JSON con campos extra
- [ ] Maneja valores límite (números muy grandes, strings muy largos)
- [ ] Maneja caracteres especiales en strings

### Serialización Bidireccional
- [ ] fromJson -> toJson es idempotente
- [ ] toJson incluye todos los campos necesarios
- [ ] toJson maneja campos nulos correctamente

### Relaciones
- [ ] Parsea relaciones anidadas
- [ ] Maneja relaciones nulas
- [ ] Parsea listas de relaciones

## Reemplazos Necesarios

Al usar esta plantilla, reemplaza:

- `{ModelName}` → Nombre del modelo en PascalCase (ej: `Negocio`, `Evento`)
- `{model_name}` → Nombre del modelo en snake_case (ej: `negocio`, `evento`)

## Ejemplo de Uso

```bash
# Crear estructura de directorios
mkdir -p test/fixture/json/evento

# Crear fixtures
touch test/fixture/json/evento/valid_evento_completo.json
touch test/fixture/json/evento/valid_evento_minimo.json
touch test/fixture/json/evento/invalid_evento_missing_id.json

# Crear test
touch test/unit/models/evento_test.dart

# Ejecutar tests
flutter test test/unit/models/evento_test.dart
```

## Consejos

1. **Fixtures realistas**: Usa datos reales de tu API para crear fixtures
2. **Cobertura completa**: Cubre todos los tipos de campos (primitivos, complejos, listas)
3. **Casos edge**: No olvides probar valores límite y casos especiales
4. **Mantenimiento**: Actualiza fixtures cuando cambies la API
5. **Documentación**: Comenta casos de test complejos

## Debugging

Si un test falla:

```dart
test('Debug test', () {
  final json = BaseTest.fixture('evento/valid_evento_completo.json');
  print('JSON: $json');
  
  final decoded = jsonDecode(json);
  print('Decoded: $decoded');
  
  try {
    final model = Evento.fromJson(decoded);
    print('Model: $model');
  } catch (e, stackTrace) {
    print('Error: $e');
    print('StackTrace: $stackTrace');
    rethrow;
  }
});
```

