<?php

namespace App\Filament\Resources;

use App\Enums\EstadoPago;
use App\Enums\MetodoPago;
use App\Filament\Resources\PagoSuscripcionResource\Pages;
use App\Filament\Resources\PagoSuscripcionResource\RelationManagers;
use App\Models\PagoSuscripcion;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PagoSuscripcionResource extends Resource
{
    protected static ?string $model = PagoSuscripcion::class;

    protected static ?string $navigationIcon = 'heroicon-o-currency-euro';

    protected static ?string $navigationGroup = 'Suscripciones';

    protected static ?string $navigationLabel = 'Pagos';

    protected static ?string $recordTitleAttribute = 'suscripcion.negocio.nombre';

    protected static ?string $label = 'Pago';

    protected static ?string $pluralLabel = 'Pagos';

    protected static ?int $navigationSort = 6;



    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('suscripcion_id')
                    ->relationship('suscripcion', 'id')
                    ->getOptionLabelFromRecordUsing(fn($record) => $record->negocio->nombre ?? 'Sin negocio')
                    ->searchable()
                    ->preload()
                    ->required(),
                Forms\Components\Select::make('metodo_pago')
                    ->options(MetodoPago::getAsOptions())
                    ->required(),
                Forms\Components\TextInput::make('importe')
                    ->required()
                    ->numeric()
                    ->prefix('€')
                    ->step(0.01),
                Forms\Components\DatePicker::make('fecha_pago')
                    ->displayFormat('d/m/Y')
                    ->required(),
                Forms\Components\TextInput::make('transaccion_id')
                    ->maxLength(255),
                Forms\Components\Select::make('estado')
                    ->options(EstadoPago::getAsOptions())
                    ->placeholder('Seleccionar estado'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('suscripcion.negocio.nombre')
                    ->label('Negocio')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('metodo_pago')
                    ->label('Método de Pago')
                    ->badge()
                    ->formatStateUsing(fn(MetodoPago $state): string => $state->label())
                    ->icon(fn(MetodoPago $state): string => $state->icon())
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('importe')
                    ->label('Importe')
                    ->money('EUR')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fecha_pago')
                    ->label('Fecha de Pago')
                    ->date('d/m/Y')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('transaccion_id')
                    ->label('Transacción ID')
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('estado')
                    ->label('Estado')
                    ->badge()
                    ->formatStateUsing(fn(?EstadoPago $state): string => $state?->label() ?? 'Sin estado')
                    ->color(fn(?EstadoPago $state): string => $state?->color() ?? 'gray')
                    ->searchable()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('metodo_pago')
                    ->options(MetodoPago::getAsOptions())
                    ->label('Método de Pago'),
                Tables\Filters\SelectFilter::make('estado')
                    ->options(EstadoPago::getAsOptions())
                    ->label('Estado'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPagoSuscripcions::route('/'),
            'create' => Pages\CreatePagoSuscripcion::route('/create'),
            'edit' => Pages\EditPagoSuscripcion::route('/{record}/edit'),
        ];
    }
}
