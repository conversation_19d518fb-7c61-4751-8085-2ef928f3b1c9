import 'package:flutter/foundation.dart';

class Suscripcion {
  final int id;
  final int negocioId;
  final String plan;
  final int status;
  final String startedAt;
  final String endsAt;

  Suscripcion({
    required this.id,
    required this.negocioId,
    required this.plan,
    required this.status,
    required this.startedAt,
    required this.endsAt,
  });

  factory Suscripcion.fromJson(Map<String, dynamic> json) {
    try {
      return Suscripcion(
        id: json['id'] as int,
        negocioId: json['negocio_id'] as int,
        plan: json['plan'],
        status: json['status'] as int,
        startedAt: json['started_at'],
        endsAt: json['ends_at'],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error al procesar el JSON para Suscripcion: $json');
        print('Error: $e');
      }
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'negocio_id': negocioId,
      'plan': plan,
      'status': status,
      'started_at': startedAt,
      'ends_at': endsAt,
    };
  }

  @override
  String toString() {
    return 'Suscripcion(id: ${id.toString()}, negocioId: $negocioId, plan: $plan, status: ${status.toString()}, startedAt: $startedAt, endsAt: $endsAt)';
  }
}
