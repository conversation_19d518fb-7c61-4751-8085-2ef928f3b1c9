<?php

namespace App\Filament\Resources;

use Filament\Forms;
use App\Models\User;
use App\Models\Zona;
use Filament\Tables;
use App\Models\Negocio;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Services\CacheService;
use Filament\Resources\Resource;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Textarea;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Actions\DeleteAction;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\ZonaResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\ZonaResource\RelationManagers;

class ZonaResource extends Resource
{
    protected static ?string $model = Zona::class;

    protected static ?string $navigationGroup = 'Gestión';

    protected static ?string $navigationIcon = 'heroicon-o-globe-alt';

    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('nombre')
                    ->required()
                    ->maxLength(50)
                    ->label('Nombre de la zona')
                    ->placeholder('Ej. Playa Norte'),

                Textarea::make('descripcion')
                    ->label('Descripción de la zona')
                    ->placeholder('Detalles adicionales de la zona'),

                Select::make('localidad_id')
                    ->relationship('localidad', 'nombre')
                    ->required(),

                KeyValue::make('coordenadas')
                    ->label('Coordenadas (GeoJSON)')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('nombre')
                    ->label('Nombre')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('localidad.nombre')
                    ->label('Localidad')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('descripcion')
                    ->label('Descripción')
                    ->limit(50)
                    ->searchable(),

                TextColumn::make('coordenadas')
                    ->label('Coordenadas')
                    ->limit(30),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                DeleteAction::make()
                    ->action(function ($record) {
                        $record->delete();
                        $service = app(CacheService::class);
                        $service->invalidateCache(Zona::class, 'zonas_all');
                        $service->invalidateCache(Negocio::class, 'negocios_all');
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->action(function ($records) {
                            $records->each(function ($record) {
                                $record->delete();
                            });
                            $service = app(CacheService::class);
                            $service->invalidateCache(Zona::class, 'zonas_all');
                            $service->invalidateCache(Negocio::class, 'negocios_all');
                        }),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListZonas::route('/'),
            'create' => Pages\CreateZona::route('/create'),
            'edit' => Pages\EditZona::route('/{record}/edit'),
        ];
    }

    public static function shouldRegisterNavigation(): bool
    {
        /** @var User */
        $user = Auth::user();
        return $user->can('zona.list');
    }
}
