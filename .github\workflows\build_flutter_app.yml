name: Flutter Build Multiplatforma

on:
   workflow_dispatch:
      inputs:
         platform:
            description: "Plataforma a compilar"
            required: true
            default: "ios"
            type: choice
            options:
               - all
               - android
               - ios
               - mobile
               #  - web

jobs:
   build:
      runs-on: macos-latest # Usa macOS para poder compilar iOS y Android

      steps:
         # Clonar repositorio
         - uses: actions/checkout@v3

         # Configurar Java para Android
         - name: Set up Java
           uses: actions/setup-java@v2
           with:
              distribution: "zulu"
              java-version: "17"

         # Configurar Flutter
         - name: Set up Flutter
           uses: subosito/flutter-action@v2
           with:
              flutter-version: "3.29.0"
              channel: "stable"

         # Print Flutter and Dart versions (for debugging)
         - name: Check Flutter and Dart versions
           run: |
              flutter config --no-analytics
              flutter --version
              dart --version

         # Preparar dependencias
         - name: Install dependencies
           working-directory: ./mobileapp
           run: |
              flutter config --no-analytics
              flutter pub get

         # Crear archivo .env para Staging
         - name: Generate .env for Staging
           working-directory: ./mobileapp
           run: |
              if [ "${{ inputs.platform }}" == "android" ] || [ "${{ inputs.platform }}" == "all" ] || [ "${{ inputs.platform }}" == "mobile" ]; then
                echo "${{ secrets.STAGING_FLUTTER_DOT_ENV }}" > .env
              fi

         # Compilación para Android - Staging Build (Debug Mode)
         - name: Build Android Staging (Debug Mode)
           working-directory: ./mobileapp
           run: |
              if [ "${{ inputs.platform }}" == "android" ] || [ "${{ inputs.platform }}" == "all" ] || [ "${{ inputs.platform }}" == "mobile" ]; then
                flutter build apk \
                  --dart-define=FLAVOR=staging \
                  -t lib/main.dart
                mv build/app/outputs/flutter-apk/app-release.apk build/app/outputs/flutter-apk/app-staging-debug.apk
              fi

         # Crear archivo .env para Producción
         - name: Generate .env for Production
           working-directory: ./mobileapp
           run: |
              if [ "${{ inputs.platform }}" == "android" ] || [ "${{ inputs.platform }}" == "all" ] || [ "${{ inputs.platform }}" == "mobile" ]; then
                echo "${{ secrets.PRODUCTION_FLUTTER_DOT_ENV }}" > .env
              fi

         # Compilación para Android - Production Build (Release Mode)
         - name: Build Android Production (Release Mode)
           working-directory: ./mobileapp
           run: |
              if [ "${{ inputs.platform }}" == "android" ] || [ "${{ inputs.platform }}" == "all" ] || [ "${{ inputs.platform }}" == "mobile" ]; then
                flutter build apk --release \
                  --dart-define=FLAVOR=production \
                  -t lib/main.dart
                mv build/app/outputs/flutter-apk/app-release.apk build/app/outputs/flutter-apk/app-production-release.apk
              fi

         # Compilación para Android - App Bundle for Production
         - name: Build Android App Bundle for Production
           working-directory: ./mobileapp
           run: |
              if [ "${{ inputs.platform }}" == "android" ] || [ "${{ inputs.platform }}" == "all" ] || [ "${{ inputs.platform }}" == "mobile" ]; then
                flutter build appbundle --release \
                  --dart-define=FLAVOR=production \
                  -t lib/main.dart
              fi

         # Crear archivo .env para Producción iOS
         - name: Generate .env for Production
           working-directory: ./mobileapp
           run: |
              if [ "${{ inputs.platform }}" == "ios" ] || [ "${{ inputs.platform }}" == "all" ] || [ "${{ inputs.platform }}" == "mobile" ]; then
                echo "${{ secrets.PRODUCTION_FLUTTER_DOT_ENV }}" > .env
              fi

         # Compilación para iOS
         - name: Build iOS
           working-directory: ./mobileapp
           run: |
              if [ "${{ inputs.platform }}" == "ios" ] || [ "${{ inputs.platform }}" == "all" ] || [ "${{ inputs.platform }}" == "mobile" ]; then
                flutter build ios --release --no-codesign
              fi

         # Compilación para Web
         #  - name: Build Web
         #    working-directory: ./mobileapp
         #    run: |
         #       if [ "${{ inputs.platform }}" == "web" ] || [ "${{ inputs.platform }}" == "all" ]; then
         #         flutter build web
         #       fi

         # Subir APKs de Android
         - name: Upload Android APKs
           uses: actions/upload-artifact@v4
           with:
              name: android-apks
              path: |
                 mobileapp/build/app/outputs/flutter-apk/app-staging-debug.apk
                 mobileapp/build/app/outputs/flutter-apk/app-production-release.apk
              retention-days: 3

         # Subir Android App Bundle
         - name: Upload Android AAB
           uses: actions/upload-artifact@v4
           with:
              name: android-aab
              path: mobileapp/build/app/outputs/bundle/release/*.aab
              retention-days: 3

         # Subir iOS .app
         - name: Upload iOS App
           uses: actions/upload-artifact@v4
           with:
              name: ios-app
              path: mobileapp/build/ios/iphoneos/Runner.app
              retention-days: 3
