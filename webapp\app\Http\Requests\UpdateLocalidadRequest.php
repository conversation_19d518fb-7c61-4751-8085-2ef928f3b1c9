<?php

namespace App\Http\Requests;

use App\Models\Localidad;
use Illuminate\Foundation\Http\FormRequest;

class UpdateLocalidadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $localidad = $this->route('localidad');
        return $this->user()->can('update', $localidad);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'nombre' => 'sometimes|string|max:255',
            'ubicacion' => 'sometimes|array',
            'ubicacion.latitud' => 'sometimes|numeric|between:-90,90',
            'ubicacion.longitud' => 'sometimes|numeric|between:-180,180',
            'limites' => 'sometimes|array',
            'limites.latitud_min' => 'sometimes|numeric|between:-90,90|lt:limites.latitud_max',
            'limites.latitud_max' => 'sometimes|numeric|between:-90,90|gt:limites.latitud_min',
            'limites.longitud_min' => 'sometimes|numeric|between:-180,180|lt:limites.longitud_max',
            'limites.longitud_max' => 'sometimes|numeric|between:-180,180|gt:limites.longitud_min',
        ];
    }
}
