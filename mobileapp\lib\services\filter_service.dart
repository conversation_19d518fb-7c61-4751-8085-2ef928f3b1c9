import 'package:mia/models/negocio.dart';
import 'package:mia/models/categoria.dart';
import 'package:mia/models/zona.dart';
import 'package:mia/utils/tree.dart';

class FilterService {
  List<Categoria>? categoriasSeleccionadas;
  List<Zona>? zonasSeleccionadas;

  FilterService({this.categoriasSeleccionadas, this.zonasSeleccionadas});

  List<Negocio> aplicarFiltros(
      List<Negocio> negocios, List<Categoria> categorias) {
    if (zonasSeleccionadas == null &&
        (categoriasSeleccionadas == null || categoriasSeleccionadas!.isEmpty)) {
      return negocios; // No hay filtros, devuelve la lista original
    }

    return negocios.where((negocio) {
      // filtro de suscripcion activa
      bool filtroSuscripcion = true;
      if (negocio.suscripcion['status'] != 1) {
        return false;
      }

      bool filtroCategoria = false;
      if (negocio.categorias != null) {
        for (var catNeg in negocio.categorias!) {
          if (_cumpleFiltroCategoria(
              catNeg, categoriasSeleccionadas!, categorias)) {
            filtroCategoria = true;
            break;
          }
        }
      }

      bool filtroZona = true;
      if (zonasSeleccionadas != null && zonasSeleccionadas!.isNotEmpty) {
        filtroZona =
            zonasSeleccionadas!.any((zona) => negocio.zona?.id == zona.id);
      }

      return filtroSuscripcion && filtroCategoria && filtroZona;
    }).toList();
  }

  bool _cumpleFiltroCategoria(Categoria? categoriaNegocio,
      List<Categoria> seleccionadas, List<Categoria> categorias) {
    if (categoriaNegocio == null) {
      return false;
    }
    // 1. Crear árbol de categorías toteles y seleccionadas (si no existe)
    final arbolSeleccionadas = Tree.buildTree<Categoria>(seleccionadas);
    // final arbolCategorias = Tree.buildTree<Categoria>(seleccionadas);

    // Posibles casos

    // Caso 0: Si no hay nada seleccionado todas cumplen
    if (arbolSeleccionadas == null) {
      return true;
    }

    // Caso A: la propia categoria esta entre las seleccionadas => cumple
    if (arbolSeleccionadas.contiene(categoriaNegocio)) {
      return true;
    }

    // Caso B: la propia categoria no esta entre las seleccionadas

    // B1 : Existen hermanos seleccionados => la categoria no cumple
    if (_tieneHermanosSeleccionados(
        categoriaNegocio, arbolSeleccionadas, categorias)) {
      return false;
    }

    // B2 : No existen hermanos seleccionados

    Map<int, Categoria> antecesores = categoriaNegocio.antecesores(categorias);

    // B21 : No existe un antecesor seleccionado (pero sabemos por caso 0 que algo hay seleccionado) => no cumple
    if (antecesores.isEmpty) return false;

    // Ordenamos los antecesores por nivel descendente (clave)
    final niveles = antecesores.keys.toList()..sort((a, b) => b.compareTo(a));

    for (final nivel in niveles) {
      final antecesor = antecesores[nivel];

      // B22 : Existe un camino a un antecesor seleccionado donde los nodos intermedios del camino no seleccionados no tienen ningun hermano seleccionado => cumple
      if (arbolSeleccionadas.contiene(antecesor!)) {
        return true;
      }

      // B23 : Aunque hay un camino a un antecesor seleccionado, alguno de los nodos en el camino no seleccionados si tienen algun hermano seleccionado => no cumple
      List<Categoria> hermanos = antecesor.hermanos(categorias);

      for (final hermano in hermanos) {
        if (!_tieneHermanosSeleccionados(
            hermano, arbolSeleccionadas, categorias)) {
          return false; // Algún hermano del antecesor está seleccionado
        }
      }
    }

    return false;
  }

  bool _tieneHermanosSeleccionados(Categoria cat,
      Tree<Categoria> arbolSeleccionadas, List<Categoria> todasLasCategorias) {
    final categoriaPadre = cat.padre(todasLasCategorias);
    List<Categoria> hermanos = [];

    if (categoriaPadre == null) {
      for (final categoria in todasLasCategorias) {
        if (categoria.parentId == null && categoria.id != cat.id) {
          hermanos.add(categoria);
        }
      }
    } else {
      hermanos = categoriaPadre.hijos(todasLasCategorias);
    }

    for (final hermano in hermanos) {
      if (arbolSeleccionadas.contiene(hermano)) {
        //si alguno de sus hermanos esta entre las categorias seleccionadas
        return true;
      }
    }

    return false;
  }
}
