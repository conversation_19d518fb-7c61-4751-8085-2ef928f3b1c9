# Plantilla: Test de Filament (Backend)

## Plantilla Base

```php
<?php

namespace Tests\Feature\Filament;

use Tests\BaseTest;
use App\Models\{ModelName};
use App\Models\User;
use Spatie\Permission\Models\Role;
use PHPUnit\Framework\Attributes\Group;
use Database\Seeders\RolesPermissionSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;

#[Group('filament')]
#[Group('{model_slug}')] // Ej: 'negocio'
class {ModelName}FilamentTest extends BaseTest
{
    use RefreshDatabase;

    private array $usuarios;

    protected function setUp(): void
    {
        parent::setUp();

        $this->seed(RolesPermissionSeeder::class);

        foreach (Role::all() as $rol) {
            $user = User::factory()->create();
            $user->assignRole($rol->name);

            $this->usuarios[$rol->name] = [
                'usuario' => $user,
            ];
        }
    }

    public function test_el_sistema_muestra_las_paginas_del_recurso_correctamente_segun_el_rol_del_usuario(): void
    {

        // 🟢 Test positivos

        $model = {ModelName}::factory()->create();

        $test_roles = $this->_getRolesPositivos(['{model_slug}.list', '{model_slug}.create', '{model_slug}.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $this->get(\App\Filament\Resources\{ModelName}Resource::getUrl('index'))->assertSuccessful();
            $this->get(\App\Filament\Resources\{ModelName}Resource::getUrl('create'))->assertSuccessful();
            $this->get(\App\Filament\Resources\{ModelName}Resource::getUrl('edit', ['record' => $model->id]))->assertSuccessful();
        }

        // 🔴 Test negativos

        $test_roles = $this->_getRolesNegativos(['{model_slug}.list', '{model_slug}.create', '{model_slug}.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $this->get(\App\Filament\Resources\{ModelName}Resource::getUrl('index'))->assertForbidden();
            $this->get(\App\Filament\Resources\{ModelName}Resource::getUrl('create'))->assertForbidden();
            $this->get(\App\Filament\Resources\{ModelName}Resource::getUrl('edit', ['record' => $model->id]))->assertForbidden();
        }
    }

    public function test_el_sistema_crea_un_nuevo_modelo_solo_con_datos_validos(): void
    {
        // 🟢 Test positivos

        $test_roles = $this->_getRolesPositivos(['{model_slug}.create']);
        $i = 0;

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $newData = {ModelName}::factory()->make();

            $test = Livewire::test(Create{ModelName}::class);

            $this->assertDatabaseCount({ModelName}::class, $i++);

            $test
                ->fillForm([
                    // ... completar con los campos del modelo
                    // Ejemplo: 'nombre' => $newData['nombre'], 'descripcion' => $newData['descripcion'],
                ])
                ->call('create')
                ->assertHasNoFormErrors();

            $this->assertDatabaseCount({ModelName}::class, $i);
            $this->assertDatabaseHas({ModelName}::class, [
                // ... completar con los campos del modelo
                // Ejemplo: 'nombre' => $newData->nombre, 'descripcion' => $newData->descripcion,
            ]);
        }

        // 🔴 Test negativos

        $test_roles = $this->_getRolesPositivos(['{model_slug}.create']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $test_cases = [
                // caso 1: campos vacíos
                [
                    'payload' => [],
                    'expectedInvalid' => ['nombre']
                ],
            ];

            $test = Livewire::test(Create{ModelName}::class);

            foreach ($test_cases as $case) {
                $test->fillForm($case['payload']);

                $test->call('create')
                    ->assertHasFormErrors($case['expectedInvalid']);
            }
        }
    }

    public function test_el_sistema_edita_un_modelo_solo_con_datos_validos(): void
    {

        // 🟢 Test positivos

        $model = {ModelName}::factory()->create();

        $test_roles = $this->_getRolesPositivos(['{model_slug}.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $model->refresh();
            $newData = {ModelName}::factory()->make()->toArray();

            $test = Livewire::test(Edit{ModelName}::class, ['record' => $model->id]);

            $test
                ->assertFormSet([
                    // ... completar con los campos del modelo
                    // Ejemplo: 'nombre' => $model->nombre, 'descripcion' => $model->descripcion,
                ])
                ->fillForm([
                    // ... completar con los campos del modelo
                    // Ejemplo: 'nombre' => $newData['nombre'], 'descripcion' => $newData['descripcion'],
                ])
                ->call('save')
                ->assertHasNoFormErrors();

            $this->assertDatabaseHas({ModelName}::class, [
                // ... completar con los campos del modelo
                // Ejemplo: 'nombre' => $newData['nombre'], 'descripcion' => $newData['descripcion'],
            ]);
        }

        // 🔴 Test negativos

        $test_roles = $this->_getRolesPositivos(['{model_slug}.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $model->refresh();

            $test_cases = [
                // caso 1: campos vacíos
                [
                    'payload' => [],
                    'expectedInvalid' => ['nombre']
                ],
            ];

            $test = Livewire::test(Edit{ModelName}::class, ['record' => $model->id]);

            foreach ($test_cases as $case) {
                $test->fillForm($case['payload'])
                    ->call('save')
                    ->assertHasFormErrors($case['expectedInvalid']);
            }

            $this->assertDatabaseHas({ModelName}::class, [
                // ... completar con los campos del modelo para comprobar que no se actualizó
                // Ejemplo: 'nombre' => $model->nombre, 'descripcion' => $model->descripcion,
            ]);
        }
    }

    public function test_el_sistema_elimina_correctamente_un_modelo(): void
    {
        // 🟢 Test positivos

        $test_roles = $this->_getRolesPositivos(['{model_slug}.delete']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $model = ::factory()->create();
            $this->assertDatabaseCount({ModelName}::class, 1);

            $test = Livewire::test(List{ModelName}es::class);

            $test
                ->assertTableActionVisible(DeleteAction::class, $model)
                ->callTableAction(DeleteAction::class, $model);

            $this->assertModelMissing($model);
            $this->assertDatabaseCount({ModelName}::class, 0);
        }

        // 🔴 Test negativos

        $test_roles = [];

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            // $test = Livewire::test(ListAplicaciones::class);
        }

        $this->assertTrue(true);
    }

}
```
