<?php

namespace App\Observers;

use App\Models\Negocio;
use App\Models\Zona;
use App\Services\CacheService;

class ZonaObserver
{
    protected CacheService $cache;

    public function __construct(CacheService $cache)
    {
        $this->cache = $cache;
    }

    public function created(Zona $zona)
    {
        $this->invalidate();
    }

    public function updated(Zona $zona)
    {
        $this->invalidate();
    }

    public function deleted(Zona $zona)
    {
        $this->invalidate();
    }

    public function restored(Zona $zona)
    {
        $this->invalidate();
    }

    public function forceDeleted(Zona $zona)
    {
        $this->invalidate();
    }

    protected function invalidate()
    {
        $this->cache->invalidateCache(Zona::class, 'zonas_all');
        $this->cache->invalidateCache(Negocio::class, 'negocios_all');
    }
}
