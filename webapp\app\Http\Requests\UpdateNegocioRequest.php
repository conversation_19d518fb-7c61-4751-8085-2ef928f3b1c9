<?php

namespace App\Http\Requests;

use App\Models\Negocio;
use App\Enums\EstadoSuscripcion;
use Illuminate\Foundation\Http\FormRequest;

class UpdateNegocioRequest extends FormRequest
{
    public function authorize(): bool
    {
        $id = $this->route('negocio'); // O 'negocio', según tu parámetro en la ruta
        $negocio = Negocio::findOrFail($id);

        return $negocio && $this->user()->can('update', $negocio);
    }

    public function rules(): array
    {
        return [
            'nombre' => 'sometimes|string|max:100',
            'descripcion' => 'nullable|string',
            'direccion' => 'sometimes|string|max:255',
            'ubicacion' => 'sometimes|array',
            'ubicacion.latitud' => 'nullable|numeric|between:-90,90',
            'ubicacion.longitud' => 'nullable|numeric|between:-180,180',
            'horario' => 'nullable|array',
            // 'horario.*' => 'regex:/^(0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]-(0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$/',
            'contacto' => 'sometimes|string|max:255',
            'zona_id' => 'nullable|exists:zonas,id',
            'user_id' => 'sometimes|exists:users,id',

            // Enlaces
            'enlaces_sociales' => 'nullable|array',
            'enlaces_sociales.*.plataforma' => 'required|string',
            'enlaces_sociales.*.url' => 'required|url',

            'enlaces_propios' => 'nullable|array',
            'enlaces_propios.*' => [
                'required',
                function ($attribute, $value, $fail) {
                    // $attribute será algo como "precios.0.productos.Entrada General"
                    // Separamos por puntos y obtenemos la última parte
                    $parts = explode('.', $attribute);
                    $titulo = end($parts); // "Entrada General"

                    // Verificamos que la clave (titulo del enlace) no esté vacía
                    if (trim($titulo) === '') {
                        $fail('El titulo del enlace no puede estar vacío.');
                    }

                    // Verificamos que el valor sea url
                    if (!filter_var($value, FILTER_VALIDATE_URL)) {
                        $fail('La URL del enlace debe ser una URL válida.');
                    }
                },
            ],

            // Precios
            'precios' => 'nullable|array',
            'precios.*.categoria' => 'required|string',
            // Precios
            'precios' => 'nullable|array',
            'precios.*.categoria' => 'required|string',
            'precios.*.productos' => 'required|array',
            'precios.*.productos.*' => [
                'required', // Para asegurar que haya un valor numérico
                function ($attribute, $value, $fail) {
                    // $attribute será algo como "precios.0.productos.Entrada General"
                    // Separamos por puntos y obtenemos la última parte
                    $parts = explode('.', $attribute);
                    $productName = end($parts); // "Entrada General"

                    // Verificamos que la clave (nombre del producto) no esté vacía
                    if (trim($productName) === '') {
                        $fail('El nombre del producto no puede estar vacío.');
                    }

                    // Verificamos que el valor sea numérico
                    if (!is_numeric($value)) {
                        $fail('El precio del producto debe ser un valor numérico.');
                    }
                },
            ],
            // suscripcion
            'suscripcion' => [
                'sometimes',
                function ($attribute, $value, $fail) {
                    // Verificamos que el valor este en los EstadoSuscripcion:cases()
                    if (!in_array($value, array_map(fn($case) => $case->value, EstadoSuscripcion::cases()))) {
                        $fail('El estado de la suscripción no es válido');
                    }
                }
            ],
            'categorias' => 'required|array|exists:categorias,id|min:1',
        ];
    }

    public function messages(): array
    {
        return [
            'direccion.max' => 'La dirección del negocio es demasiado larga.',
            'ubicacion.latitud.between' => 'La latitud debe estar entre -90 y 90 grados.',
            'ubicacion.longitud.between' => 'La longitud debe estar entre -180 y 180 grados.',
            'categoria_id.exists' => 'La categoría seleccionada no es válida.',
            'zona_id.exists' => 'La zona seleccionada no es válida.',
            'user_id.exists' => 'El cliente seleccionado no es válido.',
            'precios.*.categoria.required' => 'La categoría del precio es obligatoria.',
            'precios.*.productos.*.nombre.required' => 'El nombre del producto es obligatorio.',
            'precios.*.productos.*.precio.required' => 'El precio del producto es obligatorio.',
            'precios.*.productos.*.precio.numeric' => 'El precio debe ser un valor numérico.',
            'enlaces_sociales.*.plataforma.required' => 'La plataforma del enlace es obligatoria.',
            'enlaces_sociales.*.url.required' => 'La URL del enlace es obligatoria.',
            'enlaces_sociales.*.url.url' => 'La URL del enlace debe ser una URL válida.',
            'precios.*.categoria.required' => 'La categoría del precio es obligatoria.',
            'precios.*.productos.required' => 'Debe existir al menos un producto.',
        ];
    }
}
