# Estructura de la Documentación del Proyecto MIA

## Árbol de Documentación

```
docs/
├── README.md                           # Índice principal
│
├── 01-vision-general/
│   └── README.md                       # Arquitectura, stack, flujo de datos
│
├── 02-backend/
│   ├── README.md                       # Estructura, API, Filament
│   ├── modelos.md                      # Documentación detallada de modelos
│   ├── enums.md                        # Enumeraciones y su uso
│   ├── permisos.md                     # Sistema de permisos (futuro)
│   ├── filament.md                     # Panel Filament (futuro)
│   └── relaciones.md                   # Relaciones entre modelos (futuro)
│
├── 03-frontend/
│   ├── README.md                       # Estructura, providers, servicios
│   ├── modelos.md                      # Modelos Dart (futuro)
│   ├── providers.md                    # Providers detallados (futuro)
│   ├── servicios.md                    # Servicios detallados (futuro)
│   └── widgets.md                      # Widgets reutilizables (futuro)
│
├── 04-database/
│   ├── README.md                       # Esquema de BD (futuro)
│   └── diagrama-er.md                  # Diagrama ER (futuro)
│
├── 05-api/
│   ├── README.md                       # Documentación de API REST
│   └── openapi.yaml                    # Especificación OpenAPI (futuro)
│
├── 06-testing/
│   ├── README.md                       # Estrategia de testing
│   ├── plantilla-api-test.md          # Plantilla para tests de API
│   ├── plantilla-model-test.md        # Plantilla para tests de modelos
│   └── fixtures.md                     # Guía de fixtures (futuro)
│
└── 07-guias/
    ├── README.md                       # Índice de guías (futuro)
    ├── como-anadir-modelo.md           # Guía completa para añadir modelos
    ├── como-anadir-endpoint.md         # Guía para endpoints (futuro)
    ├── como-crear-widget.md            # Guía para widgets (futuro)
    └── mejores-practicas.md            # Mejores prácticas (futuro)
```

## Estado de la Documentación

### ✅ Completado

1. **README.md** - Índice principal con enlaces a todas las secciones
2. **01-vision-general/README.md** - Arquitectura completa del proyecto
3. **02-backend/README.md** - Estructura del backend Laravel
4. **02-backend/modelos.md** - Documentación detallada de todos los modelos
5. **02-backend/enums.md** - Documentación completa de enums
6. **03-frontend/README.md** - Estructura del frontend Flutter
7. **05-api/README.md** - Documentación completa de la API REST
8. **06-testing/README.md** - Estrategia y ejemplos de testing
9. **06-testing/plantilla-api-test.md** - Plantilla completa para tests de API
10.   **06-testing/plantilla-model-test.md** - Plantilla completa para tests de modelos
11.   **07-guias/como-anadir-modelo.md** - Guía paso a paso para añadir modelos

### 🚧 Pendiente (para futuras iteraciones)

-  **02-backend/permisos.md** - Documentación detallada del sistema de permisos
-  **02-backend/filament.md** - Guía completa del panel Filament
-  **02-backend/relaciones.md** - Relaciones entre modelos con ejemplos
-  **03-frontend/modelos.md** - Documentación de modelos Dart
-  **03-frontend/providers.md** - Providers en detalle
-  **03-frontend/servicios.md** - Servicios en detalle
-  **03-frontend/widgets.md** - Widgets reutilizables
-  **04-database/README.md** - Esquema de base de datos
-  **04-database/diagrama-er.md** - Diagrama entidad-relación
-  **05-api/openapi.yaml** - Especificación OpenAPI completa
-  **06-testing/fixtures.md** - Guía para crear fixtures
-  **07-guias/README.md** - Índice de guías
-  **07-guias/como-anadir-endpoint.md** - Guía para añadir endpoints
-  **07-guias/como-crear-widget.md** - Guía para crear widgets
-  **07-guias/mejores-practicas.md** - Mejores prácticas generales

## Cómo Usar Esta Documentación

### Para Programadores

1. **Empezar aquí**: Lee [README.md](./README.md) para una visión general
2. **Entender la arquitectura**: Revisa [01-vision-general](./01-vision-general/README.md)
3. **Trabajar en backend**: Consulta [02-backend](./02-backend/README.md)
4. **Trabajar en frontend**: Consulta [03-frontend](./03-frontend/README.md)
5. **Añadir funcionalidades**: Usa las guías en [07-guias](./07-guias/)
6. **Escribir tests**: Usa las plantillas en [06-testing](./06-testing/)

### Para LLMs (Asistentes de IA)

1. **Contexto general**: Leer [README.md](./README.md) y [01-vision-general](./01-vision-general/README.md)
2. **Modelos y estructura**: Consultar [02-backend/modelos.md](./02-backend/modelos.md) y [02-backend/enums.md](./02-backend/enums.md)
3. **API**: Referencia en [05-api/README.md](./05-api/README.md)
4. **Generar código**: Usar plantillas en [06-testing](./06-testing/) y [07-guias](./07-guias/)
5. **Mantener consistencia**: Seguir patrones documentados en cada sección

## Convenciones de Documentación

### Formato de Archivos

-  **Markdown**: Todos los archivos usan Markdown (.md)
-  **Código**: Bloques de código con syntax highlighting
-  **Diagramas**: ASCII art o Mermaid (futuro)
-  **Ejemplos**: Siempre incluir ejemplos reales del proyecto

### Estructura de Documentos

Cada documento debe incluir:

1. **Título principal** (H1)
2. **Índice** (si es largo)
3. **Descripción** breve
4. **Contenido** organizado con subtítulos
5. **Ejemplos** de código
6. **Enlaces** a documentos relacionados

### Nomenclatura

-  **Archivos**: kebab-case (ej: `como-anadir-modelo.md`)
-  **Directorios**: kebab-case con números (ej: `01-vision-general`)
-  **Títulos**: Sentence case (ej: "Cómo añadir un modelo")

## Mantenimiento

### Actualizar Documentación

Cuando hagas cambios en el código:

1. **Modelos**: Actualizar `02-backend/modelos.md` o `03-frontend/modelos.md`
2. **API**: Actualizar `05-api/README.md`
3. **Enums**: Actualizar `02-backend/enums.md`
4. **Tests**: Actualizar plantillas si cambia la estructura

### Añadir Nueva Documentación

1. Crear archivo en la sección apropiada
2. Actualizar índice en `README.md`
3. Actualizar este archivo (`Estructrura de la documentación.md`)
4. Añadir enlaces cruzados desde documentos relacionados

## Prioridades para Temporada 2

### Alta Prioridad ✅ (Completado)

-  [x] Visión general y arquitectura
-  [x] Documentación de modelos backend
-  [x] Documentación de enums
-  [x] Documentación de API
-  [x] Plantillas de testing
-  [x] Guía para añadir modelos

### Media Prioridad 🚧 (Siguiente)

-  [ ] Sistema de permisos detallado
-  [ ] Guía de Filament
-  [ ] Documentación de providers Flutter
-  [ ] Esquema de base de datos
-  [ ] Especificación OpenAPI completa

### Baja Prioridad 📋 (Futuro)

-  [ ] Guías adicionales (endpoints, widgets)
-  [ ] Mejores prácticas
-  [ ] Diagramas ER
-  [ ] Documentación de widgets

## Feedback y Mejoras

Esta documentación es un documento vivo. Si encuentras:

-  **Información faltante**: Añádela o crea un issue
-  **Errores**: Corrígelos directamente
-  **Mejoras**: Propón cambios

---

**Última actualización**: 2025-10-01
**Versión**: 1.0 (Temporada 2)
