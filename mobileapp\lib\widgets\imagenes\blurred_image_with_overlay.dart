import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:mia/models/media.dart';

class BlurredImageWithOverlay extends StatelessWidget {
  final Media imagen;
  final double height;

  const BlurredImageWithOverlay({
    super.key,
    required this.imagen,
    this.height = 250,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Imagen en Cover con Blur
        Positioned.fill(
          child: ImageFiltered(
            imageFilter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
            child: CachedNetworkImage(
              imageUrl: imagen.url,
              width: double.infinity,
              height: height,
              fit: BoxFit.cover,
              placeholder: (context, url) =>
                  const Center(child: CircularProgressIndicator()),
              errorWidget: (context, url, error) =>
                  const Icon(Icons.error, size: 50, color: Colors.red),
            ),
          ),
        ),
        // Imagen centrada sin blur
        Center(
          child: CachedNetworkImage(
            imageUrl: imagen.url,
            width: double.infinity,
            height: height,
            fit: BoxFit.contain,
            placeholder: (context, url) =>
                const Center(child: CircularProgressIndicator()),
            errorWidget: (context, url, error) =>
                const Icon(Icons.error, size: 50, color: Colors.red),
          ),
        ),
      ],
    );
  }
}
