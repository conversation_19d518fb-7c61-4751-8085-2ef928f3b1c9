import 'package:flutter/material.dart';
import 'package:mia/models/zona.dart';
import 'package:mia/services/filter_service.dart';
import 'package:mia/widgets/app_bottom_navigation_bar.dart';
import 'package:mia/widgets/filtros/filtro_negocio_zona.dart';
import 'package:shimmer/shimmer.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:mia/config/colors.dart';
import 'package:mia/config/styles.dart';
import 'package:mia/models/categoria.dart';
import 'package:mia/models/negocio.dart';
import 'package:mia/services/global_data_service.dart';
import 'package:mia/widgets/app_scaffold.dart';
import 'package:mia/widgets/cards/card_negocio.dart';

class NegociosPage extends StatefulWidget {
  const NegociosPage({super.key, required this.categoria});

  final Categoria categoria;

  @override
  State<NegociosPage> createState() => _NegociosPageState();
}

class _NegociosPageState extends State<NegociosPage> {
  List<Negocio> negocios = [];
  List<Negocio> negociosFiltrados = [];
  Set<int> visibleIndexes = {}; // Índices de elementos visibles

  final TextEditingController _searchController = TextEditingController();
  int? selectedZonaId; // ID de la zona seleccionada

  @override
  void initState() {
    super.initState();
    negocios = getNegociosByCategoria(
        GlobalDataService().negocios ?? [], widget.categoria);
    negociosFiltrados = negocios;
  }

  void _applyFilters() {
    var filterService = FilterService(
      categoriasSeleccionadas: [widget.categoria],
      zonasSeleccionadas: GlobalDataService().zonas,
    );

    // Apply text search filter
    var searchQuery = _searchController.text.toLowerCase();
    var filteredList =
        filterService.aplicarFiltros(negocios, [widget.categoria]);

    // Text search filter
    filteredList = filteredList
        .where((negocio) => negocio.nombre.toLowerCase().contains(searchQuery))
        .toList();

    // Aplicar filtro por zona si hay una seleccionada
    if (selectedZonaId != null) {
      filteredList = filteredList
          .where((negocio) => negocio.zona?.id == selectedZonaId)
          .toList();
    }

    setState(() {
      negociosFiltrados = filteredList;
    });
  }

  void _onZonaSelected(int? zonaId) {
    setState(() {
      selectedZonaId = zonaId;
    });
    _applyFilters();
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      bottomNavigationBar: const AppBottomNavigationBar(currentIndex: 0),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pop(context);
        },
        child: const Icon(Icons.arrow_back),
      ),
      child: Column(
        children: [
          // Sección de búsqueda y filtro
          _buildSearchAndFilterSection(),
          // Lista de negocios
          Expanded(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 24.0),
              // padding: const EdgeInsets.all(8.0),
              child: negocios.isEmpty
                  ? Center(
                      child: Text(
                        'No hay negocios en esta categoría',
                        style: AppStyles.getStyle(context, 'h5',
                            fontWeight: 'bold',
                            color: AppColors.current.secondaryColor),
                      ),
                    )
                  : ListView.builder(
                      itemCount: negociosFiltrados.length +
                          1, // +1 para el elemento invisible
                      itemBuilder: (context, index) {
                        if (index == negociosFiltrados.length) {
                          // Último elemento (invisible) para dar espacio
                          return SizedBox(
                              height:
                                  60); // Ajusta esta altura según sea necesario
                        }
                        return VisibilityDetector(
                          key: Key("card-$index"),
                          onVisibilityChanged: (VisibilityInfo info) {
                            if (info.visibleFraction > 0.1) {
                              setState(() {
                                visibleIndexes.add(index);
                              });
                            }
                          },
                          child: visibleIndexes.contains(index)
                              ? CardNegocio(negocio: negociosFiltrados[index])
                              : _buildShimmerPlaceholder(), // Placeholder con shimmer
                        );
                      },
                    ),
            ),
          ),
        ],
      ),
    );
  }

  List<Negocio> getNegociosByCategoria(
      List<Negocio> negocios, Categoria categoria) {
    return negocios
        .where((negocio) => negocio.categorias!.contains(categoria))
        .toList();
  }

  /// **Placeholder de carga con efecto shimmer**
  Widget _buildShimmerPlaceholder() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!, // Color de fondo del shimmer
      highlightColor: Colors.grey[100]!, // Color del efecto de brillo
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8.0),
        height: 250, // Altura de la card de placeholder
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  Widget _buildSearchAndFilterSection() {
    // Obtener todas las zonas disponibles
    List<Zona> allZonas = GlobalDataService().zonas ?? [];

    // Filtrar solo las zonas que tienen negocios en la lista actual
    List<Zona> zonasConNegocios = allZonas.where((zona) {
      return negocios.any((negocio) => negocio.zona?.id == zona.id);
    }).toList();

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        children: [
          // Mostrar el filtro de zona solo si hay más de una zona con negocios
          if (zonasConNegocios.length > 1) ...[
            FiltroNegocioZona(
              zonas: zonasConNegocios,
              selectedZonaId: selectedZonaId,
              onZonaSelected: _onZonaSelected,
            ),
            const SizedBox(
                height: 8.0), // Espacio entre el filtro y la búsqueda
          ],
          // Campo de búsqueda
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Buscar negocios...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        _applyFilters();
                      },
                    )
                  : null,
            ),
            onChanged: (_) => _applyFilters(),
          ),
        ],
      ),
    );
  }
}
