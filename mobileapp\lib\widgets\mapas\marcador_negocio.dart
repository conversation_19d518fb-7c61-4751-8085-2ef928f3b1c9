import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' as latlng;
import 'package:mia/models/negocio.dart';
import 'package:mia/services/core_service.dart';
import 'package:mia/services/mapas/map_tooltip_service.dart';

class MarcadorNegocio extends StatelessWidget {
  final Negocio negocio;
  final double markerSize;
  final bool isSelected;
  final void Function(Negocio)? onMarkerTap;
  final GlobalKey mapKey;
  final MapaTooltipService tooltipService;

  const Marc<PERSON><PERSON><PERSON><PERSON>({
    super.key,
    required this.negocio,
    required this.markerSize,
    required this.isSelected,
    required this.onMarkerTap,
    required this.mapKey,
    required this.tooltipService,
  });

  // Método para obtener los marcadores de este negocio
  List<Marker> getMarkers(BuildContext context) {
    final latlng.LatLng latLng = CoreService.getLatLng(negocio);
    final Color color = _getColor(negocio);

    return [
      // Marcador principal (icono de ubicación)
      Marker(
        width: markerSize,
        height: markerSize,
        point: latLng,
        rotate: true,
        alignment: Alignment.topCenter,
        child: GestureDetector(
          onTap: () {
            // Llamar al callback original si existe
            onMarkerTap?.call(negocio);

            // Obtener la posición del marcador en la pantalla
            final RenderBox renderBox =
                mapKey.currentContext!.findRenderObject() as RenderBox;
            final Offset localPosition = renderBox.localToGlobal(
              renderBox.size.center(Offset.zero),
            );

            // Mostrar el tooltip usando el servicio
            tooltipService.mostrarTooltip(context, negocio, localPosition);
          },
          child: Icon(
            Icons.location_pin,
            color: isSelected ? Colors.blue : color,
            size: isSelected
                ? (markerSize * 1.1) // Tamaño del marcador seleccionado
                : markerSize,
          ),
        ),
      ),

      // Punto debajo del marcador
      Marker(
        width: markerSize,
        height: markerSize,
        point: latLng,
        rotate: true,
        alignment: Alignment.center,
        child: Icon(
          Icons.circle,
          color: color,
          size: markerSize / 7,
        ),
      ),
    ];
  }

  Color _getColor(Negocio negocio) {
    return Colors.red;
  }

  @override
  Widget build(BuildContext context) {
    // Este widget no renderiza nada directamente
    return const SizedBox.shrink();
  }
}
