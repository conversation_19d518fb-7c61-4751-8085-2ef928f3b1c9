import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:mia/config/colors.dart';
import 'package:mia/config/styles.dart';
import 'package:mia/models/categoria.dart';
import 'package:mia/models/media.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mia/services/svg_cache_manager.dart';

class CategoryItem extends StatelessWidget {
  final String nombre;
  final Media? icono;
  final Categoria categoria;
  final bool activo; // Indica si la categoría tiene negocios
  final Color? disabledColor;

  final Color defaultDisabledColor = const Color.fromARGB(255, 216, 216, 216);

  const CategoryItem({
    super.key,
    required this.categoria,
    required this.nombre,
    this.icono,
    this.activo = true, // Por defecto se asume que está activo
    this.disabledColor,
  });

  // Función asíncrona que intenta cargar el SVG desde la red y, en caso de error, devuelve null.
  Future<String?> _loadSvg(String url) async {
    try {
      final cacheManager = SvgCacheManager();
      return await cacheManager.getSvg(url);
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    // final double textScale = MediaQuery.of(context).textScaleFactor;
    // // Factor de compensación inverso para mantener el tamaño base
    // final double factorCompensacion = 1 / textScale;
    final double factorCompensacion = 1.0;
    final themeTextColor = AppColors.current.textColor; // color del tema

    return Card(
      elevation: 3,
      color: AppColors.current.surfaceColor,
      child: LayoutBuilder(builder: (context, constraints) {
        // Obtener el tamaño mínimo entre ancho y alto disponible
        final availableSize = constraints.maxWidth < constraints.maxHeight
            ? constraints.maxWidth
            : constraints.maxHeight;

        // Definir breakpoints y tamaños
        double iconSize;
        double fontSize;
        int maxLines;

        if (availableSize < 80) {
          iconSize = 25;
          fontSize = 10 * factorCompensacion;
          maxLines = 2;
        } else if (availableSize < 120) {
          iconSize = 40;
          fontSize = 12 * factorCompensacion;
          maxLines = 2;
        } else if (availableSize < 160) {
          iconSize = 60;
          fontSize = 16 * factorCompensacion;
          maxLines = 2;
        } else {
          iconSize = 70;
          fontSize = 18 * factorCompensacion;
          maxLines = 2;
        }

        return InkWell(
          onTap: () {
            if (activo) {
              Navigator.pushNamed(context, '/negocios', arguments: categoria);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                      'Todavía no hay negocios en la categoría ${categoria.nombre}'),
                  duration: Duration(seconds: 2),
                ),
              );
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(4.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildIcon(icono, iconSize, themeTextColor),
                const SizedBox(height: 8),
                Flexible(
                  child: AutoSizeText(
                    nombre,
                    textAlign: TextAlign.center,
                    minFontSize: 8,
                    style:
                        AppStyles.getStyle(context, 's', fontWeight: 'medium')
                            .copyWith(
                      color:
                          activo ? null : disabledColor ?? defaultDisabledColor,
                      fontSize: fontSize,
                    ),
                    textScaleFactor: 1.0,
                    maxLines: maxLines,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }

  Widget _buildIcon(Media? icono, double size, Color themeTextColor) {
    // Si no está activo, aplicamos un tinte
    final bool tint = !activo;
    final Color tintColor = disabledColor ?? defaultDisabledColor;
    final ColorFilter filter =
        ColorFilter.mode(tint ? tintColor : themeTextColor, BlendMode.srcIn);

    if (icono == null) {
      return SizedBox(width: size, height: size);
    }

    if (icono.url.toLowerCase().endsWith('.svg')) {
      return FutureBuilder<String?>(
        future: _loadSvg(icono.url),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            // Si ocurrió un error o no se obtuvo contenido, se muestra el asset local.
            if (snapshot.hasError || snapshot.data == null) {
              return SizedBox(width: size, height: size);
            }
            // Si se obtuvo el contenido correctamente, se renderiza el SVG.
            return SvgPicture.string(
              snapshot.data!,
              width: size,
              height: size,
              colorFilter: filter,
            );
          }
          return SizedBox(width: size, height: size);
        },
      );
    } else {
      return CachedNetworkImage(
        imageUrl: icono.url,
        width: size,
        height: size,
        color: activo ? null : tintColor,
        placeholder: (context, url) => const Center(
          child: CircularProgressIndicator(),
        ),
        errorWidget: (context, url, error) =>
            SizedBox(width: size, height: size),
        fadeInDuration: const Duration(milliseconds: 300),
        fit: BoxFit.contain,
        cacheManager: null,
      );
    }
  }
}
