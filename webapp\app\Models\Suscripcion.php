<?php

namespace App\Models;

use App\Enums\EstadoPago;
use App\Enums\TipoSuscripcion;
use App\Enums\EstadoSuscripcion;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Suscripcion extends Model
{
    use HasFactory;

    protected $table = 'suscripciones';

    protected $fillable = [
        'negocio_id',
        'plan',
        'status',
        'precio',
        'started_at',
        'ends_at',
    ];

    protected $casts = [
        'status' => EstadoSuscripcion::class,
        'plan' => TipoSuscripcion::class,
        'started_at' => 'datetime',
        'ends_at' => 'datetime',
    ];

    public function negocio()
    {
        return $this->belongsTo(Negocio::class);
    }

    public function pagos()
    {
        return $this->hasMany(PagoSuscripcion::class);
    }

    public function scopeFacturables($query)
    {
        return $query->where('plan', '!=', TipoSuscripcion::FREE->value);
    }

    public function scopeActivas($query)
    {
        return $query->where('status', EstadoSuscripcion::ACTIVE->value);
    }

    public function scopeInactivas($query)
    {
        return $query->where('status', EstadoSuscripcion::INACTIVE->value);
    }

    // scope para obtener las suscripciones con pagos en la temporada actual, que va desde abril hasta abril del año siguiente, es decir, si estamos antes de abril, la temporada actual es el año anterior, si estamos después de abril, la temporada actual es el año actual
    public function scopeConPagosEnTemporadaActual($query)
    {
        $hoy = now();
        $inicioTemporada = $hoy->month >= 4 ? $hoy->year : $hoy->year - 1;
        $finTemporada = $inicioTemporada + 1;

        return $query->whereHas('pagos', function ($query) use ($inicioTemporada, $finTemporada) {
            $query->whereBetween('fecha_pago', [
                \Carbon\Carbon::createFromDate($inicioTemporada, 4, 1)->startOfDay(),
                \Carbon\Carbon::createFromDate($finTemporada, 3, 31)->endOfDay()
            ])
                ->where('estado', '!=', EstadoPago::RECHAZADO->value)
            ;
        });
    }

    public function scopeSinPagosEnTemporadaActual($query)
    {
        $hoy = now();
        $inicioTemporada = $hoy->month >= 4 ? $hoy->year : $hoy->year - 1;
        $finTemporada = $inicioTemporada + 1;

        return $query->whereDoesntHave('pagos', function ($query) use ($inicioTemporada, $finTemporada) {
            $query->whereBetween('fecha_pago', [
                \Carbon\Carbon::createFromDate($inicioTemporada, 4, 1)->startOfDay(),
                \Carbon\Carbon::createFromDate($finTemporada, 3, 31)->endOfDay()
            ])
                ->where('estado', '!=', EstadoPago::RECHAZADO->value)
            ;
        });
    }
}
