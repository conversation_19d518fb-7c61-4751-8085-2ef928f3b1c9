<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Localidad;
use Spatie\Permission\Models\Role;
use PHPUnit\Framework\Attributes\Group;
use Database\Seeders\RolesPermissionSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\BaseTest;

#[Group('api')]
#[Group('localidad')]
class LocalidadApiTest extends BaseTest
{
    use RefreshDatabase;

    private array $usuarios;

    private string $api_slug = '/api/v1/localidades';

    protected function setUp(): void
    {
        parent::setUp();

        $this->seed(RolesPermissionSeeder::class);

        foreach (Role::all() as $rol) {
            /** @var User */
            $user = User::factory()->create();
            $user->assignRole($rol->name);

            $this->usuarios[$rol->name] = [
                'usuario' => $user,
                'token' => $user->createToken('auth_toke_' . $rol->name)->plainTextToken,
            ];
        }
    }

    public function test_el_listado_de_localidades_es_accesible_solo_a_roles_con_permiso(): void
    {
        Localidad::factory(3)->create();

        $roles = Role::all();

        foreach ($roles as $rol) {
            /** @var User */
            $user = $this->usuarios[$rol->name]['usuario'];
            $this->actingAs($user);

            $response = $this->getJson("{$this->api_slug}");
            $response->assertStatus(200)
                ->assertJsonCount(3);
        }
    }

    public function test_mostrar_una_localidad_es_accesible_solo_a_roles_con_permiso(): void
    {
        $localidad = Localidad::factory()->create();
        $roles = Role::all();

        foreach ($roles as $rol) {
            /** @var User */
            $user = $this->usuarios[$rol->name]['usuario'];
            $this->actingAs($user);

            $response = $this->getJson("{$this->api_slug}/{$localidad->id}");
            $response->assertStatus(200)
                ->assertJson(['id' => $localidad->id]);
        }
    }

    public function test_solo_usuarios_con_permiso_pueden_crear_localidades(): void
    {
        // 🟢 Test positivos

        $test_roles = $this->_getRolesPositivos(['localidad.create']);

        foreach ($test_roles as $rol) {
            $this->actingAs($this->usuarios[$rol]['usuario']);

            $data = Localidad::factory()->make()->toArray();

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->postJson("{$this->api_slug}", $data);


            $response->assertStatus(201)
                ->assertJsonStructure([
                    'id',
                    'nombre',
                    'ubicacion',
                    'limites',
                    'created_at',
                    'updated_at'
                ])
                ->assertJson([
                    'nombre' => $data['nombre'],
                    'ubicacion' => $data['ubicacion'],
                    'limites' => $data['limites'],
                ]);

            $this->assertDatabaseHas('localidades', [
                'nombre' => $data['nombre'],
                'ubicacion' => json_encode($data['ubicacion']),
                'limites' => json_encode($data['limites']),
            ]);
        }

        // 🔴 Test negativos

        $test_roles = $this->_getRolesNegativos(['localidad.create']);

        foreach ($test_roles as $rol) {
            // Cambia el usuario autenticado antes de la solicitud
            $this->actingAs($this->usuarios[$rol]['usuario']);

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->postJson("{$this->api_slug}", $data);

            $response->assertStatus(403); // No autorizado
        }
    }

    public function test_el_sistema_no_crea_nueva_localidad_con_datos_inválidos(): void
    {
        // Casos de datos inválidos
        $testCases = [
            // Caso 1: Enviar datos vacíos
            [
                'payload' => [],
                'expectedInvalid' => ['nombre', 'ubicacion', 'limites']
            ],
            // Caso 2: Enviar un nombre demasiado largo
            [
                'payload' => [
                    'nombre' => str_repeat('A', 256), // 101 caracteres
                ],
                'expectedInvalid' => ['nombre', 'ubicacion', 'limites']
            ],
            // Caso 3: Coordenadas vacias
            [
                'payload' => [
                    'nombre' => 'Localidad Test',
                    'ubicacion' => [],
                    'limites' => [],
                ],
                'expectedInvalid' => ['ubicacion', 'limites']
            ],
            // Caso 4: Coordenadas fuera de rango
            [
                'payload' => [
                    'nombre' => 'Localidad Test',
                    'ubicacion' => ['latitud' => -95, 'longitud' => 200],
                    'limites' => ['latitud_min' => -95, 'latitud_max' => 200, 'longitud_min' => -95, 'longitud_max' => 200],
                ],
                'expectedInvalid' => ['ubicacion.latitud', 'ubicacion.longitud', 'limites.latitud_min', 'limites.latitud_max', 'limites.longitud_min', 'limites.longitud_max']
            ],
            // Caso 5: Limites inválidos por min mayor que max
            [
                'payload' => [
                    'nombre' => 'Localidad Test',
                    'ubicacion' => ['latitud' => 90, 'longitud' => 90],
                    'limites' => ['latitud_min' => 95, 'latitud_max' => 94, 'longitud_min' => 90, 'longitud_max' => 89],
                ],
                'expectedInvalid' => ['limites.latitud_min', 'limites.longitud_min', 'limites.latitud_max', 'limites.longitud_max']
            ],
        ];

        // Obtener un rol con permiso de creación
        $rol = $this->_getRolesPositivos(['localidad.create']);

        if (is_array($rol)) {
            $rol = $rol[0] ?? null;
        }

        if (!$rol) {
            return;
        }

        $token = $this->usuarios[$rol]['token'];

        foreach ($testCases as $case) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->postJson("{$this->api_slug}", $case['payload']);
        }

        if (isset($case['expectedForbidden']) && $case['expectedForbidden']) {
            $response->assertForbidden(); // Verificar acceso denegado
        } else {
            $response->assertInvalid($case['expectedInvalid']);
        }
    }

    public function test_solo_usuarios_con_permiso_pueden_editar_localidades(): void
    {
        $localidad = Localidad::factory()->create();

        // 🟢 Test positivos

        $test_roles = $this->_getRolesPositivos(['localidad.update']);

        foreach ($test_roles as $rol) {
            $this->actingAs($this->usuarios[$rol]['usuario']);

            $localidad->refresh();
            $data = Localidad::factory()->make()->toArray();

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->putJson("{$this->api_slug}/{$localidad->id}", $data);

            $response->assertStatus(200)
                ->assertJsonStructure([
                    'id',
                    'nombre',
                    'ubicacion',
                    'limites',
                    'created_at',
                    'updated_at'
                ])
                ->assertJson([
                    'nombre' => $data['nombre'],
                    'ubicacion' => $data['ubicacion'],
                    'limites' => $data['limites'],
                ]);

            $this->assertDatabaseHas('localidades', [
                'nombre' => $data['nombre'],
                'ubicacion' => json_encode($data['ubicacion']),
                'limites' => json_encode($data['limites']),
            ]);
        }

        // 🔴 Test negativos

        $test_roles = $this->_getRolesNegativos(['localidad.update']);

        foreach ($test_roles as $rol) {
            // Cambia el usuario autenticado antes de la solicitud
            $this->actingAs($this->usuarios[$rol]['usuario']);

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->putJson("{$this->api_slug}/{$localidad->id}", $data);

            $response->assertStatus(403); // No autorizado
        }
    }

    public function test_el_sistema_no_edita_una_localidad_con_datos_invalidos(): void
    {
        // Casos de datos inválidos
        $testCases = [
            // Caso 1: Enviar datos vacíos
            [
                'payload' => [],
                'expectedInvalid' => ['nombre', 'ubicacion', 'limites']
            ],
            // Caso 2: Enviar un nombre demasiado largo
            [
                'payload' => [
                    'nombre' => str_repeat('A', 256), // 101 caracteres
                ],
                'expectedInvalid' => ['nombre']
            ],
            // Caso 3: Coordenadas vacias
            [
                'payload' => [
                    'nombre' => 'Localidad Test',
                    'ubicacion' => ['latitud' => '', 'longitud' => ''],
                    'limites' => ['latitud_min' => '', 'latitud_max' => '', 'longitud_min' => '', 'longitud_max' => ''],
                ],
                'expectedInvalid' => ['ubicacion.latitud', 'ubicacion.longitud', 'limites.latitud_min', 'limites.latitud_max', 'limites.longitud_min', 'limites.longitud_max']
            ],
            // Caso 4: Coordenadas fuera de rango
            [
                'payload' => [
                    'nombre' => 'Localidad Test',
                    'ubicacion' => ['latitud' => -95, 'longitud' => 200],
                    'limites' => ['latitud_min' => -95, 'latitud_max' => 200, 'longitud_min' => -95, 'longitud_max' => 200],
                ],
                'expectedInvalid' => ['ubicacion.latitud', 'ubicacion.longitud', 'limites.latitud_min', 'limites.latitud_max', 'limites.longitud_min', 'limites.longitud_max']
            ],
            // Caso 5: Limites inválidos por min mayor que max
            [
                'payload' => [
                    'nombre' => 'Localidad Test',
                    'ubicacion' => ['latitud' => 90, 'longitud' => 90],
                    'limites' => ['latitud_min' => 95, 'latitud_max' => 94, 'longitud_min' => 90, 'longitud_max' => 89],
                ],
                'expectedInvalid' => ['limites.latitud_min', 'limites.longitud_min', 'limites.latitud_max', 'limites.longitud_max']
            ],

        ];

        $localidad = Localidad::factory()->create();

        $rol = $this->_getRolesPositivos(['localidad.update']);

        if (is_array($rol)) {
            $rol = $rol[0] ?? null;
        }
        if (!$rol) {
            return;
        }

        $token = $this->usuarios[$rol]['token'];

        foreach ($testCases as $case) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->putJson("{$this->api_slug}/{$localidad->id}", $case['payload']);
        }

        if (isset($case['expectedForbidden']) && $case['expectedForbidden']) {
            $response->assertForbidden(); // Verificar acceso denegado
        } else {
            $response->assertInvalid($case['expectedInvalid']);
        }
    }

    public function test_solo_los_usuarios_con_permiso_pueden_borrar_localidades(): void
    {
        // 🟢 Tests positivos

        $test_roles = $this->_getRolesPositivos(['localidad.delete']);

        foreach ($test_roles as $rol) {
            $this->actingAs($this->usuarios[$rol]['usuario']);
            $localidad = Localidad::factory()->create();

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->deleteJson("{$this->api_slug}/{$localidad->id}");

            $response->assertNoContent();

            // Verificar que el negocio no existe en la base de datos
            $this->assertDatabaseMissing(Localidad::class, [
                'id' => $localidad->id,
            ]);
        }

        // 🔴 Tests negativos
        $test_roles = $this->_getRolesNegativos(['localidad.delete']);

        $localidad = Localidad::factory()->create();

        foreach ($test_roles as $rol) {
            // Cambia el usuario autenticado antes de la solicitud
            $this->actingAs($this->usuarios[$rol]['usuario']);

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->deleteJson("{$this->api_slug}/{$localidad->id}");

            $response->assertStatus(403); // No autorizado

            // Verificar que la localidad existe en la base de datos
            $this->assertDatabaseHas(Localidad::class, [
                'id' => $localidad->id,
            ]);
        }
    }
}
