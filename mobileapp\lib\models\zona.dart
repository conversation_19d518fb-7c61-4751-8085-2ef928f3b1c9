import 'package:flutter/foundation.dart';

class Zona {
  final int id;
  final String nombre;
  final String descripcion;

  Zona({required this.id, required this.nombre, required this.descripcion});

  factory Zona.fromJson(Map<String, dynamic> json) {
    try {
      // Validar campos requeridos
      if (!json.containsKey('id') || json['id'] == null) {
        throw Exception('El campo id es requerido');
      }
      if (!json.containsKey('nombre') || json['nombre'] == null) {
        throw Exception('El campo nombre es requerido');
      }

      // Validar tipos
      if (json['id'] is! int) {
        throw Exception('El campo id debe ser un entero');
      }
      if (json['nombre'] is! String) {
        throw Exception('El campo nombre debe ser una cadena');
      }

      return Zona(
        id: json['id'],
        nombre: json['nombre'],
        descripcion: json['descripcion'] ?? '',
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error al procesar el JSON para Zona: $json');
        print('Error: $e');
      }
      // Asegurar que siempre lanzamos Exception (no TypeError u otros)
      if (e is! Exception) {
        throw Exception('Error al procesar JSON: $e');
      }
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nombre': nombre,
      'descripcion': descripcion,
    };
  }

  @override
  String toString() {
    return 'Zona(id: $id, nombre: $nombre, descripcion: $descripcion)';
  }
}
