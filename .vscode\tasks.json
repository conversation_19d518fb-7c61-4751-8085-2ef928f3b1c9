{"version": "2.0.0", "tasks": [{"type": "shell", "label": "generate-env-local", "command": "powershell", "args": ["-ExecutionPolicy", "Bypass", "-File", "${workspaceFolder}/mobileapp/scripts/generate-env.ps1", "-<PERSON><PERSON><PERSON>", "local", "-EnvFile", ".env.local"], "options": {"cwd": "${workspaceFolder}/mobileapp"}}, {"type": "shell", "label": "generate-env-staging", "command": "powershell", "args": ["-ExecutionPolicy", "Bypass", "-File", "scripts/generate-env.ps1", "-<PERSON><PERSON><PERSON>", "staging", "-EnvFile", ".env.staging"], "options": {"cwd": "${workspaceFolder}/mobileapp"}}, {"type": "shell", "label": "generate-env-production", "command": "powershell", "args": ["-ExecutionPolicy", "Bypass", "-File", "scripts/generate-env.ps1", "-<PERSON><PERSON><PERSON>", "production", "-EnvFile", ".env.production"], "options": {"cwd": "${workspaceFolder}/mobileapp"}}]}