<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class AppVersionFactory extends Factory
{
    public function definition()
    {
        return [
            'platform' => $this->faker->randomElement(['ios', 'android']),
            'latest_build' => (string) $this->faker->numberBetween(1, 1000),
            'minimum_build_allowed' => (string) $this->faker->numberBetween(1, 1000),
            'update_url' => '',
        ];
    }
}
