<?php

namespace App\Http\Controllers;

use App\Models\Negocio;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index()
    {
        // $negocios = Negocio::with('categoria')->get();

        $negocios = Negocio::all();

        return view('welcome', compact('negocios'));
    }

    public function show($id)
    {
        $negocio = Negocio::findOrFail($id);

        return view('negocio.show', compact('negocio'));
    }
}
