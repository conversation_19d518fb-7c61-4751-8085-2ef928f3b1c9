name: Flutter Build Android

on:
   workflow_dispatch:

jobs:
   build:
      runs-on: ubuntu-latest
      steps:
         - uses: actions/checkout@v3

         - name: Set up Java
           uses: actions/setup-java@v2
           with:
              distribution: "zulu"
              java-version: "17"

         - name: Set up Flutter
           uses: subosito/flutter-action@v2
           with:
              flutter-version: "3.29.0"
              channel: "stable"

         - name: Check Flutter and Dart versions
           run: |
              flutter config --no-analytics
              flutter --version
              dart --version

         - name: Install dependencies
           working-directory: ./mobileapp
           run: |
              flutter config --no-analytics
              flutter pub get

         - name: Generate .env for Staging
           working-directory: ./mobileapp
           run: echo "${{ secrets.STAGING_FLUTTER_DOT_ENV }}" > .env

         - name: Build Android Staging (Debug Mode)
           working-directory: ./mobileapp
           run: |
              flutter build apk --dart-define=FLAVOR=staging -t lib/main.dart
              mv build/app/outputs/flutter-apk/app-release.apk build/app/outputs/flutter-apk/app-staging-debug.apk

         - name: Generate .env for Production
           working-directory: ./mobileapp
           run: echo "${{ secrets.PRODUCTION_FLUTTER_DOT_ENV }}" > .env

         - name: Build Android Production (Release Mode)
           working-directory: ./mobileapp
           run: |
              flutter build apk --release --dart-define=FLAVOR=production -t lib/main.dart
              mv build/app/outputs/flutter-apk/app-release.apk build/app/outputs/flutter-apk/app-production-release.apk

         - name: Build Android App Bundle for Production
           working-directory: ./mobileapp
           run: flutter build appbundle --release --dart-define=FLAVOR=production -t lib/main.dart

         - name: Upload Android APKs
           uses: actions/upload-artifact@v4
           with:
              name: android-apks
              path: |
                 mobileapp/build/app/outputs/flutter-apk/app-staging-debug.apk
                 mobileapp/build/app/outputs/flutter-apk/app-production-release.apk
              retention-days: 3

         - name: Upload Android AAB
           uses: actions/upload-artifact@v4
           with:
              name: android-aab
              path: mobileapp/build/app/outputs/bundle/release/*.aab
              retention-days: 3
