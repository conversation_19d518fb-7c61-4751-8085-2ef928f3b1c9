<?php

namespace App\Filament\Forms\Components;

use Filament\Forms\Components\Field;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class CustomKeyValue extends Field
{
    protected string $view = 'filament.forms.components.custom-key-value';

    protected array $default = [
        'lunes'     => '',
        'martes'    => '',
        'miércoles' => '',
        'jueves'    => '',
        'viernes'   => '',
        'sábado'    => '',
        'domingo'   => '',
    ];

    public function keyLabel(string $label): static
    {
        return $this->configure(['keyLabel' => $label]);
    }

    public function valueLabel(string $label): static
    {
        return $this->configure(['valueLabel' => $label]);
    }

    public function addable(bool $condition = true): static
    {
        return $this->configure(['addable' => $condition]);
    }

    public function deletable(bool $condition = true): static
    {
        return $this->configure(['deletable' => $condition]);
    }

    public function editableKeys(bool $condition = true): static
    {
        return $this->configure(['editableKeys' => $condition]);
    }

    // public function hint(string $hint): static
    // {
    //     return $this->configure(['hint' => $hint]);
    // }

    // public function hintIcon(string $icon): static
    // {
    //     return $this->configure(['hintIcon' => $icon]);
    // }

    // Puedes agregar aquí la lógica para las reglas de validación.
    // Por ejemplo, puedes definir un método para agregar las reglas
    // que se aplicarán a cada valor:
    public function registerRules(): static
    {
        return $this->rules([
            'required',
            'array',
            'min:7',
            Rule::forEach(function ($value) {
                return [
                    'nullable',
                    // Valida el formato de hora o cadenas vacías
                    'regex:/^$|^(([01]\d|2[0-3]):[0-5]\d-([01]\d|2[0-3]):[0-5]\d(,([01]\d|2[0-3]):[0-5]\d-([01]\d|2[0-3]):[0-5]\d)*)?$/',
                    function ($attribute, $value, $fail) {
                        if (!empty($value)) {
                            $ranges = explode(',', $value);
                            foreach ($ranges as $range) {
                                $times = explode('-', $range);
                                if (count($times) == 2) {
                                    $startTime = Carbon::createFromFormat('H:i', $times[0]);
                                    $endTime   = Carbon::createFromFormat('H:i', $times[1]);
                                    if ($startTime >= $endTime) {
                                        $fail("El horario $range no es válido. La hora de inicio debe ser anterior a la hora de fin.");
                                    }
                                }
                            }
                        }
                    }
                ];
            }),
        ]);
    }
}
