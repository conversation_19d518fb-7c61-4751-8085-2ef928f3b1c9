{{-- webapp\resources\views\filament\pages\dashboard.blade.php --}}
<x-filament-panels::page class="fi-dashboard-page">
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <x-filament-widgets::widgets
        :columns="$this->getColumns()"
        :data="[
            ...$this->getWidgetData(),
        ]"
        :widgets="$this->getVisibleWidgets()"
    />

    <x-filament::modal
        id="edit-negocio"
        width="7xl"
        wire:closed="cerrarModal"
    >
        @if($negocioIdParaEditar)
            @livewire(\App\Filament\Resources\NegocioResource\Pages\EditNegocio::class, [
                'record' => $negocioIdParaEditar
            ], key('edit-negocio-'.$negocioIdParaEditar))
        @endif
    </x-filament::modal>

</x-filament-panels::page>