<?php

namespace App\Observers;

use App\Models\Evento;
use App\Models\Negocio;
use App\Services\CacheService;

class EventoObserver
{
    protected CacheService $cache;

    public function __construct(CacheService $cache)
    {
        $this->cache = $cache;
    }
    /**
     * Handle the Evento "created" event.
     */
    public function created(Evento $evento): void
    {
        $this->invalidate($evento);
    }

    /**
     * Handle the Evento "updated" event.
     */
    public function updated(Evento $evento): void
    {
        $this->invalidate($evento);
    }

    /**
     * Handle the Evento "deleted" event.
     */
    public function deleted(Evento $evento): void
    {
        $this->invalidate($evento);
    }

    /**
     * Handle the Evento "restored" event.
     */
    public function restored(Evento $evento): void
    {
        $this->invalidate($evento);
    }

    /**
     * Handle the Evento "force deleted" event.
     */
    public function forceDeleted(Evento $evento): void
    {
        $this->invalidate($evento);
    }

    private function invalidate(Evento $evento): void
    {

        $this->cache->invalidateCache(Evento::class, 'eventos_all');
        $this->cache->invalidateCache(Evento::class, 'eventos_futuros');
        $this->cache->invalidateCache(Negocio::class, 'negocios_all');
    }
}
