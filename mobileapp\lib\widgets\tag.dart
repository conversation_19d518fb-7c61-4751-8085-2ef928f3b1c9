import 'package:flutter/material.dart';
import 'package:mia/config/colors.dart';
import 'package:mia/config/styles.dart';

class Tag extends StatefulWidget {
  final String label;
  final String description;
  final bool isSelected;
  final VoidCallback? onTap;
  final VoidCallback? onClose;
  final Color? backgroundColorDefault;
  final Color? backgroundColorActive;
  final Color? textColorDefault;
  final Color? textColorActive;
  final String appStyle;

  // New border-related parameters
  final double? borderWidth;
  final Color? borderColorDefault;
  final Color? borderColorActive;

  const Tag({
    super.key,
    required this.label,
    required this.description,
    this.isSelected = false,
    this.onTap,
    this.onClose,
    this.backgroundColorDefault,
    this.backgroundColorActive,
    this.textColorDefault,
    this.textColorActive,
    this.appStyle = 'base',
    this.borderWidth,
    this.borderColorDefault,
    this.borderColorActive,
  });

  @override
  State<Tag> createState() => _TagState();
}

class _TagState extends State<Tag> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 2),
        decoration: BoxDecoration(
          color: widget.isSelected
              ? widget.backgroundColorActive ?? AppColors.current.secondaryColor
              : widget.backgroundColorDefault ??
                  AppColors.current.tertiaryColor,
          borderRadius: BorderRadius.circular(20),

          // Add optional border
          border: widget.borderWidth != null
              ? Border.all(
                  color: widget.isSelected
                      ? widget.borderColorActive ??
                          AppColors.current.primaryColor
                      : widget.borderColorDefault ??
                          AppColors.current.textColor,
                  width: widget.borderWidth!,
                )
              : null,
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                widget.label,
                style: AppStyles.getStyle(
                  context,
                  widget.appStyle,
                  fontWeight: widget.isSelected ? 'bold' : 'medium',
                  color: widget.isSelected
                      ? widget.textColorActive ?? AppColors.current.primaryColor
                      : widget.textColorDefault ?? AppColors.current.textColor,
                ),
                softWrap: false,
                overflow: TextOverflow.ellipsis,
              ),
              if (widget.isSelected) ...[
                const SizedBox(width: 4),
                InkWell(
                  onTap: widget.onClose,
                  child: Icon(
                    Icons.close,
                    size: 16,
                    color: AppColors.current.primaryColor,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
