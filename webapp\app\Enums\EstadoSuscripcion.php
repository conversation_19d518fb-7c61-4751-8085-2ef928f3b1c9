<?php

namespace App\Enums;

enum EstadoSuscripcion: int
{
    case INACTIVE = 0;
    case ACTIVE = 1;
    // case CANCELLED = 2;
    // Agrega más estados si lo necesitas

    public function message(): string
    {
        return match ($this) {
            self::INACTIVE => 'La suscripción está inactiva',
            self::ACTIVE => 'La suscripción está activa',
            // self::CANCELLED => 'La suscripción ha sido cancelada',
        };
    }

    public function label(): string
    {
        return match ($this) {
            self::INACTIVE => 'Inactiva',
            self::ACTIVE => 'Activa',
            // self::CANCELLED => 'Cancelada',
        };
    }

    public static function getAsOptions(): array
    {
        $options = [];
        foreach (self::cases() as $case) {
            $options[$case->value] = $case->label();
        }

        return $options;
    }
}
