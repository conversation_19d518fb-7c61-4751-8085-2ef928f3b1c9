<?php

namespace Database\Seeders;

use App\Models\Suscripcion;
use App\Models\PagoSuscripcion;
use Illuminate\Database\Seeder;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class PagoSuscripcionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $suscripcionesActivas = Suscripcion::activas()->facturables()->get();

        $suscripcionesActivas->each(function ($suscripcion) {
            PagoSuscripcion::factory(rand(0, 1))->create([
                'suscripcion_id' => $suscripcion->id,
            ]);
        });
    }
}
