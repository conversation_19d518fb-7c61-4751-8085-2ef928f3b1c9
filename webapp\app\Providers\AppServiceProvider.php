<?php

namespace App\Providers;

use App\Models\Evento;
use App\Models\Negocio;
use App\Models\Categoria;
use App\Models\AppVersion;
use App\Models\Localidad;
use App\Models\Zona;
use App\Policies\RolePolicy;
use App\Observers\EventoObserver;
use App\Observers\NegocioObserver;
use App\Policies\PermissionPolicy;
use Spatie\Permission\Models\Role;
use App\Observers\CategoriaObserver;
use Illuminate\Support\Facades\Gate;
use App\Observers\AppVersionObserver;
use App\Observers\LocalidadObserver;
use App\Observers\ZonaObserver;
use Illuminate\Support\ServiceProvider;
use Spatie\Permission\Models\Permission;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Registrar observadores
        Localidad::observe(LocalidadObserver::class);
        Zona::observe((ZonaObserver::class));
        Categoria::observe(CategoriaObserver::class);
        Negocio::observe(NegocioObserver::class);
        Evento::observe(EventoObserver::class);
        AppVersion::observe(AppVersionObserver::class);


        Gate::policy(Role::class, RolePolicy::class);
        Gate::policy(Permission::class, PermissionPolicy::class);
    }
}
