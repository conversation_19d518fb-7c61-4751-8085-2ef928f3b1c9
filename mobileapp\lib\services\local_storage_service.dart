// lib/services/local_storage_service.dart

import 'dart:convert';
import 'package:mia/models/negocio.dart';
import 'package:mia/models/zona.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:mia/models/categoria.dart';

class LocalStorageService {
  Future<void> setData(String key, List<dynamic> data) async {
    final prefs = await SharedPreferences.getInstance();
    final jsonList = data.map((item) => jsonEncode(item)).toList();
    await prefs.setStringList(key, jsonList);
  }

  Future<List<dynamic>?> getData(String key) async {
    final prefs = await SharedPreferences.getInstance();
    final jsonList = prefs.getStringList(key);
    if (jsonList != null) {
      return jsonList.map((json) => jsonDecode(json)).toList();
    }
    return null;
  }

  Future<void> setLastModified(String key, String value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('last_modified_$key', value);
  }

  Future<String?> getLastModified(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('last_modified_$key');
  }

  Future<void> setCategorias(List<Categoria> categorias) async {
    final prefs = await SharedPreferences.getInstance();
    final categoriasJson =
        categorias.map((categoria) => jsonEncode(categoria.toJson())).toList();
    await prefs.setStringList('categorias', categoriasJson);
  }

  Future<List<Categoria>?> getCategorias() async {
    final prefs = await SharedPreferences.getInstance();
    final categoriasJson = prefs.getStringList('categorias');
    if (categoriasJson != null) {
      return categoriasJson
          .map((json) => Categoria.fromJson(jsonDecode(json)))
          .toList();
    }
    return null;
  }

  Future<void> setZonas(List<Zona> zonas) async {
    final prefs = await SharedPreferences.getInstance();
    final zonasJson = zonas.map((zona) => jsonEncode(zona.toJson())).toList();
    await prefs.setStringList('zonas', zonasJson);
  }

  Future<List<Zona>?> getZonas() async {
    final prefs = await SharedPreferences.getInstance();
    final zonasJson = prefs.getStringList('zonas');
    if (zonasJson != null) {
      return zonasJson.map((json) => Zona.fromJson(jsonDecode(json))).toList();
    }
    return null;
  }

  Future<void> setNegocios(List<Negocio> negocios) async {
    final prefs = await SharedPreferences.getInstance();
    final negociosJson =
        negocios.map((negocio) => jsonEncode(negocio.toJson())).toList();
    await prefs.setStringList('negocios', negociosJson);
  }

  Future<List<Negocio>?> getNegocios() async {
    final prefs = await SharedPreferences.getInstance();
    final negociosJson = prefs.getStringList('negocios');
    if (negociosJson != null) {
      return negociosJson
          .map((json) => Negocio.fromJson(jsonDecode(json)))
          .toList();
    }
    return null;
  }
}
