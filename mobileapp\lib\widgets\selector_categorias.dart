import 'package:flutter/material.dart';
import 'package:mia/config/styles.dart';
import 'package:mia/models/categoria.dart';
import 'package:mia/services/category_service.dart';
import 'package:mia/widgets/tag.dart';

class SelectorCategorias extends StatefulWidget {
  final List<Categoria>? categorias;
  final void Function(List<Categoria>)? onChanged;

  const SelectorCategorias(
      {super.key, required this.categorias, this.onChanged});

  @override
  State<SelectorCategorias> createState() => _SelectorCategoriasState();
}

class _SelectorCategoriasState extends State<SelectorCategorias> {
  final CategoryService _categoryService = CategoryService();
  final List<Categoria> _categoriasSeleccionadas = [];
  List<Categoria> _categoriasPrincipales = [];
  final Map<String, List<Categoria>> _subcategorias = {};

  @override
  void initState() {
    super.initState();
    _cargarCategorias();
  }

  Future<void> _cargarCategorias() async {
    if (widget.categorias != null) {
      _categoriasPrincipales =
          _categoryService.getMainCategories(widget.categorias!);
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        _buildCategoryList(),
        if (_categoriasSeleccionadas.isNotEmpty) ...[
          const SizedBox(height: 10),
          _buildSubcategoryList(),
        ],
      ],
    );
  }

  Widget _buildCategoryList() {
    if (widget.categorias == null || widget.categorias!.isEmpty) {
      return Center(
        child: Text(
          'No hay categorías disponibles.',
          style: AppStyles.getStyle(context, 'base', fontWeight: 'bold'),
        ),
      );
    } else {
      return Wrap(
        spacing: 8.0,
        runSpacing: 4.0,
        children: _categoriasPrincipales.map((categoria) {
          final isSelected =
              _categoriasSeleccionadas.any((cat) => cat.id == categoria.id);
          return _buildCategoryTag(categoria, isSelected);
        }).toList(),
      );
    }
  }

  Tag _buildCategoryTag(Categoria categoria, bool isSelected) {
    return Tag(
      label: categoria.nombre,
      description: categoria.descripcion ?? '',
      isSelected: isSelected,
      onTap: () {
        setState(() {
          if (isSelected) {
            _categoriasSeleccionadas.remove(categoria);
            _categoryService.removeSubcategorias(
                _categoriasSeleccionadas, _subcategorias, categoria);
            _subcategorias.remove(categoria.nombre);
          } else {
            _categoriasSeleccionadas.add(categoria);
            _subcategorias.addAll(_categoryService.cargarSubcategorias(
                widget.categorias!, categoria));
          }

          // Llamar a onChanged si se proporciona
          if (widget.onChanged != null) {
            widget.onChanged!(_categoriasSeleccionadas);
          }
        });
        // debugPrint(
        //     "Categorias seleccionadas: ${_categoriasSeleccionadas.map((cat) => cat.nombre).toList()}");
      },
      onClose: isSelected
          ? () {
              setState(() {
                _categoriasSeleccionadas.remove(categoria);
                _categoryService.removeSubcategorias(
                    _categoriasSeleccionadas, _subcategorias, categoria);
                _subcategorias.remove(categoria.nombre);

                // Llamar a onChanged si se proporciona
                if (widget.onChanged != null) {
                  widget.onChanged!(_categoriasSeleccionadas);
                }
              });
            }
          : null,
    );
  }

  Widget _buildSubcategoryList() {
    List<Widget> subcategoryWidgets = [];

    for (var categoria in _categoriasSeleccionadas) {
      if (_subcategorias.containsKey(categoria.nombre)) {
        subcategoryWidgets.addAll(_buildSubcategoryTagsRecursively(
            _subcategorias[categoria.nombre]!));
      }
    }

    return Wrap(
      spacing: 8.0,
      runSpacing: 4.0,
      children: subcategoryWidgets,
    );
  }

  List<Widget> _buildSubcategoryTagsRecursively(List<Categoria> subcategorias) {
    List<Widget> subcategoryWidgets = [];

    for (var subcategoria in subcategorias) {
      final isSelected =
          _categoriasSeleccionadas.any((cat) => cat.id == subcategoria.id);

      subcategoryWidgets.add(
        Tag(
          label: subcategoria.nombre,
          description: subcategoria.descripcion ?? '',
          isSelected: isSelected,
          onTap: () {
            setState(() {
              if (isSelected) {
                _categoriasSeleccionadas.remove(subcategoria);
                _categoryService.removeSubcategorias(
                    _categoriasSeleccionadas, _subcategorias, subcategoria);
                _subcategorias.remove(subcategoria.nombre);
              } else {
                _categoriasSeleccionadas.add(subcategoria);
                _subcategorias.addAll(_categoryService.cargarSubcategorias(
                    widget.categorias!, subcategoria));
              }
              // Llamar a onChanged si se proporciona
              if (widget.onChanged != null) {
                widget.onChanged!(_categoriasSeleccionadas);
              }
            });

            // debugPrint(
            //     "Categorias seleccionadas: ${_categoriasSeleccionadas.map((cat) => cat.nombre).toList()}");
          },
          onClose: isSelected
              ? () {
                  setState(() {
                    _categoriasSeleccionadas.remove(subcategoria);
                    _categoryService.removeSubcategorias(
                        _categoriasSeleccionadas, _subcategorias, subcategoria);
                    _subcategorias.remove(subcategoria.nombre);

                    // Llamar a onChanged si se proporciona
                    if (widget.onChanged != null) {
                      widget.onChanged!(_categoriasSeleccionadas);
                    }
                  });
                }
              : null,
        ),
      );
    }
    return subcategoryWidgets;
  }

  // Exponer las categorías seleccionadas
  List<Categoria> get categoriasSeleccionadas => _categoriasSeleccionadas;
}
