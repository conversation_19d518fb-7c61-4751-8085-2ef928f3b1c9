<?php

namespace App\Filament\Resources;

use Closure;
use Filament\Forms;
use App\Models\User;
use App\Models\Zona;
use Filament\Tables;
use App\Models\Negocio;
use Filament\Forms\Form;
use App\Models\Localidad;
use Filament\Tables\Table;
use App\Services\CacheService;
use Filament\Resources\Resource;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\KeyValue;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Forms\Components\MapField;
use App\Filament\Forms\Components\BoundaryMapField;
use App\Filament\Resources\LocalidadResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Forms\Components\MapRectangleField;
use App\Filament\Resources\LocalidadResource\RelationManagers;

class LocalidadResource extends Resource
{
    protected static ?string $model = Localidad::class;

    protected static ?string $navigationGroup = 'Gestión';

    protected static ?string $navigationLabel = 'Localidades';

    protected static ?string $modelLabel = 'Localidad';

    protected static ?string $pluralModelLabel = 'Localidades';

    protected static ?string $navigationIcon = 'heroicon-s-globe-europe-africa';

    protected static ?string $slug = 'localidades';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('nombre')
                    ->required()
                    ->maxLength(255),
                Section::make('Ubicación')
                    ->schema([
                        MapField::make('ubicacion')
                            ->required()
                            ->default(function ($context, $record) {
                                if ($context === 'edit') {
                                    return [
                                        'latitud' => $record->ubicacion['latitud'] ?? '',
                                        'longitud' => $record->ubicacion['longitud'] ?? '',
                                    ];
                                } else {
                                    return [
                                        'latitud' => '',
                                        'longitud' => '',
                                    ];
                                }
                            })
                            ->columnSpan(1)
                            ->label('Ubicación')
                            ->rules([
                                fn(): Closure => function (string $attribute, $value, Closure $fail) {
                                    $latitud = $value['latitud'] ?? '';
                                    $longitud = $value['longitud'] ?? '';

                                    if ($latitud === '' || $longitud === '') {
                                        $fail('La ubicación es obligatoria y debe incluir latitud y longitud.');
                                    }

                                    if (!is_numeric($latitud) || !is_numeric($longitud)) {
                                        $fail('La latitud y la longitud deben ser valores numéricos.');
                                    }

                                    if ($latitud < -90 || $latitud > 90 || $longitud < -180 || $longitud > 180) {
                                        $fail('La latitud debe estar entre -90 y 90 grados y la longitud entre -180 y 180 grados.');
                                    }
                                },
                            ]),
                        MapRectangleField::make('limites')
                            ->columnSpan('full')
                            ->required()
                            ->default(function ($context, $record) {
                                if ($context === 'edit') {
                                    return [
                                        'latitud_min' => $record->limites['latitud_min'] ?? '',
                                        'latitud_max' => $record->limites['latitud_max'] ?? '',
                                        'longitud_min' => $record->limites['longitud_min'] ?? '',
                                        'longitud_max' => $record->limites['longitud_max'] ?? '',
                                    ];
                                } else {
                                    return [
                                        'latitud_min' => '',
                                        'latitud_max' => '',
                                        'longitud_min' => '',
                                        'longitud_max' => '',
                                    ];
                                }
                            })
                            ->rules([
                                fn(): Closure => function (string $attribute, $value, Closure $fail) {
                                    $latitud_min = $value['latitud_min'] ?? '';
                                    $latitud_max = $value['latitud_max'] ?? '';
                                    $longitud_min = $value['longitud_min'] ?? '';
                                    $longitud_max = $value['longitud_max'] ?? '';

                                    if ($latitud_min === '' || $latitud_max === '' || $longitud_min === '' || $longitud_max === '') {
                                        $fail('Los límites son obligatorios y deben incluir latitud_min, latitud_max, longitud_min y longitud_max.');
                                    }

                                    if (!is_numeric($latitud_min) || !is_numeric($latitud_max) || !is_numeric($longitud_min) || !is_numeric($longitud_max)) {
                                        $fail('Los límites deben ser valores numéricos.');
                                    }

                                    if ($latitud_min > $latitud_max || $longitud_min > $longitud_max) {
                                        $fail('Las latitudes y longitudes mínimas deben ser menores que las máximas.');
                                    }

                                    if ($latitud_min < -90 || $latitud_min > 90 || $latitud_max < -90 || $latitud_max > 90) {
                                        $fail('Las latitudes deben estar entre -90 y 90 grados.');
                                    }

                                    if ($longitud_min < -180 || $longitud_min > 180 || $longitud_max < -180 || $longitud_max > 180) {
                                        $fail('Las longitudes deben estar entre -180 y 180 grados.');
                                    }
                                },  // Agrega más reglas si es necesario
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('nombre'),
                TextColumn::make('ubicacion'),
                TextColumn::make('limites'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->action(function ($record) {
                        $record->delete();
                        $service = app(CacheService::class);
                        $service->invalidateCache(Localidad::class, 'localidades_all');
                        $service->invalidateCache(Zona::class, 'zonas_all');
                        $service->invalidateCache(Negocio::class, 'negocios_all');
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->action(function ($records) {
                            $records->each(function ($record) {
                                $record->delete();
                            });
                            $service = app(CacheService::class);
                            $service->invalidateCache(Localidad::class, 'localidades_all');
                            $service->invalidateCache(Zona::class, 'zonas_all');
                            $service->invalidateCache(Negocio::class, 'negocios_all');
                        }),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLocalidades::route('/'),
            'create' => Pages\CreateLocalidad::route('/create'),
            'edit' => Pages\EditLocalidad::route('/{record}/edit'),
        ];
    }

    public static function shouldRegisterNavigation(): bool
    {
        /** @var User */
        $user = Auth::user();
        return $user->can('localidad.list');
    }
}
