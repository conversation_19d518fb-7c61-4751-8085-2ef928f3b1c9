<?php

namespace Database\Factories;

use App\Models\Zona;
use App\Models\Localidad;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Zona>
 */
class ZonaFactory extends Factory
{
    protected $model = Zona::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'nombre' => $this->faker->unique()->streetName,
            'descripcion' => $this->faker->sentence(),
            'coordenadas' =>
            [
                $this->faker->longitude() . "," . $this->faker->latitude(),
                $this->faker->longitude() . "," . $this->faker->latitude(),
                $this->faker->longitude() . "," . $this->faker->latitude(),
                $this->faker->longitude() . "," . $this->faker->latitude(),
                $this->faker->longitude() . "," . $this->faker->latitude(),
            ],
            // 'localidad_id' => Localidad::query()->exists()
            //     ? Localidad::query()->inRandomOrder()->value('id')
            //     : Localidad::factory()->create()->id,
            'localidad_id' => Localidad::factory()->create()->id,
        ];
    }
}
