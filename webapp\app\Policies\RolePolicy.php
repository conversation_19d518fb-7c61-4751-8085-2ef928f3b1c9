<?php

namespace App\Policies;

use App\Models\User;
use Spatie\Permission\Models\Role;

class RolePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('system.roles');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Role $role): bool
    {
        return $user->can('system.roles');
    }

    /**
     * Determine whether the user can create models.
     */ public function create(User $user): bool
    {
        return $user->can('system.roles');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Role $role): bool
    {
        if ($role->name === 'admin') {
            return false;
        }

        return $user->can('system.roles');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Role $role): bool
    {
        if ($role->name === 'admin' || $role->name === 'owner' || $role->name === 'cliente' || $role->name === 'usuario') {
            return false;
        }

        return $user->can('system.roles');
    }
}
