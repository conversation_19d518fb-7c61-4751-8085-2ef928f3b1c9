<?php

namespace Database\Seeders;

use App\Models\Localidad;
use App\Models\Zona;
use App\Services\DataGeneratorService;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ZonaSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Crear zonas desde DataGeneratorService
        $localidades = DataGeneratorService::getLocalidades();

        // pasar el array a una collection de Localidad
        $localidades = Localidad::whereIn('nombre', array_column($localidades, 'nombre'))->get();

        foreach ($localidades as $localidad) {
            $zonas = DataGeneratorService::getZonas($localidad->nombre);

            foreach ($zonas as $zona) {
                Zona::create([
                    'nombre' => $zona['nombre'],
                    'descripcion' => $zona['descripcion'],
                    'localidad_id' => $localidad->id,
                ]);
            }
        }

        // Zona::factory()->create([
        //     'nombre' => 'Centro',
        //     'descripcion' => 'El centro de Matalascañas es el corazón vibrante de la localidad, ofreciendo una amplia gama de opciones de ocio y entretenimiento. Por la noche, la zona se llena de vida con música en vivo y eventos culturales, brindando a los visitantes una experiencia animada y dinámica.'
        // ]);

        // Zona::factory()->create([
        //     'nombre' => 'Caño Guerrero',
        //     'descripcion' => 'Situado en el extremo oriental de Matalascañas, es conocido por su hermoso paseo marítimo que ofrece vistas panorámicas al océano. Esta área cuenta con una variedad de restaurantes y bares frente al mar.'
        // ]);

        // Zona::factory()->create(3);
    }
}
