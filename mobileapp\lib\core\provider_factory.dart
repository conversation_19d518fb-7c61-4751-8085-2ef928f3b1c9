import 'package:mia/providers/favorites_provider.dart';
import 'package:mia/providers/location_provider.dart';
import 'package:mia/providers/permission_provider.dart';
import 'package:mia/providers/settings_provider.dart';
import 'package:mia/providers/theme_provider.dart';
import 'package:provider/provider.dart';

abstract class ProviderFactory {
  List<ChangeNotifierProvider> createProviders();
}

class DefaultProviderFactory implements ProviderFactory {
  @override
  List<ChangeNotifierProvider> createProviders() {
    return [
      ChangeNotifierProvider(create: (_) => ThemeProvider()),
      ChangeNotifierProvider(create: (_) => SettingsProvider()),
      ChangeNotifierProvider(create: (_) => FavoritesProvider()),
      ChangeNotifierProvider(create: (_) => PermissionProvider()),
      ChangeNotifierProvider(create: (_) => LocationProvider()),
    ];
  }
}
