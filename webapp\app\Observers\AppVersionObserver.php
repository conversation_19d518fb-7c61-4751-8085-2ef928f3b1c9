<?php

namespace App\Observers;

use App\Models\AppVersion;
use App\Services\CacheService;

class AppVersionObserver
{
    /**
     * Handle the AppVersion "created" event.
     */
    public function created(AppVersion $appVersion): void
    {
        $service = app(CacheService::class);
        $service->invalidateCache(AppVersion::class, 'app_versions_all');
    }

    /**
     * Handle the AppVersion "updated" event.
     */
    public function updated(AppVersion $appVersion): void
    {
        $service = app(CacheService::class);
        $service->invalidateCache(AppVersion::class, 'app_versions_all');
    }

    /**
     * Handle the AppVersion "deleted" event.
     */
    public function deleted(AppVersion $appVersion): void
    {
        $service = app(CacheService::class);
        $service->invalidateCache(AppVersion::class, 'app_versions_all');
    }

    /**
     * Handle the AppVersion "restored" event.
     */
    public function restored(AppVersion $appVersion): void
    {
        $service = app(CacheService::class);
        $service->invalidateCache(AppVersion::class, 'app_versions_all');
    }

    /**
     * Handle the AppVersion "force deleted" event.
     */
    public function forceDeleted(AppVersion $appVersion): void
    {
        $service = app(CacheService::class);
        $service->invalidateCache(AppVersion::class, 'app_versions_all');
    }
}
