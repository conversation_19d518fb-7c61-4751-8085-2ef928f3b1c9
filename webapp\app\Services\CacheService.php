<?php

namespace App\Services;

use Illuminate\Support\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CacheService
{
    /**
     * Prefijo para las claves de caché de timestamp
     */
    const TIMESTAMP_PREFIX = 'last_modified_';

    private $debug = false;

    /**
     * Calcula la última fecha de modificación de una colección
     * 
     * @param Collection $collection La colección a evaluar
     * @param string $createdAtField Campo de fecha de creación
     * @param string $updatedAtField Campo de fecha de actualización
     * @return Carbon
     */
    public function calculateLastModified($collection, $createdAtField = 'created_at', $updatedAtField = 'updated_at')
    {
        // Obtener el tipo de recurso incluso para colecciones vacías
        $resourceType = null;

        if ($collection instanceof Collection) {
            $firstItem = $collection->first();
            $resourceType = $firstItem ? get_class($firstItem) : null;

            // Para colecciones vacías, consultar directamente el timestamp guardado
            if (!$firstItem && $resourceType === null) {
                // Intenta obtener el tipo desde la colección si es posible
                if (method_exists($collection, 'getModel')) {
                    $resourceType = get_class($collection->getModel());
                } else {
                    return Carbon::now(); // Fallback si no podemos determinar el tipo
                }
            }

            $storedTimestamp = $firstItem
                ? $this->getStoredTimestamp(get_class($firstItem))
                : Carbon::createFromTimestamp(0);

            // Validar los timestamps máximos
            $maxCreatedAt = $collection->max($createdAtField);
            $validMaxCreatedAt = $maxCreatedAt instanceof Carbon
                ? $maxCreatedAt
                : ($maxCreatedAt ? Carbon::parse($maxCreatedAt) : Carbon::createFromTimestamp(0));

            $maxUpdatedAt = $collection->max($updatedAtField);
            $validMaxUpdatedAt = $maxUpdatedAt instanceof Carbon
                ? $maxUpdatedAt
                : ($maxUpdatedAt ? Carbon::parse($maxUpdatedAt) : Carbon::createFromTimestamp(0));

            return max(
                $validMaxCreatedAt,
                $validMaxUpdatedAt,
                $storedTimestamp
            );
        } elseif (is_object($collection)) {
            // Para objetos individuales también validamos
            $createdAt = $collection->{$createdAtField} ?? null;
            $validCreatedAt = $createdAt instanceof Carbon
                ? $createdAt
                : ($createdAt ? Carbon::parse($createdAt) : Carbon::createFromTimestamp(0));

            $updatedAt = $collection->{$updatedAtField} ?? null;
            $validUpdatedAt = $updatedAt instanceof Carbon
                ? $updatedAt
                : ($updatedAt ? Carbon::parse($updatedAt) : Carbon::createFromTimestamp(0));

            return max(
                $validCreatedAt,
                $validUpdatedAt,
                $this->getStoredTimestamp(get_class($collection))
            );
        }

        return Carbon::now();
    }

    /**
     * Obtiene el timestamp guardado para un recurso
     * 
     * @param string $resourceType Tipo de recurso (generalmente el nombre de la clase)
     * @return Carbon
     */
    protected function getStoredTimestamp($resourceType)
    {
        $key = self::TIMESTAMP_PREFIX . $this->normalizeResourceType($resourceType);
        $timestamp = Cache::get($key);

        return $timestamp ? Carbon::createFromTimestamp($timestamp) : Carbon::createFromTimestamp(0);
    }

    /**
     * Actualiza el timestamp de última modificación de un recurso
     * 
     * @param string $resourceType Tipo de recurso (generalmente el nombre de la clase)
     * @param Carbon|null $timestamp Timestamp a guardar (usa now() si es null)
     * @return void
     */
    public function updateTimestamp($resourceType, $timestamp = null)
    {
        $key = self::TIMESTAMP_PREFIX . $this->normalizeResourceType($resourceType);

        // Asegurar que el nuevo Last-Modified siempre sea mayor que cualquier If-Modified-Since recibido.
        $timestamp = ($timestamp ?? Carbon::now())->addSecond();

        Cache::put($key, $timestamp->timestamp, now()->addYear());
    }

    /**
     * Normaliza el nombre del tipo de recurso
     * 
     * @param string $resourceType
     * @return string
     */
    protected function normalizeResourceType($resourceType)
    {
        return strtolower(str_replace('\\', '_', $resourceType));
    }

    /**
     * Formatea una fecha para el header Last-Modified
     * 
     * @param Carbon $lastModified
     * @return string
     */
    public function formatLastModified(Carbon $lastModified)
    {
        return $lastModified->toRfc1123String();
    }

    /**
     * Verifica si el recurso no ha sido modificado según el header If-Modified-Since
     * 
     * @param Carbon $lastModified Fecha de última modificación
     * @return boolean
     */
    public function isNotModified(Carbon $lastModified)
    {
        $ifModifiedSince = request()->header('If-Modified-Since');

        if (!$ifModifiedSince) {

            $this->debug ? Log::debug('[CacheService] No If-Modified-Since header present') : null;
            return false;
        }

        // Intentamos parsear con Carbon (más robusto que strtotime)
        try {
            $ifDt = \Carbon\Carbon::parse($ifModifiedSince);
        } catch (\Exception $e) {
            // fallback
            $ts = strtotime($ifModifiedSince);
            if ($ts === false || $ts === null) {
                $this->debug ? Log::debug('[CacheService] Could not parse If-Modified-Since: ' . $ifModifiedSince) : null;
                return false;
            }
            $ifDt = \Carbon\Carbon::createFromTimestamp($ts);
        }

        if ($this->debug) {
            Log::debug('[CacheService] Parsed If-Modified-Since: ' . $ifDt->toRfc1123String() . ' ts:' . $ifDt->timestamp);
            Log::debug('[CacheService] LastModified: ' . $lastModified->toRfc1123String() . ' ts:' . $lastModified->timestamp);
        }

        // según RFC: NOT MODIFIED si lastModified <= ifModifiedSince
        return $ifDt->timestamp >= $lastModified->timestamp;
    }

    /**
     * Crea una respuesta 304 Not Modified
     * 
     * @param Carbon $lastModified Fecha de última modificación
     * @param array $headers Headers adicionales
     * @return JsonResponse
     */
    public function getNotModifiedResponse(Carbon $lastModified, array $additionalHeaders = [])
    {
        $headers = array_merge([
            'Last-Modified' => $this->formatLastModified($lastModified),
            'Cache-Control' => 'private, must-revalidate',
        ], $additionalHeaders);

        return response()->json(null, 304, $headers);
    }

    /**
     * Añade los headers de cache a una respuesta
     * 
     * @param JsonResponse $response La respuesta a modificar
     * @param Carbon $lastModified Fecha de última modificación
     * @param array $additionalHeaders Headers adicionales
     * @return JsonResponse
     */
    public function addCacheHeaders($response, Carbon $lastModified, array $additionalHeaders = [])
    {
        $response->header('Last-Modified', $this->formatLastModified($lastModified));
        $response->header('Cache-Control', 'private, must-revalidate');

        foreach ($additionalHeaders as $key => $value) {
            $response->header($key, $value);
        }

        return $response;
    }

    /**
     * Invalida la caché de uno o varios recursos.
     *
     * @param mixed $resourceOrArray string|object|array  Resource o lista de resources / mappings
     * @param string|null $cacheKey Clave de caché (opcional) — usada solo si pasas un resource simple
     * @return void
     */
    public function invalidateCache($resourceOrArray, $cacheKey = null)
    {
        $items = $this->normalizeInvalidateItems($resourceOrArray, $cacheKey);

        foreach ($items as $item) {
            $resource = $item['resource'];
            $ck = $item['cacheKey'] ?? null;

            // Si te pasan una instancia, normalize devuelve la clase; updateTimestamp espera el resourceType.
            $this->updateTimestamp($resource, Carbon::now());

            if ($this->debug) {
                Log::debug('[CacheService] Invalidated resource: ' . self::TIMESTAMP_PREFIX . $this->normalizeResourceType($resource)
                    . ($ck ? ' — forgot cache key: ' . $ck : ''));
            }

            if ($ck) {
                Cache::forget($ck);
            }
        }
    }

    /**
     * Maneja la lógica completa de verificación de cache y última modificación
     * 
     * @param mixed $data Datos o colección para verificar la última modificación
     * @param callable $dataCallback Función que devuelve los datos procesados si hay modificación
     * @param array $additionalHeaders Headers adicionales
     * @return JsonResponse
     */
    public function handleCacheableResponse($data, callable $dataCallback, array $additionalHeaders = [])
    {
        $lastModified = $this->calculateLastModified($data);

        if ($this->debug) {
            Log::debug('[CacheService] If-Modified-Since header: ' . request()->header('If-Modified-Since'));
            Log::debug('[CacheService] Stored timestamp key: ' . self::TIMESTAMP_PREFIX . $this->normalizeResourceType(is_object($data) ? get_class($data) : (is_string($data) ? $data : 'unknown')));
            Log::debug('[CacheService] computed lastModified (rfc): ' . $lastModified->toRfc1123String() . ' ts:' . $lastModified->timestamp);
        }

        // Verificar si no ha sido modificado
        if ($this->isNotModified($lastModified)) {
            return $this->getNotModifiedResponse($lastModified, $additionalHeaders);
        }

        // Procesar y devolver los datos
        $processedData = $dataCallback($data);
        $response = response()->json($processedData);

        return $this->addCacheHeaders($response, $lastModified, $additionalHeaders);
    }

    /**
     * Notifica la eliminación para uno o varios recursos (misma semántica que invalidateCache).
     *
     * @param mixed $resourceOrArray
     * @param string|null $cacheKey
     * @return void
     */
    public function notifyDeletion($resourceOrArray, $cacheKey = null)
    {
        $items = $this->normalizeInvalidateItems($resourceOrArray, $cacheKey);

        foreach ($items as $item) {
            $resource = $item['resource'];
            $ck = $item['cacheKey'] ?? null;

            $this->updateTimestamp($resource, Carbon::now());
            if ($this->debug) {
                Log::debug('[CacheService] Notified deletion for resource: ' . self::TIMESTAMP_PREFIX . $this->normalizeResourceType($resource)
                    . ($ck ? ' — forgot cache key: ' . $ck : ''));
            }

            if ($ck) {
                Cache::forget($ck);
            }
        }
    }

    /**
     * Normaliza la entrada que admite invalidateCache/notifyDeletion y devuelve
     * una lista uniforme de items: [ ['resource' => ..., 'cacheKey' => ...], ... ]
     *
     * @param mixed $resourceOrArray
     * @param string|null $defaultCacheKey
     * @return array
     */
    protected function normalizeInvalidateItems($resourceOrArray, $defaultCacheKey = null): array
    {
        $out = [];

        // Helper para convertir resource posible (string|object) a string usable
        $toResourceString = function ($r) {
            if (is_object($r)) {
                return get_class($r);
            }
            return (string)$r;
        };

        // Single string or object -> wrap and return
        if (is_string($resourceOrArray) || is_object($resourceOrArray)) {
            $out[] = [
                'resource' => $toResourceString($resourceOrArray),
                'cacheKey' => $defaultCacheKey,
            ];
            return $out;
        }

        // If not array now, nothing to do
        if (!is_array($resourceOrArray)) {
            return $out;
        }

        // Detectar si es lista indexada (list) o array asociativo
        $isList = array_values($resourceOrArray) === $resourceOrArray;

        if ($isList) {
            // Cada elemento puede ser string|object|array
            foreach ($resourceOrArray as $elem) {
                if (is_string($elem) || is_object($elem)) {
                    $out[] = [
                        'resource' => $toResourceString($elem),
                        'cacheKey' => $defaultCacheKey,
                    ];
                } elseif (is_array($elem)) {
                    // Soportamos ['resource' => ..., 'cacheKey' => ...] o [resource, cacheKey]
                    if (isset($elem['resource'])) {
                        $out[] = [
                            'resource' => $toResourceString($elem['resource']),
                            'cacheKey' => $elem['cacheKey'] ?? $defaultCacheKey,
                        ];
                    } else {
                        // índice 0 => resource, índice 1 => cacheKey
                        $out[] = [
                            'resource' => $toResourceString($elem[0] ?? ''),
                            'cacheKey' => $elem[1] ?? $defaultCacheKey,
                        ];
                    }
                }
            }
        } else {
            // Array asociativo: puede ser ['resource' => 'X', 'cacheKey' => 'Y'] o mapa resource=>cacheKey
            if (isset($resourceOrArray['resource'])) {
                $out[] = [
                    'resource' => $toResourceString($resourceOrArray['resource']),
                    'cacheKey' => $resourceOrArray['cacheKey'] ?? $defaultCacheKey,
                ];
            } else {
                // mapa resource => cacheKey
                foreach ($resourceOrArray as $res => $ck) {
                    $out[] = [
                        'resource' => $toResourceString($res),
                        'cacheKey' => $ck,
                    ];
                }
            }
        }

        return $out;
    }
}
