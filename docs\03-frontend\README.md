# Frontend - Flutter

## Estructura del Proyecto

```
mobileapp/
├── lib/
│   ├── config/              # Configuración
│   │   ├── colors.dart      # Esquemas de colores
│   │   ├── routes.dart      # Rutas de navegación
│   │   ├── theme_schemes.dart
│   │   ├── map_schemes.dart
│   │   └── distance_options.dart
│   ├── models/              # Modelos de datos
│   │   ├── negocio.dart
│   │   ├── categoria.dart
│   │   ├── zona.dart
│   │   ├── evento.dart
│   │   ├── media.dart
│   │   └── suscripcion.dart
│   ├── pages/               # Páginas/Pantallas
│   │   ├── splash.dart
│   │   ├── home.dart
│   │   ├── categorias.dart
│   │   ├── negocios_list.dart
│   │   ├── negocio_detail.dart
│   │   ├── favoritos.dart
│   │   ├── mapa.dart
│   │   └── settings.dart
│   ├── providers/           # Gestión de estado
│   │   ├── theme_provider.dart
│   │   ├── settings_provider.dart
│   │   ├── favorites_provider.dart
│   │   ├── location_provider.dart
│   │   └── permission_provider.dart
│   ├── services/            # Servicios
│   │   ├── api_service.dart
│   │   ├── local_storage_service.dart
│   │   ├── location_service.dart
│   │   ├── permission_service.dart
│   │   ├── global_data_service.dart
│   │   └── debug_service.dart
│   ├── utils/               # Utilidades
│   │   ├── treeable.dart
│   │   └── ...
│   ├── widgets/             # Widgets reutilizables
│   │   ├── negocio_card.dart
│   │   ├── categoria_chip.dart
│   │   └── ...
│   ├── app.dart             # Configuración de la app
│   └── main.dart            # Punto de entrada
├── test/                    # Tests
│   ├── unit/
│   ├── integration/
│   ├── widget/
│   ├── helpers/
│   └── fixture/
├── assets/                  # Recursos estáticos
│   ├── images/
│   └── fonts/
└── pubspec.yaml            # Dependencias
```

## Arquitectura

### Patrón de Arquitectura

La aplicación sigue una arquitectura basada en **Provider** para la gestión de estado:

```
┌─────────────────────────────────────────────────────┐
│                    UI Layer                          │
│              (Pages & Widgets)                       │
│  - Muestra datos                                     │
│  - Captura eventos del usuario                       │
└────────────┬────────────────────────────────────────┘
             │
             │ context.watch<Provider>()
             │ context.read<Provider>()
             │
┌────────────▼────────────────────────────────────────┐
│              Provider Layer                          │
│         (ChangeNotifier Providers)                   │
│  - ThemeProvider                                     │
│  - SettingsProvider                                  │
│  - FavoritesProvider                                 │
│  - LocationProvider                                  │
│  - PermissionProvider                                │
└────────────┬────────────────────────────────────────┘
             │
             │ Llama a servicios
             │
┌────────────▼────────────────────────────────────────┐
│              Service Layer                           │
│  - ApiService (HTTP)                                 │
│  - LocalStorageService (SharedPreferences)           │
│  - LocationService (Geolocator)                      │
│  - PermissionService (permission_handler)            │
│  - GlobalDataService (Singleton de datos)            │
└────────────┬────────────────────────────────────────┘
             │
             │
┌────────────▼────────────────────────────────────────┐
│              Data Layer                              │
│  - Modelos (Negocio, Categoria, etc.)               │
│  - Serialización JSON                                │
└─────────────────────────────────────────────────────┘
```

## Providers

### ThemeProvider

**Archivo**: `lib/providers/theme_provider.dart`

Gestiona el tema de la aplicación (claro/oscuro).

```dart
class ThemeProvider with ChangeNotifier {
  AppTheme _currentTheme = AppTheme.claro;
  
  ThemeMode get themeMode => 
    _currentTheme == AppTheme.oscuro ? ThemeMode.dark : ThemeMode.light;
  
  AppTheme get currentTheme => _currentTheme;
  AppColorScheme get scheme => AppColors.current;
  
  Future<void> setTheme(AppTheme theme) async {
    _currentTheme = theme;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('theme', theme.name);
    AppColors.setScheme(theme);
    notifyListeners();
  }
}
```

**Uso**:
```dart
// Leer
final themeProv = context.watch<ThemeProvider>();
final isDark = themeProv.themeMode == ThemeMode.dark;

// Cambiar
context.read<ThemeProvider>().setTheme(AppTheme.oscuro);
```

### SettingsProvider

**Archivo**: `lib/providers/settings_provider.dart`

Gestiona configuraciones de la app (plataforma de mapas, distancia de filtro).

```dart
class SettingsProvider with ChangeNotifier {
  MapPlatform _mapPlatform = Platform.isIOS ? MapPlatform.apple : MapPlatform.google;
  DistanceOption _distanceOption = DistanceOption.twoPointFiveKm;
  
  MapPlatform get mapPlatform => _mapPlatform;
  double get distanceKm => _distanceOption.km;
  DistanceOption get distanceOption => _distanceOption;
  
  Future<void> setMapPlatform(MapPlatform p) async { ... }
  Future<void> setDistanceKm(double km) async { ... }
}
```

**Uso**:
```dart
final settings = context.watch<SettingsProvider>();
final distancia = settings.distanceKm; // 2.5
```

### FavoritesProvider

**Archivo**: `lib/providers/favorites_provider.dart`

Gestiona los negocios favoritos del usuario.

```dart
class FavoritesProvider with ChangeNotifier {
  Set<int> _favorites = {};
  
  Set<int> get favorites => _favorites;
  bool isFavorite(int id) => _favorites.contains(id);
  
  Future<void> toggleFavorite(int id) async {
    if (_favorites.contains(id)) {
      _favorites.remove(id);
    } else {
      _favorites.add(id);
    }
    notifyListeners();
    
    // Guardar en SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final stringList = _favorites.map((i) => i.toString()).toList();
    await prefs.setStringList('favoriteBusinesses', stringList);
  }
}
```

**Uso**:
```dart
final favProv = context.watch<FavoritesProvider>();
final isFav = favProv.isFavorite(negocio.id);

// Toggle
IconButton(
  icon: Icon(isFav ? Icons.favorite : Icons.favorite_border),
  onPressed: () => context.read<FavoritesProvider>().toggleFavorite(negocio.id),
)
```

### LocationProvider

**Archivo**: `lib/providers/location_provider.dart`

Gestiona la ubicación del usuario.

```dart
enum LocationStatus { initial, loading, success, error }

class LocationProvider extends ChangeNotifier {
  LocationStatus _status = LocationStatus.initial;
  LatLng? _userLocation;
  String? _errorMessage;
  
  LocationStatus get status => _status;
  LatLng? get userLocation => _userLocation;
  String? get errorMessage => _errorMessage;
  
  Future<void> fetchLocation() async { ... }
  void startTracking({int distanceFilter = 5}) { ... }
  void stopTracking() { ... }
}
```

**Uso**:
```dart
final locProv = context.watch<LocationProvider>();

if (locProv.status == LocationStatus.success) {
  final userPos = locProv.userLocation; // LatLng
}
```

### PermissionProvider

**Archivo**: `lib/providers/permission_provider.dart`

Gestiona permisos de ubicación.

```dart
enum LocationPermissionStatus {
  unknown, granted, denied, permanentlyDenied
}

class PermissionProvider extends ChangeNotifier {
  LocationPermissionStatus _status = LocationPermissionStatus.unknown;
  
  LocationPermissionStatus get status => _status;
  
  Future<void> checkPermission() async { ... }
  Future<void> requestPermission() async { ... }
}
```

## Servicios

### ApiService

**Archivo**: `lib/services/api_service.dart`

Gestiona todas las llamadas HTTP a la API.

```dart
class ApiService {
  static final String _baseUri = dotenv.env['API_URL'] ?? 'http://10.0.2.2/mia/api/v1/';
  final http.Client _client;
  
  // Método genérico para fetch con caché
  Future<List<T>> fetchData<T>({
    required String endpoint,
    required T Function(Map<String, dynamic>) fromJson,
    bool verbose = false,
    int? limitSample,
  }) async {
    // 1. Obtener Last-Modified del caché local
    final lastModified = await LocalStorageService().getLastModified(endpoint);
    
    // 2. Hacer request con If-Modified-Since
    final response = await _client.get(
      Uri.parse('$_baseUri$endpoint'),
      headers: lastModified != null ? {'If-Modified-Since': lastModified} : {},
    );
    
    // 3. Si 304 Not Modified, usar caché
    if (response.statusCode == 304) {
      return _getDataFromCache(endpoint, fromJson);
    }
    
    // 4. Si 200, actualizar caché y devolver datos
    if (response.statusCode == 200) {
      final jsonData = jsonDecode(response.body);
      await LocalStorageService().setData(endpoint, jsonData);
      await LocalStorageService().setLastModified(endpoint, response.headers['last-modified']!);
      
      return (jsonData as List).map((data) => fromJson(data)).toList();
    }
    
    throw Exception('Error al obtener datos: ${response.statusCode}');
  }
  
  // Métodos específicos
  Future<List<Negocio>> getNegocios() => fetchData(
    endpoint: 'negocios',
    fromJson: Negocio.fromJson,
  );
  
  Future<List<Categoria>> getCategorias() => fetchData(
    endpoint: 'categorias',
    fromJson: Categoria.fromJson,
  );
  
  // ... más métodos
}
```

### LocalStorageService

**Archivo**: `lib/services/local_storage_service.dart`

Gestiona el almacenamiento local con SharedPreferences.

```dart
class LocalStorageService {
  Future<void> setData(String key, dynamic data) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(key, jsonEncode(data));
  }
  
  Future<dynamic> getData(String key) async {
    final prefs = await SharedPreferences.getInstance();
    final data = prefs.getString(key);
    return data != null ? jsonDecode(data) : null;
  }
  
  Future<void> setLastModified(String key, String lastModified) async { ... }
  Future<String?> getLastModified(String key) async { ... }
}
```

### GlobalDataService

**Archivo**: `lib/services/global_data_service.dart`

Singleton que mantiene los datos globales de la app.

```dart
class GlobalDataService {
  static final GlobalDataService _instance = GlobalDataService._internal();
  factory GlobalDataService() => _instance;
  GlobalDataService._internal();
  
  List<Negocio>? negocios;
  List<Categoria>? categorias;
  List<Zona>? zonas;
  List<Evento>? eventos;
  
  void setNegocios(List<Negocio> data) {
    negocios = data;
  }
  
  // ... más métodos
}
```

## Modelos

### Negocio

**Archivo**: `lib/models/negocio.dart`

```dart
class Negocio {
  final int id;
  final String nombre;
  final String? descripcion;
  final String direccion;
  final String contacto;
  final Map<String, String>? contactosSecundarios;
  final List<Categoria>? categorias;
  final Zona? zona;
  final List<Evento>? eventos;
  final List<Media>? imagenes;
  final List<Media>? logos;
  final Map<String, String>? ubicacion;
  final Map<String, List<String>>? horario;
  final Map<String, String>? enlacesPropios;
  final Map<String, String>? enlacesSociales;
  final Map<String, Map<String, String>>? precios;
  final Map<String, dynamic> suscripcion;
  
  Negocio({...});
  
  factory Negocio.fromJson(Map<String, dynamic> json) {
    return Negocio(
      id: json['id'] as int,
      nombre: json['nombre'] as String,
      descripcion: json['descripcion'] as String?,
      // ... más campos
      categorias: json['categorias'] != null
        ? (json['categorias'] as List).map((c) => Categoria.fromJson(c)).toList()
        : null,
      // ... más relaciones
    );
  }
  
  Map<String, dynamic> toJson() => { ... };
}
```

Ver más modelos: [Modelos](./modelos.md)

## Navegación

### Rutas

**Archivo**: `lib/config/routes.dart`

```dart
final Map<String, WidgetBuilder> appRoutes = {
  '/': (context) => const SplashPage(),
  '/home': (context) => const HomePage(),
  '/categorias': (context) => const CategoriasPage(),
  '/negocios': (context) => const NegociosListPage(),
  '/negocio-detail': (context) => const NegocioDetailPage(),
  '/favoritos': (context) => const FavoritosPage(),
  '/mapa': (context) => const MapaPage(),
  '/settings': (context) => const SettingsPage(),
};
```

### Navegación con Argumentos

```dart
// Navegar pasando argumentos
Navigator.pushNamed(
  context,
  '/negocio-detail',
  arguments: {'negocio': negocio},
);

// Recibir argumentos
final args = ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>;
final negocio = args['negocio'] as Negocio;
```

## Widgets Principales

### NegocioCard

Widget reutilizable para mostrar un negocio en lista.

```dart
class NegocioCard extends StatelessWidget {
  final Negocio negocio;
  final VoidCallback? onTap;
  
  const NegocioCard({required this.negocio, this.onTap});
  
  @override
  Widget build(BuildContext context) {
    return Card(
      child: ListTile(
        leading: // Imagen
        title: Text(negocio.nombre),
        subtitle: Text(negocio.zona?.nombre ?? ''),
        trailing: FavoriteButton(negocioId: negocio.id),
        onTap: onTap,
      ),
    );
  }
}
```

## Flujo de Datos

### Carga Inicial

```
SplashPage
  ↓
ApiService.getNegocios()
  ↓
HTTP GET /api/v1/negocios
  ↓
If-Modified-Since header
  ↓
304 Not Modified → Usar caché local
200 OK → Actualizar caché
  ↓
Negocio.fromJson()
  ↓
GlobalDataService().setNegocios()
  ↓
Navigator.pushReplacementNamed('/home')
```

## Próximos Pasos

- [Modelos Detallados](./modelos.md)
- [Providers](./providers.md)
- [Servicios](./servicios.md)
- [Widgets](./widgets.md)

