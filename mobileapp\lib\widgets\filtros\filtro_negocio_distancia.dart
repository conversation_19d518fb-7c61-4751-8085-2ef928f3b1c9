// lib/widgets/filtros/filtro_negocio_distancia.dart

import 'package:flutter/material.dart';
import 'package:mia/config/distance_options.dart';

/// Widget de filtro por distancia para negocios.
/// Muestra un ListView horizontal de ChoiceChips con valores de distancia en km.
class FiltroNegocioDistancia extends StatelessWidget {
  final List<DistanceOption> opciones;
  final DistanceOption selectedOption;
  final ValueChanged<DistanceOption> onOptionSelected;

  const FiltroNegocioDistancia({
    super.key,
    required this.opciones,
    required this.selectedOption,
    required this.onOptionSelected,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: opciones.map((opt) {
          final bool seleccionado = opt == selectedOption;
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 6.0),
            child: ChoiceChip(
              label: Text(opt.label),
              selected: seleccionado,
              onSelected: (activo) {
                if (activo) onOptionSelected(opt);
              },
            ),
          );
        }).toList(),
      ),
    );
  }
}
