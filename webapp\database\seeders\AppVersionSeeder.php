<?php

namespace Database\Seeders;

use App\Models\AppVersion;
use Illuminate\Database\Seeder;

class AppVersionSeeder extends Seeder
{
    public function run()
    {
        AppVersion::firstOrCreate(
            ['platform' => 'ios'],
            ['latest_build' => '1'],
            ['minimum_build_allowed' => '1'],
            // ['update_url' => 'https://apps.apple.com/app/id6744295769']
        );

        AppVersion::firstOrCreate(
            ['platform' => 'android'],
            ['latest_build' => '1'],
            ['minimum_build_allowed' => '1'],
            // ['update_url' => 'https://play.google.com/store/apps/details?id=es.lbcdev.mia']
        );
    }
}
