<?php

namespace App\Http\Controllers\Api;

use App\Models\Evento;
use App\Models\Negocio;
use Illuminate\Http\Request;
use App\Services\CacheService;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
use App\Http\Requests\StoreEventoRequest;
use App\Http\Requests\UpdateEventoRequest;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class EventoController extends Controller
{
    use AuthorizesRequests;

    protected $cacheService;

    public function __construct(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    /**
     * Display a listing of the resource. Sólo eventos futuros.
     */
    public function index(Request $request)
    {
        $scope = $request->query('scope', 'futuros');

        // valores validos para el scope 'futuros' o 'all'
        if (!in_array($scope, ['futuros', 'all'])) {
            $scope = 'futuros';
        }

        $cacheKey = 'eventos_' . $scope;

        // Para eventos futuros, usar un tiempo de caché limitado
        if ($scope == 'futuros') {
            $eventos = Cache::remember($cacheKey, now()->addHours(6), function () {
                return Evento::futuros()->get();
            });
        } else {
            $eventos = Cache::rememberForever($cacheKey, function () {
                return Evento::all();
            });
        }

        return $this->cacheService->handleCacheableResponse(
            $eventos,
            function ($data) {
                return $data->map(function ($evento) {
                    $imagenes = $evento
                        ->media()
                        ->where('collection_name', 'imagenes_eventos')
                        ->orderBy('order_column', 'desc')
                        ->get()
                        ->map(fn($media) => $this->_mediaToArray($media));

                    return [
                        'id' => $evento->id,
                        'nombre' => $evento->nombre,
                        'descripcion' => $evento->descripcion,
                        'fecha_inicio' => $evento->fecha_inicio,
                        'fecha_fin' => $evento->fecha_fin,
                        'url' => $evento->url,
                        'negocio_id' => $evento->negocio_id,
                        'created_at' => $evento->created_at,
                        'updated_at' => $evento->updated_at,
                        'imagenes' => $imagenes,
                    ];
                });
            }
        );
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $evento = Evento::findOrFail($id);

            return $this->cacheService->handleCacheableResponse(
                $evento,
                function ($data) use ($evento) {

                    $imagenes = $evento
                        ->media()
                        ->where('collection_name', 'imagenes_eventos')
                        ->orderBy('order_column', 'desc')
                        ->get()
                        ->map(fn($media) => $this->_mediaToArray($media));

                    return [
                        'id' => $data->id,
                        'nombre' => $data->nombre,
                        'descripcion' => $data->descripcion,
                        'fecha_inicio' => $data->fecha_inicio,
                        'fecha_fin' => $data->fecha_fin,
                        'url' => $data->url,
                        'negocio_id' => $data->negocio_id,
                        'created_at' => $data->created_at,
                        'updated_at' => $data->updated_at,
                        'imagenes' => $imagenes,
                    ];
                }
            );
        } catch (\Exception $e) {
            return response()->json(['error' => 'Evento no encontrado'], 404);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreEventoRequest $request)
    {
        if (!$request->authorize()) {
            return response()->json(['error' => 'No autorizado'], 403);
        }

        $validated = $request->validated();
        $evento = Evento::create($validated);

        $this->cacheService->invalidateCache(Evento::class, 'eventos_all');
        $this->cacheService->invalidateCache(Evento::class, 'eventos_futuros');

        return response()->json($evento, 201);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateEventoRequest $request, string $id)
    {
        try {
            $evento = Evento::findOrFail($id);

            if (!$request->authorize()) {
                return response()->json(['error' => 'No autorizado'], 403);
            }

            $validated = $request->validated();
            $evento->update($validated);

            $this->cacheService->invalidateCache(Evento::class, 'eventos_all');
            $this->cacheService->invalidateCache(Evento::class, 'eventos_futuros');

            return response()->json($evento);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Evento no encontrado'], 404);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $evento = Evento::findOrFail($id);

            if (!$this->authorize('delete', $evento)) {
                return response()->json(['error' => 'No autorizado'], 403);
            }

            // Comprobar si es un evento futuro y en caso de que si, invalidar la caché de eventos futuros
            $fecha = $evento->fecha_fin;

            $evento->delete();

            if ($fecha >= now()) {
                $this->cacheService->invalidateCache(Evento::class, 'eventos_futuros');
            }

            $this->cacheService->invalidateCache(Evento::class, 'eventos_all');

            return response()->noContent();
        } catch (\Exception $e) {
            return response()->json(['error' => 'Evento no encontrado'], 404);
        }
    }

    protected function _mediaToArray($media): array
    {
        return [
            'id' => $media->id,
            'order' => $media->order_column,
            'url' => $media->getUrl(),
            'thumb' => $media->getUrl('thumb'),
            'name' => $media->name,
        ];
    }
}
