<?php

namespace App\Validators;

class BussinesHoursValidator
{
    public static function validate(array $horario): array
    {
        $errors = [];
        $daysOfWeek = ['lunes', 'martes', 'miércoles', 'jueves', 'viernes', 's<PERSON>bado', 'domingo'];

        // Ensure only the expected days are present
        $invalidDays = array_diff(array_keys($horario), $daysOfWeek);
        if (!empty($invalidDays)) {
            $errors[] = 'Días de horario no válidos: ' . implode(', ', $invalidDays);
        }

        // Time format validation regex
        $timeFormatRegex = '/^$|^(0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]-(0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$|^(0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]-(0[0-9]|1[0-9]|2[0-3]):[0-5][0-9],(0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]-(0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$/';

        // Validate each day's time format
        foreach ($horario as $day => $timeSlot) {
            // Skip empty values (closed day)
            if (trim($timeSlot) === '') {
                continue;
            }

            // Check if the time format matches the allowed patterns
            if (!preg_match($timeFormatRegex, $timeSlot)) {
                $errors[] = "El horario para $day no tiene un formato válido. Use HH:MM-HH:MM para horario continuo o HH:MM-HH:MM,HH:MM-HH:MM para horario discontinuo.";
            }

            // Additional validation to ensure start time is before end time
            $timePeriods = explode(',', $timeSlot);
            foreach ($timePeriods as $period) {
                $times = explode('-', $period);
                if (count($times) == 2) {
                    $startTime = strtotime($times[0]);
                    $endTime = strtotime($times[1]);

                    if ($startTime === false || $endTime === false) {
                        $errors[] = "Formato de hora inválido para $day: $period";
                    }

                    if ($startTime >= $endTime) {
                        $errors[] = "La hora de inicio debe ser anterior a la hora de fin para $day: $period";
                    }
                }
            }
        }

        return $errors;
    }

    // Método adicional para formatear horas
    public static function formatHours(array $horario): array
    {
        $formattedHours = $horario;

        // Aquí podrías agregar lógica adicional de formateo si es necesario
        // Por ejemplo, normalizar espacios, formato de horas, etc.

        return $formattedHours;
    }
}
