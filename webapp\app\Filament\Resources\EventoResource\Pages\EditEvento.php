<?php

namespace App\Filament\Resources\EventoResource\Pages;

use App\Models\User;
use Filament\Actions;
use App\Models\Evento;
use App\Models\Negocio;
use App\Services\CacheService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Validator;
use App\Filament\Resources\EventoResource;
use App\Http\Requests\UpdateEventoRequest;

class EditEvento extends EditRecord
{
    protected static string $resource = EventoResource::class;

    protected CacheService $cacheService;

    public function boot(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->action(function ($record) {
                    $record->delete();

                    Notification::make()
                        ->title('Evento eliminado')
                        ->success()
                        ->send();

                    $this->redirect($this->getResource()::getUrl('index'));
                }),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        /** @var User */
        $user = Auth::user();

        if (!$user->can('update', $record)) {
            Notification::make()
                ->title('No tienes permiso para editar este evento.')
                ->danger()
                ->send();
            abort(403, 'No tienes permiso para editar este evento');
        }

        $request = new UpdateEventoRequest();
        $validator = Validator::make($data, $request->rules(), $request->messages());

        try {
            $validatedData = $validator->validate();
            $record->update($validatedData);

            $fecha = $record->fecha_fin;

            if ($fecha >= now()) {
                $this->cacheService->invalidateCache(Evento::class, 'eventos_futuros');
            }

            $this->cacheService->invalidateCache(Evento::class, 'eventos_all');

            $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');

            return $record;
        } catch (\Illuminate\Validation\ValidationException $e) {
            Notification::make()
                ->title('Error al actualizar el evento.')
                // Mostrar en el body una lista de los errores de validación a partir de StoreEventoRequest::messages()
                ->body(function () use ($e) {
                    $errors = $e->validator->errors();
                    $errorMessages = [];
                    foreach ($errors->all() as $error) {
                        $errorMessages[] = $error;
                    }
                    return implode('<br>', $errorMessages);
                })
                ->danger()
                ->send();
            throw $e;
        }
    }
}
