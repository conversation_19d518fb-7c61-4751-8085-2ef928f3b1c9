<?php

namespace App\Enums;

enum MetodoPago: string
{
    case EFECTIVO = 'efectivo';
    case BIZUM = 'bizum';
    case TRANSFERENCIA = 'transferencia';

    public function message(): string
    {
        return match ($this) {
            self::BIZUM => 'Pago realizado mediante Bizum',
            self::TRANSFERENCIA => 'Pago realizado mediante transferencia bancaria',
            self::EFECTIVO => 'Pago realizado en efectivo',
        };
    }

    public function label(): string
    {
        return match ($this) {
            self::BIZUM => 'Bizum',
            self::TRANSFERENCIA => 'Transferencia',
            self::EFECTIVO => 'Efectivo',
        };
    }

    public function icon(): string
    {
        return match ($this) {
            self::BIZUM => 'heroicon-o-device-phone-mobile',
            self::TRANSFERENCIA => 'heroicon-o-building-library',
            self::EFECTIVO => 'heroicon-o-banknotes',
        };
    }

    public static function getAsOptions(): array
    {
        $options = [];
        foreach (self::cases() as $case) {
            $options[$case->value] = $case->label();
        }

        return $options;
    }
}
