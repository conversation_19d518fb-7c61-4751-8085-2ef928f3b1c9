// lib/pages/settings.dart

import 'package:flutter/material.dart';
import 'package:mia/config/colors.dart';
import 'package:mia/config/distance_options.dart';
import 'package:mia/config/map_schemes.dart';
import 'package:mia/config/styles.dart';
import 'package:mia/providers/settings_provider.dart';
import 'package:provider/provider.dart';
import 'package:mia/providers/theme_provider.dart';
import 'package:mia/config/theme_schemes.dart';
import 'package:mia/widgets/app_bottom_navigation_bar.dart';
import 'package:mia/widgets/app_scaffold.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  late AppTheme _selectedTheme;
  late MapPlatform _selectedMapPlatform;
  late DistanceOption _selectedDistanceOption;

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    final themeProv = context.read<ThemeProvider>();
    _selectedTheme = themeProv.currentTheme;

    final settingsProv = context.read<SettingsProvider>();
    _selectedMapPlatform = settingsProv.mapPlatform;
    _selectedDistanceOption = settingsProv.distanceOption;

    _isLoading = false;
  }

  Future<void> _saveThemePreference(AppTheme theme) async {
    final prov = context.read<ThemeProvider>();
    await prov.setTheme(theme);
    setState(() => _selectedTheme = theme);
  }

  Future<void> _saveMapPreference(MapPlatform platform) async {
    final prov = context.read<SettingsProvider>();
    await prov.setMapPlatform(platform);
    setState(() => _selectedMapPlatform = platform);
  }

  Future<void> _saveDistancePreference(double km) async {
    final prov = context.read<SettingsProvider>();
    await prov.setDistanceKm(km);
    setState(() => _selectedDistanceOption =
        DistanceOption.values.firstWhere((o) => o.km == km));
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      bottomNavigationBar: AppBottomNavigationBar(currentIndex: 4),
      child: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Container(
              color: Colors.transparent,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text(
                      'Configuración',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: AppColors.current.secondaryColor,
                      ),
                    ),
                  ),
                  Expanded(
                    child: ListView(
                      padding: EdgeInsets.zero,
                      children: [
                        _buildSectionHeader('Apariencia'),
                        Container(
                          color: AppColors.current.surfaceColor,
                          child: _buildSettingItem(
                            icon: Icons.brightness_6,
                            title: 'Tema',
                            subtitle: _selectedTheme.name.capitalize(),
                            onTap: _showThemeSelectionDialog,
                          ),
                        ),
                        const SizedBox(height: 1.0),
                        _buildSectionHeader('Preferencias'),
                        Container(
                          color: AppColors.current.surfaceColor,
                          child: _buildSettingItem(
                            icon: Icons.location_on,
                            title: 'Mapas (Cómo llegar)',
                            subtitle: _selectedMapPlatform.name.capitalize(),
                            onTap: _showMapSelectionDialog,
                          ),
                        ),
                        const SizedBox(height: 1.0),
                        Container(
                          color: AppColors.current.surfaceColor,
                          child: _buildSettingItem(
                            icon: Icons.my_location,
                            title: 'Distancia inicial (Cerca de mí)',
                            subtitle: _selectedDistanceOption.label,
                            onTap: _showDistanceSelectionDialog,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Container(
      color: AppColors.current.surfaceColor,
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w500,
          color: AppColors.current.textColor,
        ),
      ),
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  // String _distanceLabel(double km) {
  //   if (km == 0.0) return 'Sin filtro';
  //   if (km == km.roundToDouble()) return '${km.toInt()} km';
  //   return '${km.toStringAsFixed(1).replaceAll('.', ',')} km';
  // }

  void _showThemeSelectionDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          scrollable: true,
          title: const Text('Selecciona un tema'),
          content: ListBody(
            children: AppTheme.values.map((theme) {
              return RadioListTile<AppTheme>(
                title: Text(theme.name.capitalize()),
                value: theme,
                groupValue: _selectedTheme,
                onChanged: (AppTheme? value) {
                  if (value != null) {
                    _saveThemePreference(value);
                    Navigator.of(context).pop();
                  }
                },
              );
            }).toList(),
          ),
          actions: [
            TextButton(
              child: const Text('Cancelar'),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        );
      },
    );
  }

  void _showMapSelectionDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          scrollable: true,
          title: const Text('Selecciona plataforma de mapas'),
          content: ListBody(
            children: MapPlatform.values.map((mp) {
              return RadioListTile<MapPlatform>(
                title: Text(
                  mp.name.capitalize(),
                  style:
                      AppStyles.getStyle(context, 'base', fontWeight: 'medium'),
                ),
                value: mp,
                groupValue: _selectedMapPlatform,
                onChanged: (MapPlatform? value) {
                  if (value != null) {
                    _saveMapPreference(value);
                    Navigator.of(context).pop();
                  }
                },
              );
            }).toList(),
          ),
          actions: [
            TextButton(
              child: const Text('Cancelar'),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        );
      },
    );
  }

  void _showDistanceSelectionDialog() {
    final opciones = DistanceOption.values;
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Selecciona distancia'),
          // envolvemos en SingleChildScrollView para poder desplazar si excede altura
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize:
                  MainAxisSize.min, // ← que sólo ocupe lo que necesita
              children: opciones.map((opt) {
                return RadioListTile<DistanceOption>(
                  title: Text(opt.label),
                  value: opt,
                  groupValue: _selectedDistanceOption,
                  onChanged: (sel) {
                    if (sel != null) {
                      _saveDistancePreference(sel.km);
                      Navigator.of(context).pop();
                    }
                  },
                );
              }).toList(),
            ),
          ),
          actions: [
            TextButton(
              child: const Text('Cancelar'),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        );
      },
    );
  }
}

// Extension para capitalizar nombres de tema
extension StringExtension on String {
  String capitalize() => this[0].toUpperCase() + substring(1);
}
