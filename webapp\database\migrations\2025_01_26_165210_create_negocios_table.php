<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('negocios', function (Blueprint $table) {
            $table->id();
            $table->string('nombre', 100);
            $table->text('descripcion')->nullable();
            $table->text('direccion', 255);
            $table->json('ubicacion')->nullable()->comment('Coordenadas en formato JSON (latitud y longitud)');
            $table->json('horario')->nullable()->comment('Horario en formato JSON');
            $table->json('enlaces_sociales')->nullable()->comment('Lista de enlaces en formato JSON');
            $table->json('enlaces_propios')->nullable()->comment('Lista de enlaces en formato JSON');
            $table->json('precios')->nullable()->comment('Lista de precios en formato JSON');
            $table->string('contacto');
            $table->foreignId('zona_id')->nullable()->constrained('zonas')->onDelete('set null');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('negocios');
    }
};
