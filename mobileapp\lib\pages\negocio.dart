import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map_animations/flutter_map_animations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mia/config/colors.dart';
import 'package:mia/config/styles.dart';
import 'package:mia/models/categoria.dart';
import 'package:mia/models/evento.dart';
import 'package:mia/models/negocio.dart';
import 'package:mia/services/core_service.dart';
import 'package:mia/widgets/app_bottom_navigation_bar.dart';
import 'package:mia/widgets/app_scaffold.dart';
import 'package:mia/widgets/botones/fav_button.dart';
import 'package:mia/widgets/imagenes/carousel.dart';
import 'package:mia/widgets/lista_con_separador.dart';
// import 'package:mia/widgets/custom_button.dart';
import 'package:mia/widgets/mapas/locations_map.dart';
import 'package:mia/widgets/modales/evento_modal.dart';

class NegocioPage extends StatefulWidget {
  final Negocio negocio;

  const NegocioPage({super.key, required this.negocio});

  @override
  State<NegocioPage> createState() => _NegocioPageState();
}

class _NegocioPageState extends State<NegocioPage>
    with TickerProviderStateMixin {
  // Controlador para el mapa
  late final _animatedMapController = AnimatedMapController(vsync: this);

  @override
  void dispose() {
    _animatedMapController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    bool isOpen = widget.negocio.isOpenNow();
    String horarioHoy = widget.negocio.getTramoHorario() ?? '';

    return AppScaffold(
      bottomNavigationBar: const AppBottomNavigationBar(currentIndex: 0),
      floatingActionButton: FloatingActionButton(
        shape:
            ShapeBorder.lerp(const CircleBorder(), const CircleBorder(), 0.5),
        onPressed: () {
          Navigator.pop(context);
        },
        child: Icon(Icons.arrow_back),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Carousel(imagenes: widget.negocio.imagenes ?? []),
            _buildHeaderInfo(isOpen, horarioHoy, context),
            _buildSeccionInfo(widget.negocio),
            const SizedBox(height: 16),
            _buildSeccionMapa(
                negocio: widget.negocio, mapController: _animatedMapController),
            const SizedBox(height: 16),
            if (widget.negocio.eventos != null)
              widget.negocio.eventos!.isNotEmpty
                  ? _buildSeccionEventos(widget.negocio.eventos ?? [])
                  : Container(),
            if (widget.negocio.eventos != null)
              widget.negocio.eventos!.isNotEmpty
                  ? const SizedBox(height: 16)
                  : Container(),
            // Text(
            //   'Lista de precios',
            //   style: AppStyles.getStyle('h6',
            //       fontWeight: 'bold', color: AppColors.current.secondaryColor),
            // ),
            // _buildPreciosList(widget.negocio.precios),
            // const SizedBox(height: 16),
            if (widget.negocio.enlacesSociales != null) _buildSeccionRedes(),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  // Sección de cabecera

  Widget _buildHeaderInfo(
      bool isOpen, String horarioHoy, BuildContext context) {
    return Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Row(
            children: [
              Expanded(
                child: Text(widget.negocio.nombre,
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    style: AppStyles.getStyle(context, 'h5',
                        fontWeight: 'bold',
                        color: AppColors.current.secondaryColor)),
              ),
              FavButton(
                negocio: widget.negocio,
                size: 32,
              ),
            ],
          ),
          const SizedBox(height: 8),
          // Mostrar nombres de categorias separados por comas
          _buildListaCategorias(widget.negocio.categorias ?? []),
          const SizedBox(height: 8),
          Text(widget.negocio.descripcion ?? '',
              style: AppStyles.getStyle(context, 'base')),
          // _buildHeaderHorario(isOpen, horarioHoy, context),
        ]));
  }

  Widget _buildListaCategorias(List<Categoria> categorias) {
    if (categorias.isEmpty) {
      return Text(
        'Categorías no disponibles',
        style: AppStyles.getStyle(context, 'base'),
      );
    }

    return ListaConSeparador(
        layoutType: TipoLayout.wrap,
        direction: Axis.horizontal,
        spacing: 8.0,
        runSpacing: 4.0,
        runAlignment: WrapAlignment.center,
        wrapCrossAlignment: WrapCrossAlignment.center,
        separator: Icon(Icons.circle, size: 5),
        children: categorias.map((categoria) {
          return Text(
            categoria.nombre,
            style: AppStyles.getStyle(context, 'base', fontWeight: 'bold'),
          );
        }).toList());
  }

  Widget _buildHeaderHorario(
      bool isOpen, String horarioHoy, BuildContext context,
      {bool mostarLink = true}) {
    // Si el horario de hoy no está vacío, obtenemos el día actual y lo agregamos
    String horarioConDia = horarioHoy;
    if (horarioHoy.isNotEmpty) {
      final List<String> weekdays = [
        'lunes',
        'martes',
        'miércoles',
        'jueves',
        'viernes',
        'sábado',
        'domingo'
      ];
      final now = DateTime.now();
      final String currentDay = weekdays[now.weekday - 1];
      // Anteponer el día actual (capitalizado) seguido de " - " y los tramos horarios
      horarioConDia = '${_capitalize(currentDay)} - $horarioHoy';
    }

    // Obtener la hora actual formateada solo si estamos en debug
    String debugTime = '';
    if (kDebugMode) {
      final now = DateTime.now();
      // Formatear la hora actual en HH:mm:ss
      debugTime =
          ' (${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')})';
    }

    return Wrap(
      crossAxisAlignment: WrapCrossAlignment.center,
      alignment: WrapAlignment.start,
      spacing: 8,
      runSpacing: 4,
      children: [
        isOpen
            ? Text(
                'Abierto',
                style: AppStyles.getStyle(context, 'base',
                    fontWeight: 'bold', color: AppColors.current.successColor),
              )
            : Text(
                'Cerrado',
                style: AppStyles.getStyle(context, 'base',
                    fontWeight: 'bold', color: AppColors.current.errorColor),
              ),
        // Mostrar el tiempo actual entre paréntesis en modo debug
        if (kDebugMode)
          Text(
            debugTime,
            style: AppStyles.getStyle(context, 'base',
                color: AppColors.current.infoColor),
          ),
        Text(
          horarioConDia,
          style: AppStyles.getStyle(context, 'base'),
        ),
        if (mostarLink)
          TextButton(
            onPressed: () {
              _mostrarHorariosModal(context);
            },
            style: TextButton.styleFrom(
              padding: EdgeInsets.zero,
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Text(
              'Ver horarios',
              style: AppStyles.getStyle(context, 'base',
                  color: AppColors.current.infoColor),
            ),
          ),
      ],
    );
  }

  // Sección de información

  Widget _buildSeccionInfo(Negocio negocio) {
    return Container(
      decoration: BoxDecoration(color: AppColors.current.surfaceColor),
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Text(
              'Información',
              style: AppStyles.getStyle(context, 'h5',
                  fontWeight: 'bold', color: AppColors.current.textColor),
            ),
          ),
          _buildInfoList(negocio),
        ],
      ),
    );
  }

  Widget _buildInfoList(Negocio negocio) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListView(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              ListTile(
                title: Text('Horario',
                    style:
                        AppStyles.getStyle(context, 'h6', fontWeight: 'bold')),
                subtitle: _buildHeaderHorario(negocio.isOpenNow(),
                    negocio.getAllHorariosHoy() ?? '', context,
                    mostarLink: false),
                trailing: Icon(Icons.arrow_forward, size: 32),
                onTap: () {
                  _mostrarHorariosModal(context);
                },
              ),
              Divider(height: 1, color: AppColors.current.accentColor),
              _buildListTileLLamadas(negocio),
              if (negocio.enlacesPropios != null)
                for (var enlace in negocio.enlacesPropios!.entries)
                  Column(
                    children: [
                      Divider(height: 1, color: AppColors.current.accentColor),
                      ListTile(
                        title: Text(enlace.key,
                            style: AppStyles.getStyle(context, 'h6',
                                fontWeight: 'bold')),
                        subtitle: Text(enlace.value),
                        trailing: Icon(Icons.open_in_new, size: 32),
                        onTap: () async {
                          await CoreService.launchWeb(enlace.value);
                        },
                      ),
                    ],
                  )
            ],
          ),
        ],
      ),
    );
  }

  ListTile _buildListTileLLamadas(Negocio negocio) {
    // Determinar si hay contactos secundarios. En caso negativo mostar solo el principal, en caso contrario mostrar un modal con una lista de tiles con el contacto principal primero y los secundarios después que al pulsar se llame al contacto

    if (negocio.contactosSecundarios != null &&
        negocio.contactosSecundarios!.isNotEmpty) {
      return ListTile(
        title: Text('Llamar',
            style: AppStyles.getStyle(context, 'h6', fontWeight: 'bold')),
        subtitle: Text(
            '${negocio.contacto} y ${negocio.contactosSecundarios!.length} más'),
        trailing: Icon(Icons.phone, size: 32),
        onTap: () async {
          _mostrarContactosModal(context, negocio);
        },
      );
    } else {
      return ListTile(
        title: Text('Llamar',
            style: AppStyles.getStyle(context, 'h6', fontWeight: 'bold')),
        subtitle: Text(negocio.contacto),
        trailing: Icon(Icons.phone, size: 32),
        onTap: () async {
          // check contacto valido
          if (negocio.contacto.isEmpty) {
            CoreService.muestraError('Contacto no disponible');
            return;
          }
          await CoreService.launchPhoneCall(negocio.contacto);
        },
      );
    }
  }

  // Sección de mapa
  Widget _buildSeccionMapa(
      {required Negocio negocio,
      required AnimatedMapController mapController}) {
    return Container(
      decoration: BoxDecoration(color: AppColors.current.surfaceColor),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Stack(
            children: [
              LocationsMap(
                negocios: [negocio],
                height: 250,
                mapController: mapController,
                showActions: false,
                unboundedMap: false,
              ),
              // This transparent layer catches all taps on the map
              Positioned.fill(
                child: GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    Navigator.pushNamed(
                      context,
                      '/negocio-mapa',
                      arguments: negocio,
                    );
                  },
                  // This transparent layer will catch all touch events
                  child: Container(
                    color: Colors.transparent,
                  ),
                ),
              ),
            ],
          ),
          ListView(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              ListTile(
                title: negocio.direccion.isEmpty
                    ? Text('Dirección no disponible',
                        style: AppStyles.getStyle(context, 'h6',
                            fontWeight: 'medium'))
                    : GestureDetector(
                        onTap: () {
                          Navigator.pushNamed(
                            context,
                            '/negocio-mapa',
                            arguments: negocio,
                          );
                        },
                        child: Text(negocio.direccion,
                            style: AppStyles.getStyle(context, 'h6',
                                fontWeight: 'medium')),
                      ),
              ),
              Divider(height: 1, color: AppColors.current.accentColor),
              ListTile(
                title: Text('Cómo llegar',
                    style:
                        AppStyles.getStyle(context, 'h6', fontWeight: 'bold')),
                leading: Icon(Icons.directions_outlined, size: 32),
                onTap: () {
                  // Conectar con app de mapas para obtener indicaciones mandando la ubicacion
                  CoreService.launchMap(negocio);
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Sección de eventos

  Widget _buildSeccionEventos(List<Evento> eventos) {
    return Container(
      decoration: BoxDecoration(color: AppColors.current.surfaceColor),
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Text(
              'Eventos',
              style: AppStyles.getStyle(context, 'h5',
                  fontWeight: 'bold', color: AppColors.current.textColor),
            ),
          ),
          _buildEventList(eventos),
        ],
      ),
    );
  }

  Widget _buildEventList(List<Evento> eventos) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListView(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              for (var evento in eventos)
                Column(
                  children: [
                    ListTile(
                      leading: SizedBox(
                          width: 50,
                          height: 50,
                          child:
                              _getPrimeraImagenEvento(evento) ?? Container()),
                      title: Text(evento.nombre,
                          style: AppStyles.getStyle(context, 'h6',
                              fontWeight: 'bold')),
                      subtitle: AutoSizeText(
                        evento.getHorarioString(),
                        minFontSize: 8,
                        maxLines: 2,
                        style: AppStyles.getStyle(context, 'base',
                            fontWeight: 'medium'),
                        overflow: TextOverflow.ellipsis,
                      ),
                      // subtitle: ListaConSeparador(
                      //     layoutType: TipoLayout.wrap,
                      //     direction: Axis.horizontal,
                      //     wrapCrossAlignment: WrapCrossAlignment.center,
                      //     separator: Padding(
                      //       padding:
                      //           const EdgeInsets.symmetric(horizontal: 8.0),
                      //       child: Icon(Icons.circle, size: 5),
                      //     ),
                      //     children: _buildInfoFechasEvento(evento)),
                      trailing: Icon(Icons.arrow_forward, size: 32),
                      onTap: () {
                        mostrarEventoModal(context, evento, false);
                      },
                    ),
                    Divider(height: 1, color: AppColors.current.accentColor),
                  ],
                )
            ],
          ),
        ],
      ),
    );
  }

  Widget? _getPrimeraImagenEvento(Evento evento) {
    if (evento.imagenes != null && evento.imagenes!.isNotEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(10.0),
        child: Image.network(
          evento.imagenes![0].url,
          width: 50,
          height: 50,
          fit: BoxFit.cover,
          errorBuilder:
              (BuildContext context, Object error, StackTrace? stackTrace) {
            return SvgPicture.asset(
              'assets/la_piedra.svg',
              width: 50,
              height: 50,
            );
          },
        ),
      );
    } else {
      return null;
    }
  }

  // List<Widget> _buildInfoFechasEvento(Evento evento) {
  //   // si la fechaFin es menos de 24 horas de diferencia, mostrar solo la fechaInicio
  //   DateTime parsedDateInicio = DateTime.parse(evento.fechaInicio);
  //   DateTime parsedDateFin = DateTime.parse(evento.fechaFin);

  //   if (parsedDateFin.difference(parsedDateInicio).inHours < 24) {
  //     return [
  //       Text(CoreService.datetimeToLocale(evento.fechaInicio,
  //           format: 'E d MMM HH:mm')),
  //     ];
  //   }
  //   return [
  //     Text(CoreService.datetimeToLocale(evento.fechaInicio,
  //         format: 'E d MMM HH:mm')),
  //     Text(CoreService.datetimeToLocale(evento.fechaFin,
  //         format: 'E d MMM HH:mm')),
  //   ];
  // }

  // Sección de enlaces

/*
  Widget _buildSeccionRedesAlt_1() {
    return Container(
      decoration: BoxDecoration(color: Colors.white),
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Redes sociales',
            style: AppStyles.getStyle('h5',
                fontWeight: 'bold', color: AppColors.current.textColor),
          ),
          ListView(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              for (var enlace in widget.negocio.enlacesSociales!.entries)
                Column(
                  children: [
                    ListTile(
                      title: Text(enlace.key,
                          style: AppStyles.getStyle('h6', fontWeight: 'bold')),
                      subtitle: Text(enlace.value),
                      trailing: Icon(Icons.open_in_new, size: 32),
                      onTap: () {
                        // Acción al presionar el tercer elemento
                      },
                    ),
                    Divider(height: 1, color: AppColors.current.primaryColor),
                  ],
                )
            ],
          ),
        ],
      ),
    );
  }
*/

  Widget _buildSeccionRedes() {
    return Container(
        decoration: BoxDecoration(color: AppColors.current.surfaceColor),
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Text('Redes sociales',
            //     style: AppStyles.getStyle('h5',
            //         fontWeight: 'bold', color: AppColors.current.textColor)),
            _buildEnlacesSocialesList(widget.negocio.enlacesSociales,
                columna: false),
          ],
        ));
  }

  Widget _buildEnlacesSocialesList(Map<String, String>? enlacesSociales,
      {bool columna = true}) {
    if (enlacesSociales == null || enlacesSociales.isEmpty) {
      // return Text(
      //   'Enlaces sociales no disponibles',
      //   style: AppStyles.getStyle('base'),
      // );
      return Container();
    }

    if (columna) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: getItemsRedes(enlacesSociales),
      );
    } else {
      return Wrap(
        alignment: WrapAlignment.center,
        children: getItemsRedes(enlacesSociales),
      );
    }
  }

  List<Container> getItemsRedes(Map<String, String> enlacesSociales) {
    return enlacesSociales.entries.map((entry) {
      return Container(
        decoration: BoxDecoration(
          border: Border(
            bottom:
                BorderSide(color: AppColors.current.textColor.withOpacity(0.1)),
          ),
        ),
        //hacer que el texto sea un enlace
        child: TextButton(
          onPressed: () {
            CoreService.launchWeb(entry.value);
          },
          child: Text(
            // capitalizar entry.key
            _capitalize(entry.key),
            style: AppStyles.getStyle(context, 'base'),
          ),
        ),
      );
    }).toList();
  }

  // Modales

  void _mostrarHorariosModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(20),
          // Set max height to 70% of screen height
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Horarios',
                    style: AppStyles.getStyle(context, 'h6',
                        fontWeight: 'bold', color: AppColors.current.textColor),
                  ),
                  IconButton(
                    icon: Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: SingleChildScrollView(
                  child: _buildHorarioListEnModal(widget.negocio.horario),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHorarioListEnModal(Map<String, List<String>>? horario) {
    if (horario == null || horario.isEmpty) {
      return Text(
        'Horario no disponible',
        style: AppStyles.getStyle(context, 'base'),
      );
    }

    // La lista de días empieza en lunes y termina en domingo
    final List<String> weekdays = [
      'lunes',
      'martes',
      'miércoles',
      'jueves',
      'viernes',
      'sábado',
      'domingo'
    ];

    // DateTime.weekday devuelve 1 para lunes y 7 para domingo.
    // Se utiliza ese valor para indexar correctamente la lista 'weekdays'.
    final now = DateTime.now();
    final String today = weekdays[now.weekday - 1];

    return Column(
      children: weekdays.map((dia) {
        final bool isToday = dia == today;
        // Verificar si el día existe en el horario y si tiene horarios válidos
        final bool diaExiste = horario.containsKey(dia);
        final List<String>? tramosHorarios = diaExiste ? horario[dia] : null;
        final bool estaCerrado =
            tramosHorarios == null || tramosHorarios.isEmpty;

        // Si está cerrado o no hay información, mostrar 'Cerrado'
        final String horariosTexto =
            estaCerrado ? 'Cerrado' : tramosHorarios.join(', ');

        return Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          decoration: BoxDecoration(
            color: isToday
                ? AppColors.current.secondaryColor.withOpacity(0.1)
                : null,
            border: Border(
              bottom: BorderSide(
                  color: AppColors.current.textColor.withOpacity(0.1)),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _capitalize(dia),
                style: AppStyles.getStyle(
                  context,
                  'base',
                  fontWeight: isToday ? 'extra' : 'medium',
                ),
              ),
              Expanded(
                child: Text(
                  horariosTexto,
                  style: AppStyles.getStyle(
                    context,
                    'base',
                    fontWeight: isToday ? 'extra' : null,
                  ),
                  textAlign: TextAlign.right,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  void _mostrarContactosModal(BuildContext context, Negocio negocio) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(20),
          // Set max height to 70% of screen height
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Contactos',
                    style: AppStyles.getStyle(context, 'h6',
                        fontWeight: 'bold', color: AppColors.current.textColor),
                  ),
                  IconButton(
                    icon: Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: SingleChildScrollView(
                  child: _buildContactosList(negocio),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildContactosList(Negocio negocio) {
    final List<MapEntry<String, String>> allContacts = [];

    // Agregar contacto principal si existe y no está vacío
    if (negocio.contacto.isNotEmpty) {
      allContacts.add(MapEntry("Teléfono principal", negocio.contacto));
    }

    // Separar contactos secundarios en dos listas: con título y sin título
    final List<MapEntry<String, String>> contactsWithTitle = [];
    final List<MapEntry<String, String>> contactsWithoutTitle = [];

    if (negocio.contactosSecundarios != null &&
        negocio.contactosSecundarios!.isNotEmpty) {
      negocio.contactosSecundarios!.forEach((key, phone) {
        // Si la clave es "Teléfono", significa que no tenía título
        if (key == "Teléfono") {
          contactsWithoutTitle.add(MapEntry(key, phone));
        } else {
          contactsWithTitle.add(MapEntry(key, phone));
        }
      });

      // Agregar primero los contactos con título
      allContacts.addAll(contactsWithTitle);

      // Luego agregar los contactos sin título
      allContacts.addAll(contactsWithoutTitle);
    }

    // Si no hay ningún contacto, mostrar mensaje
    if (allContacts.isEmpty) {
      return Text(
        'Contactos no disponibles',
        style: AppStyles.getStyle(context, 'base'),
      );
    }

    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: allContacts.length,
      separatorBuilder: (context, index) => Divider(
        height: 1,
        color: AppColors.current.accentColor,
      ),
      itemBuilder: (context, index) {
        final contactLabel = allContacts[index].key;
        final phone = allContacts[index].value;

        return ListTile(
          title: Text(
            contactLabel,
            style: AppStyles.getStyle(context, 'h6', fontWeight: 'bold'),
          ),
          subtitle: Text(phone),
          trailing: const Icon(Icons.phone, size: 32),
          onTap: () async {
            if (phone.isEmpty) {
              CoreService.muestraError('Contacto no disponible');
              return;
            }
            await CoreService.launchPhoneCall(phone);
          },
        );
      },
    );
  }
// Otras funciones y widgets aquí...

  String _capitalize(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1);
  }

/*
  Widget _buildPreciosList(Map<String, Map<String, String>>? precios) {
    if (precios == null || precios.isEmpty) {
      return Text(
        'Precios no disponibles',
        style: AppStyles.getStyle('base'),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: precios.entries.map((entry) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 8),
            Text(
              entry.key,
              style: AppStyles.getStyle('base',
                  fontWeight: 'bold', color: AppColors.current.textColor),
            ),
            SizedBox(height: 4),
            _buildPreciosItem(entry.value),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildPreciosItem(Map<String, String> preciosItem) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: preciosItem.entries.map((entry) {
        return Container(
          padding: EdgeInsetsDirectional.symmetric(vertical: 2, horizontal: 0),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                  color: AppColors.current.textColor.withOpacity(0.1)),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                entry.key,
                style: AppStyles.getStyle('base'),
              ),
              Text(
                '${entry.value} €',
                style: AppStyles.getStyle('base'),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
*/
}
