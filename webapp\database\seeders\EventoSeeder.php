<?php

namespace Database\Seeders;

use App\Models\Evento;
use App\Models\Negocio;
use Illuminate\Database\Seeder;
use App\Services\DataGeneratorService;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class EventoSeeder extends Seeder
{
    use WithFaker;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $negocios = Negocio::all();

        foreach ($negocios as $negocio) {
            $nombres = DataGeneratorService::getNombreEvento($negocio->categorias->first()->nombre);

            // Crear de 0 a 3 eventos por negocio
            $negocio->eventos()->saveMany(
                Evento::factory(rand(0, 3))->make([
                    'negocio_id' => $negocio->id,
                    'nombre' => $this->faker->unique()->randomElement($nombres),
                ])
            );
        }
    }
}
