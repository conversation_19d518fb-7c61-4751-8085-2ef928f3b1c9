# .\scripts\build-flutter.ps1 -Environment staging -Platform android -Release

param(
    [Parameter(Mandatory = $true)]
    [ValidateSet("local", "staging", "production")]
    [string]$Environment,

    [Parameter(Mandatory = $true)]
    # [ValidateSet("android", "ios", "windows", "macos", "linux", "web", "all")]
    [ValidateSet("android", "windows", "linux", "web", "all")]
    [string]$Platform,

    [switch]$Release
)

# Configuración de directorios
$rootDir = Split-Path -Parent $PSScriptRoot
$envSourceFile = Join-Path $rootDir ".env.$Environment"
$envTargetFile = Join-Path $rootDir ".env"

# Mensaje informativo
Write-Host "Generando build para entorno: $Environment en plataforma: $Platform" -ForegroundColor Cyan

# Copiar archivo de entorno
if (Test-Path $envSourceFile) {
    Copy-Item $envSourceFile $envTargetFile -Force
    Write-Host "Archivo de entorno copiado: $envSourceFile -> $envTargetFile" -ForegroundColor Green
}
else {
    Write-Host "Error: No se encontró el archivo de entorno $envSourceFile" -ForegroundColor Red
    exit 1
}

# Determinar modo de compilación
$buildMode = if ($Release) { "--release" } else { "" }

# Ejecutar comando de build según la plataforma
Push-Location $rootDir
try {
    switch ($Platform) {
        "android" {
            $command = "flutter build apk $buildMode --dart-define=FLAVOR=$Environment -t lib/main.dart"
            if ($Environment -eq "production" -and $Release) {
                Write-Host "Generando APK para producción..." -ForegroundColor Yellow
                Invoke-Expression $command
                
                Write-Host "Generando App Bundle para producción..." -ForegroundColor Yellow
                Invoke-Expression "flutter build appbundle $buildMode --dart-define=FLAVOR=$Environment -t lib/main.dart"
            }
            else {
                Write-Host "Generando APK para $Environment..." -ForegroundColor Yellow
                Invoke-Expression $command
            }
        }
        # "ios" {
        #     $iosFlags = if ($Release) { "--release --no-codesign" } else { "" }
        #     Write-Host "Generando build para iOS..." -ForegroundColor Yellow
        #     Invoke-Expression "flutter build ios $iosFlags --dart-define=FLAVOR=$Environment"
        # }
        "windows" {
            Write-Host "Generando build para Windows..." -ForegroundColor Yellow
            Invoke-Expression "flutter build windows $buildMode --dart-define=FLAVOR=$Environment"
        }
        # "macos" {
        #     Write-Host "Generando build para macOS..." -ForegroundColor Yellow
        #     Invoke-Expression "flutter build macos $buildMode --dart-define=FLAVOR=$Environment"
        # }
        "linux" {
            Write-Host "Generando build para Linux..." -ForegroundColor Yellow
            Invoke-Expression "flutter build linux $buildMode --dart-define=FLAVOR=$Environment"
        }
        "web" {
            Write-Host "Generando build para Web..." -ForegroundColor Yellow
            Invoke-Expression "flutter build web $buildMode --dart-define=FLAVOR=$Environment"
        }
        "all" {
            # Ejecutar builds para todas las plataformas disponibles en el sistema actual
            if ($IsWindows -or $env:OS -match "Windows") {
                Write-Host "Generando build para Windows..." -ForegroundColor Yellow
                Invoke-Expression "flutter build windows $buildMode --dart-define=FLAVOR=$Environment"
                
                Write-Host "Generando build para Android..." -ForegroundColor Yellow
                Invoke-Expression "flutter build apk $buildMode --dart-define=FLAVOR=$Environment -t lib/main.dart"
            }
            if ($IsMacOS) {
                # Write-Host "Generando build para macOS..." -ForegroundColor Yellow
                # Invoke-Expression "flutter build macos $buildMode --dart-define=FLAVOR=$Environment"
                
                # Write-Host "Generando build para iOS..." -ForegroundColor Yellow
                # Invoke-Expression "flutter build ios $buildMode --dart-define=FLAVOR=$Environment"
            }
            if ($IsLinux) {
                Write-Host "Generando build para Linux..." -ForegroundColor Yellow
                Invoke-Expression "flutter build linux $buildMode --dart-define=FLAVOR=$Environment"
            }
            
            Write-Host "Generando build para Web..." -ForegroundColor Yellow
            Invoke-Expression "flutter build web $buildMode --dart-define=FLAVOR=$Environment"
        }
    }
    
    Write-Host "Build completado exitosamente para $Environment en $Platform" -ForegroundColor Green
}
catch {
    Write-Host "Error durante el proceso de build: $_" -ForegroundColor Red
}
finally {
    Pop-Location
}