<?php

namespace App\Policies;

use App\Models\PagoSuscripcion;
use App\Models\User;

class PagoSuscripcionPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('system.admin-dashboard');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, PagoSuscripcion $pagoSuscripcion): bool
    {
        return $user->can('system.admin-dashboard');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('system.admin-dashboard');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, PagoSuscripcion $pagoSuscripcion): bool
    {
        return $user->can('system.admin-dashboard');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, PagoSuscripcion $pagoSuscripcion): bool
    {
        return $user->can('system.admin-dashboard');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, PagoSuscripcion $pagoSuscripcion): bool
    {
        return $user->can('system.admin-dashboard');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, PagoSuscripcion $pagoSuscripcion): bool
    {
        return $user->can('system.admin-dashboard');
    }
}
