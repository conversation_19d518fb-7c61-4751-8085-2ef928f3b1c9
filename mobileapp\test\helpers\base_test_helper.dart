import 'dart:convert';
import 'dart:io';

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'base_test_helper.mocks.dart';

@GenerateMocks([http.Client])
class BaseTest {
  static MockClient? _mockClient;
  static bool _initialized = false;

  /// Inicializa el entorno de pruebas cargando el archivo .env.test
  static Future<void> initialize() async {
    if (!_initialized) {
      await dotenv.load(fileName: "../../.env.test");
      _mockClient = MockClient();
      _initialized = true;
    }
  }

  /// Obtiene una instancia del cliente HTTP mockeado
  static MockClient getMockClient() {
    if (!_initialized) {
      throw Exception(
          'BaseTest no ha sido inicializado. Llama a BaseTest.initialize() primero.');
    }
    return _mockClient!;
  }

  /// Configura una respuesta mockeada para una URL específica
  static void mockHttpResponse(
      String url, String responseBody, int statusCode) {
    when(_mockClient!.get(Uri.parse(url)))
        .thenAnswer((_) async => http.Response(responseBody, statusCode));
  }

  /// Configura una respuesta POST mockeada
  static void mockHttpPostResponse(
      String url, String responseBody, int statusCode) {
    when(_mockClient!.post(
      Uri.parse(url),
      headers: anyNamed('headers'),
      body: anyNamed('body'),
    )).thenAnswer((_) async => http.Response(responseBody, statusCode));
  }

  /// Lee un archivo JSON de prueba
  static String fixture(String path) =>
      File('test/fixture/json/$path').readAsStringSync();

  /// Convierte un string JSON a Map<String, dynamic>
  static Map<String, dynamic> jsonToMap(String jsonString) {
    return json.decode(jsonString);
  }

  /// Convierte un string JSON a List<dynamic>
  static List<dynamic> jsonToList(String jsonString) {
    return json.decode(jsonString);
  }

  /// Obtiene un objeto individual de una lista JSON o devuelve el objeto si ya es un mapa
  static Map<String, dynamic> getJsonObject(String jsonString,
      [int index = 0]) {
    final dynamic decoded = json.decode(jsonString);
    if (decoded is List) {
      return decoded[index];
    }
    return decoded;
  }
}
