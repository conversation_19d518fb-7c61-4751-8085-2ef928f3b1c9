<?php

namespace Database\Seeders;

use App\Models\Localidad;
use App\Services\DataGeneratorService;
use Illuminate\Database\Seeder;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class LocalidadSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $localidades = DataGeneratorService::getLocalidades();

        foreach ($localidades as $localidad) {
            Localidad::create([
                'nombre' => $localidad['nombre'],
                'ubicacion' => $localidad['ubicacion'],
                'limites' => $localidad['limites'],
            ]);
        }

        // Localidad::factory()->count(5)->create();
    }
}
