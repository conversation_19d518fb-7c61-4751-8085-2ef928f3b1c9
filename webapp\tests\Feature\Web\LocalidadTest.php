<?php

namespace Tests\Feature\Web;

use Tests\BaseTest;
use App\Models\User;
use Livewire\Livewire;
use App\Models\Localidad;
use Spatie\Permission\Models\Role;
use PHPUnit\Framework\Attributes\Group;
use Filament\Tables\Actions\DeleteAction;
use Database\Seeders\RolesPermissionSeeder;
use App\Filament\Resources\LocalidadResource;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Filament\Resources\LocalidadResource\Pages\EditLocalidad;
use App\Filament\Resources\LocalidadResource\Pages\CreateLocalidad;
use App\Filament\Resources\LocalidadResource\Pages\ListLocalidades;

#[Group('localidad')]
#[Group('webapp')]
#[Group('localidad_webapp')]
class LocalidadTest extends BaseTest
{
    use RefreshDatabase;

    private array $usuarios;

    // Configuración inicial para cada prueba
    protected function setUp(): void
    {
        parent::setUp();

        $this->seed(RolesPermissionSeeder::class);

        foreach (Role::all() as $rol) {
            $user = User::factory()->create();
            $user->assignRole($rol->name);

            $this->usuarios[$rol->name] = [
                'usuario' => $user,
            ];
        }
    }


    public function test_el_sistema_muestra_las_paginas_de_localidad_correctamente_segun_el_rol_del_usuario(): void
    {

        // 🟢 Test positivos

        $localidad = Localidad::factory()->create();

        $test_roles = $this->_getRolesPositivos(['localidad.list', 'localidad.create', 'localidad.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $this->get(LocalidadResource::getUrl('index'))->assertSuccessful();
            $this->get(LocalidadResource::getUrl('create'))->assertSuccessful();
            $this->get(LocalidadResource::getUrl('edit', ['record' => $localidad->id]))->assertSuccessful();
        }

        // 🔴 Test negativos

        $test_roles = $this->_getRolesNegativos(['localidad.list', 'localidad.create', 'localidad.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $this->get(LocalidadResource::getUrl('index'))->assertForbidden();
            $this->get(LocalidadResource::getUrl('create'))->assertForbidden();
            $this->get(LocalidadResource::getUrl('edit', ['record' => $localidad->id]))->assertForbidden();
        }
    }

    public function test_el_sistema_crea_una_nueva_localidad_solo_con_datos_validos(): void
    {
        // 🟢 Test positivos

        $test_roles = $this->_getRolesPositivos(['localidad.create']);
        $i = 0;

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $newData = Localidad::factory()->make();

            $test = Livewire::test(CreateLocalidad::class);

            $this->assertDatabaseCount(Localidad::class, $i++);

            $test
                ->fillForm([
                    'nombre' => $newData['nombre'],
                    'ubicacion' => $newData['ubicacion'],
                    'limites' => $newData['limites'],
                ])
                ->call('create')
                ->assertHasNoFormErrors();

            $this->assertDatabaseCount(Localidad::class, $i);
            $this->assertDatabaseHas(Localidad::class, [
                'nombre' => $newData->nombre,
                'ubicacion' => json_encode($newData['ubicacion']),
                'limites' => json_encode($newData['limites']),
            ]);
        }

        // 🔴 Test negativos

        $test_roles = $this->_getRolesPositivos(['localidad.create']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $test_cases = [
                // caso 1: campos vacíos
                [
                    'payload' => [],
                    'expectedInvalid' => ['nombre', 'ubicacion', 'limites']
                ],
                // [
                //     'payload' => [
                //         'ubicacion' => [
                //             'latitud' => 10,
                //             'longitud' => 20,
                //         ],
                //     ],
                //     'expectedInvalid' => ['ubicacion']
                // ],
                // [
                //     'payload' => [
                //         'limites' => [
                //             'latitud_min' => 10,
                //             'latitud_max' => 20,
                //             'longitud_min' => 30,
                //             'longitud_max' => 40,
                //         ],
                //     ],
                //     'expectedInvalid' => ['limites']
                // ],

            ];

            $test = Livewire::test(CreateLocalidad::class);

            foreach ($test_cases as $case) {
                $test->fillForm($case['payload']);

                if (isset($payload['ubicacion'])) {
                    $test->set('data.ubicacion', $payload['ubicacion']);
                }

                if (isset($payload['limites'])) {
                    $test->set('data.limites', $payload['limites']);
                }

                $test->call('create')
                    ->assertHasFormErrors($case['expectedInvalid']);
            }
        }
    }

    public function test_el_sistema_edita_una_localidad_solo_con_datos_validos(): void
    {

        // 🟢 Test positivos

        $localidad = Localidad::factory()->create();

        $test_roles = $this->_getRolesPositivos(['localidad.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $localidad->refresh();
            $newData = Localidad::factory()->make()->toArray();

            $test = Livewire::test(EditLocalidad::class, ['record' => $localidad->id]);

            $test
                ->assertFormSet([
                    'nombre' => $localidad->nombre,
                    'ubicacion' => $localidad->ubicacion,
                    'limites' => $localidad->limites,
                ])
                ->fillForm([
                    'nombre' => $newData['nombre'],
                    'ubicacion' => $newData['ubicacion'],
                    'limites' => $newData['limites'],
                ])
                ->call('save')
                ->assertHasNoFormErrors();

            $this->assertDatabaseHas(Localidad::class, [
                'nombre' => $newData['nombre'],
                'ubicacion' => json_encode($newData['ubicacion']),
                'limites' => json_encode($newData['limites']),
            ]);
        }

        // 🔴 Test negativos

        $test_roles = $this->_getRolesPositivos(['localidad.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $localidad->refresh();

            $test_cases = [
                // caso 1: campos vacíos
                [
                    'payload' => [],
                    'expectedInvalid' => ['nombre', 'ubicacion', 'limites']
                ],
            ];

            $test = Livewire::test(EditLocalidad::class, ['record' => $localidad->id]);

            foreach ($test_cases as $case) {
                $test->fillForm($case['payload'])
                    ->call('save')
                    ->assertHasFormErrors($case['expectedInvalid']);
            }

            $this->assertDatabaseHas(Localidad::class, [
                'nombre' => $localidad->nombre,
                'ubicacion' => json_encode($localidad->ubicacion),
                'limites' => json_encode($localidad->limites),
            ]);
        }
    }

    public function test_el_sistema_elimina_correctamente_una_localidad(): void
    {
        // 🟢 Test positivos

        $test_roles = $this->_getRolesPositivos(['localidad.delete']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $localidad = Localidad::factory()->create();
            $this->assertDatabaseCount(Localidad::class, 1);

            $test = Livewire::test(ListLocalidades::class);

            $test
                ->assertTableActionVisible(DeleteAction::class, $localidad)
                ->callTableAction(DeleteAction::class, $localidad);

            $this->assertModelMissing($localidad);
            $this->assertDatabaseCount(Localidad::class, 0);
        }

        // 🔴 Test negativos

        $test_roles = [];

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            // $test = Livewire::test(ListAplicaciones::class);
        }

        $this->assertTrue(true);
    }

    #[Group('test')]
    public function _test_el_sistema_____(): void
    {

        // 🟢 Test positivos

        // $categoria = Zona::factory()->create();

        $test_roles = [];

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            // $test = Livewire::test(ListAplicaciones::class);
        }

        // 🔴 Test negativos

        $test_roles = [];

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            // $test = Livewire::test(ListAplicaciones::class);
        }
    }
}
