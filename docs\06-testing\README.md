# Testing

## Índice

-  [Estrategia de Testing](#estrategia-de-testing)
-  [Backend Testing](#backend-testing)
-  [Frontend Testing](#frontend-testing)
-  [Plantillas](#plantillas)
-  [Mejores Prácticas](#mejores-prácticas)

## Estrategia de Testing

### Backend

Hay que cubrir:

-  Modelos (tests unitarios si son necesarios)
-  Recurso Filament (CRUD)
-  API REST (tests de integración)

Como la aplicacion tiene diferentes roles con diferentes permisos se debe probar cada test con todos los roles. Para los que tiene permiso se probará la acción en sí, para los que no tiene permiso se probará que devuelve el error correspondiente.

## Backend Testing

### Estructura

```
tests/
├── Unit/                    # Tests unitarios
│   ├── BasicTest.php
│   └── Models/
├── Feature/                 # Tests de integración
│   ├── Api/                 # Tests de API
│   │   ├── NegocioApiTest.php
│   │   ├── EventoApiTest.php
│   │   └── ...
│   ├── Auth/                # Tests de autenticación
│   └── Web/                 # Tests de rutas web
└── BaseTest.php             # Clase base compartida
```

### Ejecutar Tests

```bash
# Todos los tests
php artisan test

# Tests específicos
php artisan test --filter NegocioApiTest

# Con cobertura
php artisan test --coverage

# Por grupo
php artisan test --group api
php artisan test --group negocio
```

### BaseTest

**Archivo**: `tests/BaseTest.php`

Clase base que proporciona helpers comunes:

```php
<?php

namespace Tests;

use Tests\TestCase;
use Spatie\Permission\Models\Role;

abstract class BaseTest extends TestCase
{
    /**
     * Roles con unos permisos dados
     *
     * @param array $permisos Array con los nombres de los permisos
     * @return array Array con los nombres de los roles
     */
    protected function _getRolesPositivos(array $permisos): array
    {
        if (empty($permisos)) {
            return [];
        }

        $roles = Role::all();
        $rolesConTodosLosPermisos = [];

        foreach ($roles as $role) {
            $permisosDelRol = $role->permissions->pluck('name')->toArray();
            $tienePermisos = true;

            foreach ($permisos as $permiso) {
                if (!in_array($permiso, $permisosDelRol)) {
                    $tienePermisos = false;
                    break;
                }
            }

            if ($tienePermisos) {
                $rolesConTodosLosPermisos[] = $role->name;
            }
        }

        return $rolesConTodosLosPermisos;
    }

    /**
     * Roles sin ninguno de unos permisos dados
     *
     * @param array $permisos Array con los nombres de los permisos
     * @return array Array con los nombres de los roles
     */
    protected function _getRolesNegativos(array $permisos): array
    {
        if (empty($permisos)) {
            return [];
        }

        return Role::whereDoesntHave('permissions', function ($query) use ($permisos) {
            $query->whereIn('name', $permisos);
        })
            ->get()->pluck('name')->toArray()
        ;
    }

    /**
     * Roles sin alguno de unos permisos dados
     *
     * @param array $permisos Array con los nombres de los permisos
     * @return array Array con los nombres de los roles
     */
    protected function _getRolesNegativosSinAlgunPermiso(array $permisos): array
    {
        if (empty($permisos)) {
            return [];
        }

        $rolesPositivos = $this->_getRolesPositivos($permisos);
        $todosLosRoles = Role::pluck('name')->toArray();

        return array_values(array_diff($todosLosRoles, $rolesPositivos));
    }

    /**
     * Crea un usuario con un rol dado o sin roles
     *
     * @param string |null $roleName Nombre del rol o null para sin roles
     * @return User Usuario creado
     */
    protected function createUserWithRole(string | null $roleName): User
    {
        $user = User::factory()->create();
        if ($roleName) {
            $user->assignRole($roleName);
        }

        return $user;
    }
}
```

### Plantilla: Test de Recurso Filament

Ver: [Plantilla Filament Test](./plantilla-model-backend-filament-test.md)

### Plantilla: Test de API

Ver: [Plantilla API Test](./plantilla-model-backend-api-test.md)

## Frontend Testing

### Estructura

```
test/
├── unit/                    # Tests unitarios
│   ├── models/              # Tests de modelos
│   │   ├── negocio_test.dart
│   │   ├── categoria_test.dart
│   │   └── utils/
│   │       └── model_test_utils.dart
│   └── permission_service_test.dart
├── integration/             # Tests de integración
│   └── app_update_service_test.dart
├── widget/                  # Tests de widgets
│   └── widget_test.dart
├── helpers/                 # Helpers para tests
│   ├── base_test_helper.dart
│   └── api_test_helper.dart
└── fixture/                 # Datos de prueba
    └── json/
        ├── negocio/
        ├── categoria/
        └── ...
```

### Ejecutar Tests

```bash
# Todos los tests
flutter test

# Tests específicos
flutter test test/unit/models/negocio_test.dart

# Con cobertura
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
```

### BaseTest Helper

**Archivo**: `test/helpers/base_test_helper.dart`

```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:http/http.dart' as http;
import 'dart:io';

@GenerateMocks([http.Client])
class BaseTest {
  static MockClient? _mockClient;
  static bool _initialized = false;

  /// Inicializa el entorno de pruebas
  static Future<void> initialize() async {
    if (!_initialized) {
      await dotenv.load(fileName: "../../.env.test");
      _mockClient = MockClient();
      _initialized = true;
    }
  }

  /// Obtiene el cliente HTTP mockeado
  static MockClient getMockClient() {
    if (!_initialized) {
      throw Exception('BaseTest no inicializado');
    }
    return _mockClient!;
  }

  /// Configura una respuesta mockeada
  static void mockHttpResponse(String url, String responseBody, int statusCode) {
    when(_mockClient!.get(Uri.parse(url)))
        .thenAnswer((_) async => http.Response(responseBody, statusCode));
  }

  /// Lee un archivo JSON de prueba
  static String fixture(String path) =>
      File('test/fixture/json/$path').readAsStringSync();
}
```

### Plantilla: Test de Modelo

Ver: [Plantilla Model Test](./plantilla-model-test.md)

### ModelTestUtils

**Archivo**: `test/unit/models/utils/model_test_utils.dart`

Utilidad para tests comunes de modelos:

```dart
import 'package:flutter_test/flutter_test.dart';
import '../../helpers/base_test_helper.dart';

class ModelTestUtils {
  /// Ejecuta tests comunes para cualquier modelo
  static void runCommonModelTests<T>({
    required List<String> validFixturePaths,
    required List<String> invalidFixturePaths,
    required T Function(Map<String, dynamic>) fromJson,
    required Map<String, dynamic> Function(T) toJson,
    required Map<String, dynamic> Function(String) getExpectedValues,
    required Map<String, dynamic> customModel,
    required List<String> requiredFields,
  }) {
    group('Serialización JSON válida', () {
      for (final path in validFixturePaths) {
        test('Debe deserializar correctamente: $path', () {
          final jsonString = BaseTest.fixture(path);
          final json = jsonDecode(jsonString);

          expect(() => fromJson(json), returnsNormally);

          final model = fromJson(json);
          final expected = getExpectedValues(path);

          // Verificar campos esperados
          for (final entry in expected.entries) {
            expect(
              _getFieldValue(model, entry.key),
              equals(entry.value),
              reason: 'Campo ${entry.key} no coincide',
            );
          }
        });
      }
    });

    group('Manejo de JSON inválido', () {
      for (final path in invalidFixturePaths) {
        test('Debe lanzar excepción: $path', () {
          final jsonString = BaseTest.fixture(path);
          final json = jsonDecode(jsonString);

          expect(() => fromJson(json), throwsException);
        });
      }
    });

    group('Serialización bidireccional', () {
      test('toJson() debe ser reversible', () {
        final model = fromJson(customModel);
        final json = toJson(model);
        final modelFromJson = fromJson(json);

        expect(toJson(modelFromJson), equals(json));
      });
    });

    group('Campos requeridos', () {
      for (final field in requiredFields) {
        test('Debe requerir el campo: $field', () {
          final incomplete = Map<String, dynamic>.from(customModel);
          incomplete.remove(field);

          expect(() => fromJson(incomplete), throwsException);
        });
      }
    });
  }

  static dynamic _getFieldValue(dynamic obj, String fieldName) {
    // Reflexión simple para obtener valor de campo
    // Implementación específica según necesidades
  }
}
```

### Ejemplo Real: NegocioTest

**Archivo**: `test/unit/models/negocio_test.dart`

```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mia/models/negocio.dart';
import '../../helpers/base_test_helper.dart';
import 'utils/model_test_utils.dart';

void main() async {
  TestWidgetsFlutterBinding.ensureInitialized();
  await BaseTest.initialize();

  group('Negocio Model Tests', () {
    ModelTestUtils.runCommonModelTests<Negocio>(
      validFixturePaths: [
        'negocio/valid_negocio_completo.json',
        'negocio/valid_negocio_minimo.json',
        'negocio/valid_negocio_parcial.json',
      ],
      invalidFixturePaths: [
        'negocio/invalid_negocio_missing_id.json',
        'negocio/invalid_negocio_missing_nombre.json',
        'negocio/invalid_negocio_missing_direccion.json',
      ],
      fromJson: Negocio.fromJson,
      toJson: (negocio) => negocio.toJson(),
      getExpectedValues: (fixturePath) => _getExpectedValues(fixturePath),
      customModel: {
        'id': 1,
        'nombre': 'Test Negocio',
        'direccion': 'Calle Test',
        'contacto': '+34 600 000 000',
        'suscripcion': {'id': 1, 'plan': 'basic', 'status': 1},
      },
      requiredFields: ['id', 'nombre', 'direccion', 'contacto', 'suscripcion'],
    );

    _runSpecificTests();
  });
}

Map<String, dynamic> _getExpectedValues(String fixturePath) {
  if (fixturePath.contains('completo')) {
    return {
      'id': 1,
      'nombre': 'Restaurante Ejemplo',
      // ... más campos
    };
  }
  // ... más casos
  return {};
}

void _runSpecificTests() {
  group('Tests específicos de Negocio', () {
    test('Debe parsear ubicación correctamente', () {
      final json = {
        'id': 1,
        'nombre': 'Test',
        'direccion': 'Test',
        'contacto': 'Test',
        'ubicacion': {
          'latitud': '40.4168',
          'longitud': '-3.7038',
        },
        'suscripcion': {},
      };

      final negocio = Negocio.fromJson(json);

      expect(negocio.ubicacion, isNotNull);
      expect(negocio.ubicacion!['latitud'], equals('40.4168'));
      expect(negocio.ubicacion!['longitud'], equals('-3.7038'));
    });
  });
}
```

## Mejores Prácticas

### 1. Nomenclatura

```dart
// ✅ Bien
test('debe_crear_negocio_con_datos_validos')
test('debe_lanzar_excepcion_cuando_falta_nombre')

// ❌ Mal
test('test1')
test('negocio')
```

### 2. Arrange-Act-Assert

```php
public function test_ejemplo(): void
{
    // Arrange (Preparar)
    $user = User::factory()->create();
    $negocio = Negocio::factory()->create();

    // Act (Actuar)
    $response = $this->actingAs($user)
        ->getJson("/api/v1/negocios/{$negocio->id}");

    // Assert (Afirmar)
    $response->assertStatus(200);
    $response->assertJson(['id' => $negocio->id]);
}
```

### 3. Usar factories

```php
// ✅ Bien
$negocio = Negocio::factory()->create();

// ❌ Mal
$negocio = new Negocio();
$negocio->nombre = 'Test';
$negocio->save();
```

## Próximos Pasos

-  [Plantilla API Test](./plantilla-api-test.md)
-  [Plantilla Model Test](./plantilla-model-test.md)
-  [Fixtures JSON](./fixtures.md)
