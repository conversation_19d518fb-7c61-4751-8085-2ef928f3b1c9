import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:mia/config/colors.dart';
import 'package:mia/config/styles.dart';
import 'package:mia/models/evento.dart';
import 'package:mia/services/core_service.dart';
import 'package:mia/widgets/imagenes/carousel.dart';
import 'package:mia/widgets/lista_con_separador.dart';

class EventoModal extends StatelessWidget {
  final Evento evento;
  final BuildContext parentContext;
  final bool showLinkNegocio;

  const EventoModal(
      {super.key,
      required this.evento,
      required this.parentContext,
      this.showLinkNegocio = true});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  evento.nombre,
                  style: AppStyles.getStyle(context, 'h6',
                      fontWeight: 'bold', color: AppColors.current.textColor),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: ListaConSeparador(
              layoutType: TipoLayout.wrap,
              direction: Axis.horizontal,
              wrapCrossAlignment: WrapCrossAlignment.center,
              separator: const Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.0),
                child: Icon(Icons.circle, size: 5),
              ),
              children: [
                AutoSizeText(
                  evento.getHorarioString(),
                  minFontSize: 8,
                  style: AppStyles.getStyle(context, 'base',
                      fontWeight: 'bold', color: AppColors.current.textColor),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                // Text(
                //   CoreService.datetimeToLocale(evento.fechaInicio,
                //       format: 'E d MMM HH:mm'),
                //   style: AppStyles.getStyle('base',
                //       fontWeight: 'bold', color: AppColors.current.textColor),
                // ),
                // Text(
                //   CoreService.datetimeToLocale(evento.fechaFin,
                //       format: 'E d MMM HH:mm'),
                //   style: AppStyles.getStyle('base',
                //       fontWeight: 'bold', color: AppColors.current.textColor),
                // ),
              ],
            ),
          ),
          _buildInfoNegocio(context),
          const SizedBox(height: 16),
          Expanded(
            child: SingleChildScrollView(
              child: _buildEvento(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoNegocio(BuildContext context) {
    if (evento.getNegocio() == null) {
      return Container();
    }

    return GestureDetector(
      onTap: () {
        Navigator.pushNamed(
          parentContext,
          '/negocio',
          arguments: evento.getNegocio(),
        );
      },
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: [
          Text(
            evento.getNegocio()?.nombre ?? '',
            style: AppStyles.getStyle(context, 'base', fontWeight: 'medium'),
          ),
          showLinkNegocio
              ? Text(
                  'Ver más',
                  style: AppStyles.getStyle(context, 'base',
                      color: AppColors.current.accentColor,
                      fontWeight: 'medium'),
                )
              : Container(),
        ],
      ),
    );
  }

  Widget _buildEvento(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Carousel(imagenes: evento.imagenes ?? []),
        const SizedBox(height: 8),
        Text(
          evento.descripcion,
          style: AppStyles.getStyle(context, 'base'),
        ),
        const SizedBox(height: 8),
        evento.url == ''
            ? Container()
            : Center(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: ButtonTheme(
                    child: TextButton(
                      onPressed: () {
                        CoreService.launchWeb(evento.url);
                      },
                      child: Text(
                        'Más información',
                        style: AppStyles.getStyle(context, 'base',
                            color: AppColors.current.accentColor),
                      ),
                    ),
                  ),
                ),
              ),
      ],
    );
  }
}

// Función para mostrar el modal desde cualquier lugar
void mostrarEventoModal(BuildContext context, Evento evento,
    [bool showLinkNegocio = true]) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    builder: (context) {
      return EventoModal(
        evento: evento,
        parentContext: context,
        showLinkNegocio: showLinkNegocio,
      );
    },
  );
}
