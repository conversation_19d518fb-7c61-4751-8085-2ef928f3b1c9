import 'dart:convert';
import 'dart:io' as io;

import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:package_info_plus/package_info_plus.dart';
import 'package:mia/widgets/dialogos/dialogo_app_update.dart';

typedef GetCurrentBuild = Future<int> Function();

class AppUpdateService {
  final http.Client httpClient;
  final GetCurrentBuild getCurrentBuild;
  final bool isAndroid;
  final bool isIOS;

  AppUpdateService({
    required this.httpClient,
    required this.getCurrentBuild,
    this.isAndroid = false,
    this.isIOS = false,
  });

  /// Factory constructor que usa la plataforma real del dispositivo
  factory AppUpdateService.platform({
    required http.Client httpClient,
    GetCurrentBuild? getCurrentBuild,
  }) {
    return AppUpdateService(
      httpClient: httpClient,
      getCurrentBuild: getCurrentBuild ?? defaultGetCurrentBuild,
      isAndroid: io.Platform.isAndroid,
      isIOS: io.Platform.isIOS,
    );
  }

  /// Función por defecto para obtener el build actual, usando package_info_plus.
  static Future<int> defaultGetCurrentBuild() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      return int.tryParse(packageInfo.buildNumber) ?? 0;
    } catch (e) {
      debugPrint("Error al obtener el build actual: $e");
      return 0;
    }
  }

  /// Comprueba si hay actualizaciones y muestra el diálogo de actualización en caso afirmativo.
  Future<bool> checkForUpdates(BuildContext context) async {
    try {
      final int currentBuild = await getCurrentBuild();

      // Determinar la ruta de la API según la plataforma
      String platformPath = '';
      if (isAndroid) {
        platformPath = '/android-version';
      } else if (isIOS) {
        platformPath = '/ios-version';
      } else {
        return false;
      }

      final String? baseUrl = dotenv.env['API_URL'];
      if (baseUrl == null) {
        debugPrint("La variable de entorno API_URL no está definida.");
        return false;
      }
      final String apiUrl = baseUrl + platformPath;

      final response = await httpClient.get(Uri.parse(apiUrl));
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        if (data.containsKey('latest_build') &&
            data.containsKey('minimum_build_allowed')) {
          final int latestBuild = data['latest_build'] ?? 1;
          final int minBuild = data['minimum_build_allowed'] ?? 1;
          final String? updateUrl = data['update_url'] ?? '';

          if (currentBuild < latestBuild) {
            final bool forceUpdate = currentBuild < minBuild;
            // Mostrar diálogo
            if (context.mounted) {
              await showDialog(
                context: context,
                barrierDismissible: !forceUpdate,
                builder: (BuildContext context) {
                  return DialogoAppUpdate(
                    forceUpdate: forceUpdate,
                    updateUrl: updateUrl,
                  );
                },
              );
              return true;
            }
          } else {
            debugPrint("No hay actualizaciones disponibles.");
          }
        } else {
          debugPrint(
              "La respuesta de la API no contiene los campos requeridos.");
        }
      } else {
        debugPrint("Error al llamar a la API: código ${response.statusCode}");
      }
    } catch (e) {
      debugPrint("Error al verificar actualizaciones: $e");
    }
    return false;
  }
}
