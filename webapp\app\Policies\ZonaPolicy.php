<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Zona;
use Illuminate\Auth\Access\HandlesAuthorization;

class ZonaPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('zona.list');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Zona $zona): bool
    {
        return $user->can('zona.read');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('zona.create');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Zona $zona): bool
    {
        return $user->can('zona.update');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Zona $zona): bool
    {
        return $user->can('zona.delete');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Zona $zona): bool
    {
        return $user->can('zona.restore');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Zona $zona): bool
    {
        return $user->can('zona.force-delete');
    }
}
