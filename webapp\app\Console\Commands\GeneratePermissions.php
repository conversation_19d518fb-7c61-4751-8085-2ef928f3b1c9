<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class GeneratePermissions extends Command
{
    protected $signature = 'permissions:generate {--fresh : Eliminar permisos existentes}';
    protected $description = 'Genera permisos automáticos para recursos Filament y los asigna a roles';

    // Definimos los roles del sistema
    protected $roles = [
        'admin',
        'owner',
        'cliente',
        'usuario'
    ];

    // Permisos genéricos para cada modelo
    protected $modelPermissions = [
        'list',
        'read',
        'create',
        'update',
        'delete',
        'force-delete',
        'restore'
    ];

    // Permisos de sistema
    protected $systemPermissions = [
        'access-panel',
        'settings',
        'admin',
        'users',
        'roles',
        'permissions'
    ];

    public function handle()
    {
        $this->error('¡ADVERTENCIA! Esto eliminará todos los permisos y roles existentes.');
        $this->error('De momento no se permite');
        return;

        // Si se usa la opción --fresh, eliminar todos los permisos y roles existentes
        if ($this->option('fresh')) {
            $this->info('Eliminando permisos y roles existentes...');
            Permission::query()->delete();
            Role::query()->delete();
        }

        // Crear los roles definidos
        $this->createRoles();

        // Generar permisos para modelos definidos en el seeder
        $this->generateModelPermissions();

        // Generar permisos de sistema
        $this->generateSystemPermissions();

        // Generar también permisos para recursos Filament
        // $this->generateFilamentPermissions();

        // Asignar permisos a los roles
        $this->assignPermissionsToRoles();

        $this->info('¡Roles y permisos generados correctamente!');
    }

    protected function createRoles()
    {
        $this->info('Creando roles...');

        foreach ($this->roles as $role) {
            Role::firstOrCreate(['name' => $role]);
            $this->line("- Rol '{$role}' creado o verificado");
        }
    }

    protected function generateModelPermissions()
    {
        $this->info('Generando permisos para modelos...');

        // Modelos identificados en tu seeder
        $models = ['localidad', 'zona', 'categoria', 'negocio', 'user'];

        foreach ($models as $model) {
            $this->line("Generando permisos para {$model}:");

            foreach ($this->modelPermissions as $action) {
                $permissionName = "{$model}.{$action}";

                Permission::firstOrCreate([
                    'name' => $permissionName,
                    'guard_name' => 'web',
                ]);

                $this->line("  - {$permissionName}");
            }
        }
    }

    protected function generateSystemPermissions()
    {
        $this->info('Generando permisos de sistema...');

        foreach ($this->systemPermissions as $permission) {
            $permissionName = "system.{$permission}";

            Permission::firstOrCreate([
                'name' => $permissionName,
                'guard_name' => 'web',
            ]);

            $this->line("- {$permissionName}");
        }
    }

    protected function generateFilamentPermissions()
    {
        $this->info('Generando permisos para recursos Filament...');

        // Obtener todos los recursos Filament
        $resourceClasses = $this->getFilamentResources();

        foreach ($resourceClasses as $resource) {
            $model = $resource::getModel();
            $modelName = Str::lower(class_basename($model));

            // Mapear acciones de Filament a formato compatible
            $actions = [
                'view_any' => 'list',
                'view' => 'read',
                'create' => 'create',
                'update' => 'update',
                'delete' => 'delete',
                'delete_any' => 'delete',
                'force_delete' => 'force-delete',
                'restore' => 'restore',
            ];

            foreach ($actions as $filamentAction => $ourAction) {
                // Formato Filament
                $filamentPermission = "{$modelName}:{$filamentAction}";
                // Nuestro formato
                $ourPermission = "{$modelName}.{$ourAction}";

                // Crear el permiso en formato Filament
                Permission::firstOrCreate([
                    'name' => $filamentPermission,
                    'guard_name' => 'web',
                ]);

                $this->line("- {$filamentPermission} (mapea a {$ourPermission})");
            }
        }
    }

    protected function assignPermissionsToRoles()
    {
        $this->info('Asignando permisos a roles...');

        // Admin tiene todos los permisos
        $adminRole = Role::findByName('admin');

        $allPermissions = Permission::all();

        $this->line("- Todos los permisos asignados al rol 'admin'");

        // Owner
        $ownerRole = Role::findByName('owner');
        $ownerPermissions = $this->getOwnerPermissions();
        $ownerRole->syncPermissions($ownerPermissions);
        $this->line("- Permisos asignados al rol 'owner'");

        // Cliente
        $clienteRole = Role::findByName('cliente');
        $clientePermissions = $this->getClientePermissions();
        $clienteRole->syncPermissions($clientePermissions);
        $this->line("- Permisos asignados al rol 'cliente'");

        // Usuario (no tiene permisos específicos en tu seeder)
        $this->line("- El rol 'usuario' no tiene permisos asignados");
    }

    protected function getOwnerPermissions()
    {
        $permissionNames = [];

        // Todos los permisos para estos modelos
        $fullAccessModels = ['categoria', 'zona', 'localidad', 'negocio', 'user'];

        foreach ($fullAccessModels as $model) {
            foreach ($this->modelPermissions as $action) {
                $permissionNames[] = "{$model}.{$action}";
            }
        }

        // Permisos de sistema específicos
        $permissionNames[] = "system.access-panel";
        $permissionNames[] = "system.admin-dashboard";

        // También agregar equivalentes de Filament
        // foreach ($fullAccessModels as $model) {
        //     $filamentActions = ['view_any', 'view', 'create', 'update', 'delete', 'delete_any', 'force_delete', 'restore'];
        //     foreach ($filamentActions as $action) {
        //         $permissionNames[] = Str::lower(class_basename($model)) . ":{$action}";
        //     }
        // }

        return Permission::whereIn('name', $permissionNames)->get();
    }

    protected function getClientePermissions()
    {
        $permissionNames = [
            'negocio.list',
            'negocio.read',
            'system.access-panel',
            // Equivalentes de Filament
            // 'negocio:view_any',
            // 'negocio:view',
        ];

        return Permission::whereIn('name', $permissionNames)->get();
    }

    private function getFilamentResources()
    {
        $resources = [];
        $resourcesPath = app_path('Filament/Resources');

        if (file_exists($resourcesPath)) {
            $resourceFiles = glob("{$resourcesPath}/*.php");

            foreach ($resourceFiles as $file) {
                $className = 'App\\Filament\\Resources\\' . pathinfo($file, PATHINFO_FILENAME);

                if (class_exists($className) && is_subclass_of($className, 'Filament\\Resources\\Resource')) {
                    // No incluir los recursos de permisos y roles para evitar duplicados
                    if (!in_array($className, [
                        'App\\Filament\\Resources\\RoleResource',
                        'App\\Filament\\Resources\\PermissionResource',
                    ])) {
                        $resources[] = $className;
                    }
                }
            }
        }

        return $resources;
    }
}
