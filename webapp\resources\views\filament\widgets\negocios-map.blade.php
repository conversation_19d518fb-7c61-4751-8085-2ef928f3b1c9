<x-filament-widgets::widget>
        {{-- Contenedor con x-data y x-init --}}
        <div class="border rounded shadow bg-gray-100 dark:bg-gray-700 w-full h-72 relative" wire:ignore>
            <div
                style="height: 500px;"
                class="border border-gray-300 bg-white dark:bg-gray-800 rounded"
                x-data="{
                    map: null,
                    negocios:{{$negocios}},
                    initMap() {
                        // Centro por defecto
                        let lat = 36.9990019;
                        let lng = -6.5478919;
                        // Inicializamos el mapa
                        this.map = L.map('map').setView([lat, lng], 14);
                        // Capa de OpenStreetMap
                        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                            maxZoom: 20,
                            attribution: '© OpenStreetMap contributors'
                        }).addTo(this.map);
                    // 3) Añadimos un marcador por cada negocio
                    this.negocios.forEach((negocio) => {
                        // Extraer lat y lng
                        let latitud = parseFloat(negocio.ubicacion?.latitud ?? lat);
                        let longitud = parseFloat(negocio.ubicacion?.longitud ?? lng);
                        // Crear marcador
                        let marker = L.marker([latitud, longitud]).addTo(this.map);
                        // Popup con información del negocio
                        let nombre = negocio.nombre ?? 'Negocio sin nombre';
                        let infoPopup = `<b>${nombre}</b><br/>
                                         Lat: ${latitud}, Lng: ${longitud}`;
                        marker.bindPopup(infoPopup);
                    });
            
            /*
                        // Ejemplo: Añadir marcador en el centro
                        L.marker([lat, lng]).addTo(this.map)
                            .bindPopup('Marcador de ejemplo')
                            // .openPopup()
                            ;
            */
                    }
                }"
                x-init="initMap()"
            >
                {{-- Contenedor del mapa --}}
                <div id="map" class="w-full h-full"></div>
            </div>
        </div>

</x-filament-widgets::widget>
