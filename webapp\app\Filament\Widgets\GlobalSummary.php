<?php

namespace App\Filament\Widgets;

use App\Models\Evento;
use App\Models\Negocio;
use App\Models\Categoria;
use Filament\Widgets\Widget;

class GlobalSummary extends Widget
{
    protected static string $view = 'filament.widgets.global-summary';

    public $numCategorias;
    public $numNegocios;
    public $numEventos;

    public function mount(): void
    {
        // Lógica para obtener los datos globales
        $numCategorias = Categoria::count();
        $numNegocios = Negocio::count();
        $numEventos = Evento::count();

        $this->numCategorias = $numCategorias;
        $this->numNegocios = $numNegocios;
        $this->numEventos = $numEventos;
    }
}
