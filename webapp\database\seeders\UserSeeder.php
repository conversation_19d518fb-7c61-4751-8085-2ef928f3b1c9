<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $ownerRole = Role::firstOrCreate(['name' => 'owner']);
        $clienteRole = Role::firstOrCreate(['name' => 'cliente']);
        $usuarioRole = Role::firstOrCreate(['name' => 'usuario']);

        // Crea un usuario desde las variables del archivo .env
        $adminEmail = env('ADMIN_EMAIL', '<EMAIL>');
        $adminPassword = env('ADMIN_PASSWORD', 'password');

        $user = User::firstOrCreate(
            ['email' => $adminEmail],
            [
                'name' => 'Admin', // Nombre predeterminado
                'password' => bcrypt($adminPassword),
            ]
        );

        // Asigna el rol admin al usuario
        if (!$user->hasRole('admin')) {
            $user->assignRole($adminRole);
        }
        if (!$user->hasRole('owner')) {
            $user->assignRole('owner');
        }
        // $this->command->info('Usuario super administrador creado: ' . $adminEmail);

        // Crea un propietario
        User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ])->assignRole($ownerRole);


        // Crea varios clientes
        User::factory(4)->create([
            'password' => bcrypt('password'),
        ])
            ->add(User::factory()->create([
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
            ]))
            ->each(fn(User $cliente) => $cliente->assignRole($clienteRole));

        // Crea varios usuarios
        User::factory(19)->create([
            'password' => bcrypt('password'),
        ])
            ->add(User::factory()->create([
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
            ]))
            ->each(fn(User $usuario) => $usuario->assignRole($usuarioRole));
    }
}
