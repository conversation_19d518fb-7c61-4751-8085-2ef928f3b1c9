import 'package:flutter_test/flutter_test.dart';
import 'package:mia/utils/tree.dart'; // Importa la clase Tree
import 'package:mia/utils/treeable.dart'; // Importa la interfaz Treeable

// Clase mock para Treeable (para simplificar las pruebas)
class MockTreeable implements Treeable {
  final int id;
  final int? parentId;

  MockTreeable({required this.id, this.parentId});

  @override
  int getId() => id;

  @override
  int? getIdPadre() => parentId;

  @override
  Treeable createDummyNode() => MockTreeable(id: -1, parentId: null);
}

void main() {
  group('Tree', () {
    test('buildTree - árbol con un solo nodo', () {
      final elementos = [MockTreeable(id: 1)];
      final arbol = Tree.buildTree<MockTreeable>(elementos);
      expect(arbol?.data.id, 1);
      expect(arbol?.children.length, 0);
    });

    test('buildTree - árbol con múltiples nodos', () {
      final elementos = [
        MockTreeable(id: 1),
        MockTreeable(id: 2, parentId: 1),
        MockTreeable(id: 3, parentId: 1),
      ];
      final arbol = Tree.buildTree<MockTreeable>(elementos);
      expect(arbol?.data.id, 1);
      expect(arbol?.children.length, 2);
      expect(arbol?.children[0].data.getId(), 2);
      expect(arbol?.children[1].data.getId(), 3);
    });

    // Añade más tests para otras funcionalidades de Tree
    test('ancestors - nodo hoja', () {
      final elementos = [
        MockTreeable(id: 1),
        MockTreeable(id: 2, parentId: 1),
        MockTreeable(id: 3, parentId: 2),
      ];
      final arbol = Tree.buildTree<MockTreeable>(elementos);
      final nodo3 = arbol?.children[0].children[0]; // Nodo con id 3
      final ancestros = nodo3?.antecesores;
      expect(ancestros?.length, 2);
      expect(ancestros?[0].data.getId(), 2);
      expect(ancestros?[1].data.getId(), 1);
    });

    test('descendants - nodo raíz', () {
      final elementos = [
        MockTreeable(id: 1),
        MockTreeable(id: 2, parentId: 1),
        MockTreeable(id: 3, parentId: 1),
      ];
      final arbol = Tree.buildTree<MockTreeable>(elementos);
      final descendientes = arbol?.descendientes;
      expect(descendientes?.length, 2);
      expect(descendientes?[0].data.id, 2);
      expect(descendientes?[1].data.id, 3);
    });

    test('siblings - nodo intermedio', () {
      final elementos = [
        MockTreeable(id: 1),
        MockTreeable(id: 2, parentId: 1),
        MockTreeable(id: 3, parentId: 1),
        MockTreeable(id: 4, parentId: 1),
      ];
      final arbol = Tree.buildTree<MockTreeable>(elementos);
      final nodo2 = arbol?.children[0]; // Nodo con id 2
      final hermanos = nodo2?.siblings;
      expect(hermanos?.length, 2);
      expect(hermanos?[0].data.getId(), 3);
      expect(hermanos?[1].data.getId(), 4);
    });
  });
}
