<?php

namespace App\Filament\Widgets;

use App\Models\Zona;
use App\Models\Negocio;
use Filament\Forms\Form;
use App\Models\Categoria;
use App\Models\Localidad;
use App\Enums\EstadoSuscripcion;
use Illuminate\Support\Collection;
use Filament\Forms\Components\Select;
use Filament\Widgets\Widget;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;

class NegociosMapWithFilters extends Widget implements HasForms
{
    use InteractsWithForms;

    protected static string $view = 'filament.widgets.negocios-map-with-filters';

    protected int | string | array $columnSpan = 'full';

    public ?Collection $negocios = null;
    public array $filters = [
        'localidad' => null,
        'zona' => null,
        'categoria' => null,
        'suscripcion' => null,
    ];

    protected $listeners = [
        'showEditNegocio' => 'showEditNegocio',
    ];

    public function mount(): void
    {
        $this->negocios = $this->getNegociosFiltrados();
    }

    protected function getForms(): array
    {
        return [
            'filtersForm',
        ];
    }

    public function filtersForm(Form $form): Form
    {
        return $form
            ->statePath('filters')
            ->schema([
                Select::make('localidad')
                    ->label('Localidades')
                    ->options(Localidad::all()->pluck('nombre', 'id'))
                    ->placeholder('Todas las localidades')
                    ->selectablePlaceholder(false)
                    ->reactive()
                    ->afterStateUpdated(function () {
                        $this->filters['zona'] = null;
                        $this->negocios = $this->getNegociosFiltrados();
                    }),

                Select::make('zona')
                    // ->options(Zona::all()->pluck('nombre', 'id'))
                    ->options(function () {
                        $localidad = $this->filters['localidad'];

                        if (!$localidad) {
                            $localidad = Localidad::first()?->id ?? null;
                        }

                        return Zona::where('localidad_id', $localidad)->pluck('nombre', 'id');
                    })
                    ->label('Zona')
                    ->placeholder('Todas las zonas')
                    ->reactive()
                    ->afterStateUpdated(function () {
                        $this->negocios = $this->getNegociosFiltrados();
                    }),

                Select::make('categoria')
                    ->options(function () {
                        $categorias = Categoria::whereHas('negocios', function ($query) {

                            if (empty($this->filters['localidad'])) {
                                $this->filters['localidad'] = Localidad::first()?->id ?? null;
                            }

                            $query->whereHas('zona', function ($query) {
                                $query->where('localidad_id', $this->filters['localidad']);

                                if ($this->filters['zona']) {
                                    $query->where('id', $this->filters['zona']);
                                }
                            });
                        })->pluck('nombre', 'id');

                        return $categorias;
                    })
                    ->label('Categoría')
                    ->placeholder('Todas las categorías')
                    ->reactive()
                    ->afterStateUpdated(function () {
                        $this->negocios = $this->getNegociosFiltrados();
                    }),

                Select::make('suscripcion')
                    ->options([
                        EstadoSuscripcion::ACTIVE->value => 'Activa',
                        EstadoSuscripcion::INACTIVE->value => 'Inactiva',
                    ])
                    ->label('Suscripción')
                    ->placeholder('Todas las suscripciones')
                    ->reactive()
                    ->afterStateUpdated(function () {
                        $this->negocios = $this->getNegociosFiltrados();
                    }),
            ])
            ->columns(4);
    }

    private function getNegociosFiltrados(): Collection
    {
        $negociosFiltrados = Negocio::query();

        // Obtener los filtros del formulario
        $formData = $this->filtersForm->getState();

        $addFiltroLocalidad = !empty($formData['localidad']);
        if ($addFiltroLocalidad) {
            $negociosFiltrados->whereHas('zona', function ($query) use ($formData) {
                $query->where('localidad_id', $formData['localidad']);
            });
        }

        $addFiltroZona = !empty($formData['zona']);
        if ($addFiltroZona) {
            $negociosFiltrados->where('zona_id', $formData['zona']);
        }

        $addFiltroCategoria = !empty($formData['categoria']);
        if ($addFiltroCategoria) {
            $negociosFiltrados->whereHas('categorias', function ($query) use ($formData) {
                $query->where('categoria_id', $formData['categoria']);
            });
        }

        $addFiltroSuscripcion = !empty($formData['suscripcion']) &&
            in_array($formData['suscripcion'], array_map(fn($case) => $case->value, EstadoSuscripcion::cases()));

        if ($addFiltroSuscripcion) {
            $negociosFiltrados->whereHas('suscripcion', function ($query) use ($formData) {
                $query->where('status', $formData['suscripcion']);
            });
        }

        $negociosFiltrados = $negociosFiltrados->with(['categorias', 'zona', 'suscripcion', 'zona.localidad']);

        return $negociosFiltrados->get();
    }

    public function clearFilters(): void
    {
        $this->filters = [
            'localidad' => null,
            'zona' => null,
            'categoria' => null,
            'suscripcion' => null,
        ];

        $this->filtersForm->fill($this->filters);
        $this->negocios = $this->getNegociosFiltrados();
    }

    public function showEditNegocio(int $negocioId)
    {
        $this->dispatch('showEditNegocio', negocioId: $negocioId);
    }

    protected function getViewData(): array
    {
        $formData = $this->filtersForm->getState();
        return [
            'negocios' => $this->negocios,
            'filters' => $formData,
        ];
    }
}
