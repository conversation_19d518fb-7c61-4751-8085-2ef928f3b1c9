import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import 'package:mia/config/colors.dart';
import 'package:mia/models/negocio.dart';
import 'package:mia/services/core_service.dart';
import 'package:mia/widgets/botones/fav_button.dart'; // Cambiado a FavButton simple

class NegocioTooltip extends StatefulWidget {
  final Negocio negocio;
  final LatLng? ubicacionUsuario;
  final VoidCallback? onClose;

  const NegocioTooltip({
    super.key,
    required this.negocio,
    this.ubicacionUsuario,
    this.onClose,
  });

  @override
  State<NegocioTooltip> createState() => _NegocioTooltipState();
}

class _NegocioTooltipState extends State<NegocioTooltip> {
  String? distanciaTexto;

  @override
  void initState() {
    super.initState();
    _calcularDistanciaTexto();
  }

  @override
  void didUpdateWidget(NegocioTooltip oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Recalcular la distancia si cambia la ubicación del usuario
    if (widget.ubicacionUsuario != oldWidget.ubicacionUsuario) {
      _calcularDistanciaTexto();
    }
  }

  void _calcularDistanciaTexto() {
    if (widget.ubicacionUsuario == null) {
      setState(() {
        distanciaTexto = null;
      });
      return;
    }

    final distancia =
        widget.negocio.calcularDistancia(widget.ubicacionUsuario!);
    if (distancia == null) {
      setState(() {
        distanciaTexto = null;
      });
      return;
    }

    // Formatear la distancia según su magnitud
    setState(() {
      if (distancia < 1000) {
        distanciaTexto = '${distancia.round()} m';
      } else {
        distanciaTexto = '${(distancia / 1000).toStringAsFixed(1)} km';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Material(
      elevation: 4.0,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        width: 220,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppColors.current.surfaceColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildNombreNegocio(theme),
            const SizedBox(height: 4),
            _buildDistanciaYFavorito(),
            const SizedBox(height: 4),
            _buildHorarioConEstado(),
            _buildDireccion(),
            _buildCategorias(),
            const SizedBox(height: 8),
            _buildButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildButtons(BuildContext context) {
    return Wrap(
      spacing: 8,
      alignment: WrapAlignment.spaceBetween,
      children: [
        TextButton.icon(
          onPressed: () {
            widget.onClose?.call();
            Navigator.of(context).pushNamed(
              '/negocio',
              arguments: widget.negocio,
            );
          },
          style: TextButton.styleFrom(
            foregroundColor: AppColors.current.accentColor,
          ),
          icon: const Icon(Icons.info_outline, size: 16),
          label: const Text('Ver más'),
        ),
        TextButton.icon(
          onPressed: () {
            widget.onClose?.call();
            CoreService.launchMap(widget.negocio);
          },
          style: TextButton.styleFrom(
            foregroundColor: AppColors.current.accentColor,
          ),
          icon: const Icon(Icons.directions, size: 16),
          label: const Text('Cómo llegar'),
        ),
      ],
    );
  }

  // Construye el widget para el nombre del negocio
  Widget _buildNombreNegocio(ThemeData theme) {
    return Text(
      widget.negocio.nombre,
      style: theme.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold, color: AppColors.current.textColor),
      textAlign: TextAlign.left,
      overflow: TextOverflow.ellipsis,
      maxLines: 3,
    );
  }

  // NUEVO: Construye el widget para mostrar la distancia y el botón de favorito
  Widget _buildDistanciaYFavorito() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Mostrar distancia si está disponible
        if (distanciaTexto != null)
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.place_outlined,
                size: 14,
                color: AppColors.current.accentColor,
              ),
              const SizedBox(width: 2),
              Text(
                distanciaTexto!,
                style: TextStyle(
                  fontSize: 12,
                  color: AppColors.current.textColor,
                ),
              ),
            ],
          )
        else
          const SizedBox.shrink(),

        // Botón de favoritos sin padding
        FavButton(
          negocio: widget.negocio,
          size: 20,
          padding: EdgeInsets.zero, // Especificar padding cero
        ),
      ],
    );
  }

  // MODIFICADO: Construye el widget para mostrar el horario con el estado
  Widget _buildHorarioConEstado() {
    final horarioHoy = widget.negocio.getAllHorariosHoy();
    if (horarioHoy == null) return const SizedBox.shrink();

    final bool estaAbierto = widget.negocio.isOpenNow();

    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.access_time,
            size: 14,
            color: AppColors.current.textColor,
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Wrap(
              spacing: 4,
              children: [
                // Indicador de abierto/cerrado
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                  decoration: BoxDecoration(
                    color: estaAbierto
                        ? Colors.green.shade100
                        : Colors.red.shade100,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    estaAbierto ? 'Abierto' : 'Cerrado',
                    style: TextStyle(
                      fontSize: 10,
                      color: estaAbierto
                          ? AppColors.current.successColor
                          : AppColors.current.errorColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),

                // Horario
                Text(
                  horarioHoy,
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.current.textColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Construye el widget para mostrar la dirección
  Widget _buildDireccion() {
    if (widget.negocio.direccion.isEmpty) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.location_on_outlined,
            size: 14,
            color: AppColors.current.textColor,
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              widget.negocio.direccion,
              style: TextStyle(
                fontSize: 12,
                color: AppColors.current.textColor,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }

  // Construye el widget para mostrar las categorías
  Widget _buildCategorias() {
    if (widget.negocio.categorias == null ||
        widget.negocio.categorias!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Row(
        children: [
          Icon(
            Icons.category_outlined,
            size: 14,
            color: AppColors.current.textColor,
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              widget.negocio.categorias!.map((c) => c.nombre).join(', '),
              style: TextStyle(
                fontSize: 12,
                color: AppColors.current.textColor,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ],
      ),
    );
  }
}
