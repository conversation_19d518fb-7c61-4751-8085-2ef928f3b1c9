<?php

namespace App\Policies;

use App\Models\Negocio;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class NegocioPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Negocio $negocio): bool
    {
        return true;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('negocio.create');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Negocio $negocio): bool
    {
        return $user->can('negocio.update') || $user->id === $negocio->user_id;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Negocio $negocio): bool
    {
        return $user->can('negocio.delete') /*|| $user->id === $negocio->user_id*/;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Negocio $negocio): bool
    {
        return $user->can('negocio.restore');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Negocio $negocio): bool
    {
        return $user->can('negocio.force-delete');
    }
}
