name: Flutter Build iOS

on:
   workflow_dispatch:
      inputs:
         export_method:
            description: "Seleccione el método de exportación para el archive"
            required: true
            default: "app-store"
            type: choice
            options:
               - app-store
               - ad-hoc
               - development
         salida:
            description: "Seleccione la salida deseada"
            required: true
            default: "Unsigned .app"
            type: choice
            options:
               - "Unsigned .app"
               - "Signed .ipa"
               - "Signed .ipa + App Store Connect submission"
         fast_exit:
            description: "Terminar early en pruebas"
            required: true
            default: false
            type: boolean

jobs:
   build-ios:
      runs-on: macos-latest
      timeout-minutes: 120

      steps:
         - name: Checkout repository
           uses: actions/checkout@v3

         # Crear keychain y añadir certificado y clave privada
         - name: Create Keychain and Import Certificate (Debug Mode)
           run: |
              # Mostrar longitudes de las variables para confirmar que contienen datos.
              echo "MACOS_KEYCHAIN_PASSWORD length: ${#MACOS_KEYCHAIN_PASSWORD}"
              echo "APPLE_DEVELOPER_CERTIFICATE length: ${#APPLE_DEVELOPER_CERTIFICATE}"
              echo "APPLE_DEVELOPER_CERTIFICATE_PRIVATE_KEY length: ${#APPLE_DEVELOPER_CERTIFICATE_PRIVATE_KEY}"
              echo "APPLE_DEVELOPER_CERTIFICATE_PASSWORD length: ${#APPLE_DEVELOPER_CERTIFICATE_PASSWORD}"

              echo "Creando keychain..."
              security create-keychain -p "$MACOS_KEYCHAIN_PASSWORD" build.keychain
              security default-keychain -s build.keychain
              security unlock-keychain -p "$MACOS_KEYCHAIN_PASSWORD" build.keychain

              # Debugging approach for certificate
              echo "Decodificando certificado (.cer)..."
              # Check if the variable isn't empty
              if [ -z "$APPLE_DEVELOPER_CERTIFICATE" ]; then
                echo "Error: APPLE_DEVELOPER_CERTIFICATE is empty"
                exit 1
              fi

              # Print the first few characters to verify content (but not expose the whole thing)
              #echo "Certificate preview: ${APPLE_DEVELOPER_CERTIFICATE:0:20}..."

              # For PEM-formatted certificate
              echo "Processing certificate (.cer)..."
              echo "$APPLE_DEVELOPER_CERTIFICATE" > certificate.pem

              # Verify the certificate file was created
              if [ -s certificate.pem ]; then
                echo "Certificate successfully saved"
                # Quick verification of the certificate
                openssl x509 -in certificate.pem -text -noout | head -n 5
              else
                echo "Failed to save certificate or certificate is empty"
                exit 1
              fi

              # Para la clave privada
              echo "Procesando clave privada (.p12)..."
              echo "$APPLE_DEVELOPER_CERTIFICATE_PRIVATE_KEY" > encrypted_private_key.pem

              # Intentamos descifrar la clave privada primero
              echo "Descifrando clave privada..."
              openssl rsa -in encrypted_private_key.pem -out decrypted_private_key.pem -passin pass:"$APPLE_DEVELOPER_CERTIFICATE_PASSWORD"

              # Verificar que el archivo descifrado se creó correctamente
              if [ -s decrypted_private_key.pem ]; then
                echo "Private key successfully decrypted"
              else
                echo "Failed to decrypt private key - check password"
                exit 1
              fi

              # Convertir la clave privada PEM a formato P12 para importar en keychain
              echo "Convirtiendo clave privada a formato P12..."
              openssl pkcs12 -export -out apple_development.p12 -inkey decrypted_private_key.pem -in certificate.pem -password pass:"$APPLE_DEVELOPER_CERTIFICATE_PASSWORD"

              echo "Importando certificado en el keychain..."
              security import certificate.pem -k build.keychain -T /usr/bin/codesign

              echo "Importando clave privada en el keychain..."
              security import apple_development.p12 -k build.keychain -P "$APPLE_DEVELOPER_CERTIFICATE_PASSWORD" -T /usr/bin/codesign

              echo "Configurando permisos para codesign..."
              security set-key-partition-list -S apple-tool:,apple: -s -k "$MACOS_KEYCHAIN_PASSWORD" build.keychain
           env:
              MACOS_KEYCHAIN_PASSWORD: ${{ secrets.MACOS_KEYCHAIN_PASSWORD }}
              APPLE_DEVELOPER_CERTIFICATE: ${{ secrets.APPLE_DEVELOPER_CERTIFICATE }}
              APPLE_DEVELOPER_CERTIFICATE_PRIVATE_KEY: ${{ secrets.APPLE_DEVELOPER_CERTIFICATE_PRIVATE_KEY }}
              APPLE_DEVELOPER_CERTIFICATE_PASSWORD: ${{ secrets.APPLE_DEVELOPER_CERTIFICATE_PASSWORD }}

         - name: Install Apple Provisioning Profile
           env:
              PROVISIONING_PROFILE_BASE64: ${{ secrets.APPLE_PROVISIONING_PROFILE_BASE64 }}
              PROVISIONING_PROFILE_UUID: ${{ secrets.APPLE_PROVISIONING_PROFILE_UUID }}
           run: |
              # Create variables
              PP_PATH=$RUNNER_TEMP/profile.mobileprovision

              # Import provisioning profile from secrets
              echo -n "$PROVISIONING_PROFILE_BASE64" | base64 --decode --output $PP_PATH

              # Make directories if they don't exist
              mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles

              # Copy provisioning profile with UUID as filename
              cp $PP_PATH ~/Library/MobileDevice/Provisioning\ Profiles/${PROVISIONING_PROFILE_UUID}.mobileprovision

         - name: Show Keychain Content (Debug)
           run: |
              security find-identity -v -p codesigning /Users/<USER>/Library/Keychains/build.keychain-db
              security list-keychains

         - name: List Certificates
           run: |
              security find-identity -v -p codesigning

         - name: Set up Flutter
           if: ${{ github.event.inputs.fast_exit == 'false' }}
           uses: subosito/flutter-action@v2
           with:
              flutter-version: "3.29.0"
              channel: "stable"
              cache: true

         - name: Setup Ruby
           if: ${{ github.event.inputs.fast_exit == 'false' }}
           uses: ruby/setup-ruby@v1
           with:
              ruby-version: "3.0"
              bundler-cache: true

         - name: Create .env.test file
           if: ${{ github.event.inputs.fast_exit == 'false' }}
           run: |
              cd mobileapp
              echo "${{ secrets.TEST_FLUTTER_DOT_ENV }}" > .env
              echo "${{ secrets.TEST_FLUTTER_DOT_ENV }}" > .env.test

         - name: Get Flutter packages
           if: ${{ github.event.inputs.fast_exit == 'false' }}
           run: |
              cd mobileapp
              flutter pub get

         - name: Install CocoaPods
           if: ${{ github.event.inputs.fast_exit == 'false' }}
           run: |
              cd mobileapp
              gem install cocoapods
              find . -name "Podfile" -execdir pod install \;

         - name: Flutter analyze
           if: ${{ github.event.inputs.fast_exit == 'false' }}
           run: |
              cd mobileapp
              # flutter analyze --no-fatal-infos || true
           continue-on-error: true

         - name: Flutter unit tests
           if: ${{ github.event.inputs.fast_exit == 'false' }}
           run: |
              cd mobileapp
              # flutter test
           continue-on-error: true

         - name: Create .env file for production
           if: ${{ github.event.inputs.fast_exit == 'false' }}
           run: |
              cd mobileapp
              echo "${{ secrets.PRODUCTION_FLUTTER_DOT_ENV }}" > .env

         - name: Create export_options.plist
           if: ${{ github.event.inputs.fast_exit == 'false' }}
           env:
              TEAM_ID: ${{ secrets.APPLE_TEAM_ID }}
              BUNDLE_ID: ${{ secrets.APPLE_BUNDLE_ID }}
              EXPORT_METHOD: ${{ github.event.inputs.export_method }}
           run: |
              cd mobileapp
              cat > export_options.plist << EOF
              <?xml version="1.0" encoding="UTF-8"?>
              <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
               <plist version="1.0">
               <dict>
                  <key>method</key>
                  <string>${EXPORT_METHOD}</string>
                  <key>teamID</key>
                  <string>${TEAM_ID}</string>
                  <key>uploadBitcode</key>
                  <false/>
                  <key>compileBitcode</key>
                  <false/>
                  <key>stripSwiftSymbols</key>
                  <true/>
                  <key>signingStyle</key>
                  <string>manual</string>
                  <key>provisioningProfiles</key>
                  <dict>
                     <key>${BUNDLE_ID}</key>
                     <string>${{ secrets.APPLE_PROVISIONING_PROFILE_UUID }}</string>
                  </dict>
                  <key>signingCertificate</key>
                  <string>Apple Distribution</string>
                  <key>generateAppStoreInformation</key>
                  <false/>
                  <key>validateEmbeddedProfile</key>
                  <false/>
               </dict>
               </plist>
              EOF
              cp export_options.plist ~/export_options.plist

         - name: Build iOS Simulator (.app file)
           if: ${{ github.event.inputs.fast_exit == 'false' }}
           run: |
              cd mobileapp
              flutter build ios --simulator --no-codesign

         - name: Compress .app file
           if: ${{ github.event.inputs.fast_exit == 'false' }}
           run: |
              cd mobileapp/build/ios/iphonesimulator
              zip -r Runner.zip Runner.app

         - name: Upload iOS Simulator artifact
           if: ${{ github.event.inputs.fast_exit == 'false' }}
           uses: actions/upload-artifact@v4
           with:
              name: mia-app-ios-simulator
              path: mobileapp/build/ios/iphonesimulator/Runner.zip
              retention-days: 1

         - name: Build iOS Unsigned app (.app file)
           if: ${{ github.event.inputs.fast_exit == 'false' }}
           run: |
              cd mobileapp
              flutter build ios --release --no-codesign

         - name: Upload iOS Unsigned app artifact
           if: ${{ github.event.inputs.fast_exit == 'false' }}
           uses: actions/upload-artifact@v4
           with:
              name: mia-unsigned-app-ios
              path: mobileapp/build/ios/iphoneos/Runner.app
              retention-days: 1

         - name: Remove Flutter framework signatures
           if: ${{ (github.event.inputs.salida != 'Unsigned .app') && (github.event.inputs.fast_exit == 'false') }}
           run: |
              cd mobileapp
              find build/ios/iphoneos/Runner.app/Frameworks/App.framework -type f -exec codesign --remove-signature {} \;

         - name: Prepare Podfile for signing
           if: ${{ (github.event.inputs.salida != 'Unsigned .app') && (github.event.inputs.fast_exit == 'false') }}
           run: |
              cd mobileapp/ios
              # Eliminar el bloque post_install existente
              sed -i '' '/post_install do |installer|/,/^end/d' Podfile
              # Agregar un nuevo bloque post_install con ambas configuraciones
              cat >> Podfile << 'EOF'
              post_install do |installer|
                installer.pods_project.targets.each do |target|
                  flutter_additional_ios_build_settings(target)
                  target.build_configurations.each do |config|
                    # Desactivar la firma manual para los pods
                    config.build_settings['CODE_SIGNING_REQUIRED'] = "NO"
                    config.build_settings['CODE_SIGNING_ALLOWED'] = "NO"
                    config.build_settings['EXPANDED_CODE_SIGN_IDENTITY'] = ""
                    config.build_settings['CODE_SIGNING_IDENTITY'] = ""
                    config.build_settings['PROVISIONING_PROFILE_SPECIFIER'] = ""
                    
                    # Asegurar que la versión mínima de iOS sea 12.0
                    config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'

                    # Habilitar permisos necesarios según permission_handler pub.dev
                     config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)',
                     'PERMISSION_EVENTS=0',                # calendario :contentReference[oaicite:7]{index=7}
                     'PERMISSION_REMINDERS=0',             # recordatorios :contentReference[oaicite:8]{index=8}
                     'PERMISSION_CONTACTS=0',              # contactos :contentReference[oaicite:9]{index=9}
                     'PERMISSION_CAMERA=0',                # cámara :contentReference[oaicite:10]{index=10}
                     'PERMISSION_MICROPHONE=0',            # micrófono :contentReference[oaicite:11]{index=11}
                     'PERMISSION_PHOTOS=0',                # fotos :contentReference[oaicite:12]{index=12}
                     'PERMISSION_LOCATION=0',              # ubicación (genérico) :contentReference[oaicite:13]{index=13}
                     'PERMISSION_LOCATION_WHENINUSE=1',    # solo en uso :contentReference[oaicite:14]{index=14}
                     'PERMISSION_NOTIFICATIONS=0',          # notificaciones :contentReference[oaicite:15]{index=15}
                     ]
                  end
                end
              end
              EOF
              # Reinstalar pods con el Podfile actualizado
              pod install

         - name: Build and sign IPA
           if: ${{ (github.event.inputs.salida != 'Unsigned .app') && (github.event.inputs.fast_exit == 'false') }}
           env:
              BUNDLE_ID: ${{ secrets.APPLE_BUNDLE_ID }}
              TEAM_ID: ${{ secrets.APPLE_TEAM_ID }}
              EXPORT_METHOD: ${{ github.event.inputs.export_method }}
              PROVISIONING_PROFILE_UUID: ${{ secrets.APPLE_PROVISIONING_PROFILE_UUID }}
           run: |
              cd mobileapp/ios

              # Crear un archivo xcconfig para aplicar la configuración solo al target Runner
              cat > Runner.xcconfig << EOF
              DEVELOPMENT_TEAM = $TEAM_ID
              CODE_SIGN_IDENTITY = Apple Distribution
              CODE_SIGN_STYLE = Manual
              PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*] = $PROVISIONING_PROFILE_UUID
              EOF

              # Build with xcodebuild specifying the configuration file
              xcodebuild -workspace Runner.xcworkspace -scheme Runner -configuration Release \
              -archivePath $RUNNER_TEMP/Runner.xcarchive \
              -allowProvisioningUpdates \
              -xcconfig Runner.xcconfig \
              OTHER_CODE_SIGN_FLAGS="--keychain $RUNNER_TEMP/app-signing.keychain-db" \
              archive

              xcodebuild -exportArchive \
              -archivePath $RUNNER_TEMP/Runner.xcarchive \
              -exportOptionsPlist ~/export_options.plist \
              -exportPath $RUNNER_TEMP/build/ios/ipa

         - name: Upload IPA artifact
           if: ${{ (github.event.inputs.salida != 'Unsigned .app') && (github.event.inputs.fast_exit == 'false') }}
           uses: actions/upload-artifact@v4
           with:
              name: mia-app-ipa-file
              path: ${{ runner.temp }}/build/ios/ipa/*.ipa
              retention-days: 5

         #  - name: Upload xcodebuild logs
         #    if: ${{ github.event.inputs.salida != 'Unsigned .app' }}
         #    uses: actions/upload-artifact@v4
         #    with:
         #       name: xcodebuild-logs
         #       path: /tmp/xcodebuild_logs/*.log
         #       retention-days: 1
         #       if-no-files-found: ignore

         #  - name: Upload Flutter drive logs
         #    if: ${{ github.event.inputs.salida != 'Unsigned .app' }}
         #    uses: actions/upload-artifact@v4
         #    with:
         #       name: flutter-drive-logs
         #       path: mobileapp/flutter_drive.log
         #       retention-days: 1
         #       if-no-files-found: ignore

         # - name: Deploy to TestFlight
         #   if: ${{ (github.event.inputs.salida == 'Signed .ipa + App Store Connect submission') && (github.event.inputs.export_method == 'app-store') && (success()) && (github.event.inputs.fast_exit == 'false') }}
         #   env:
         #     APP_STORE_CONNECT_KEY_ID: ${{ secrets.APP_STORE_CONNECT_KEY_ID }}
         #     APP_STORE_CONNECT_ISSUER_ID: ${{ secrets.APP_STORE_CONNECT_ISSUER_ID }}
         #     APP_STORE_CONNECT_KEY_CONTENT: ${{ secrets.APP_STORE_CONNECT_KEY_CONTENT }}
         #     APP_STORE_APPLE_ID: ${{ secrets.ENV_APP_STORE_APPLE_ID }}
         #   run: |
         #     # Crear archivo temporal para la clave API
         #     APP_STORE_KEY_PATH=$RUNNER_TEMP/app_store_connect_key.p8
         #     echo -n "$APP_STORE_CONNECT_KEY_CONTENT" > $APP_STORE_KEY_PATH
         #
         #     # Localizar el archivo IPA
         #     IPA_PATH=$(find $RUNNER_TEMP/build/ios/ipa -name "*.ipa" | head -n 1)
         #
         #     # Subir usando altool (parte de Xcode)
         #     xcrun altool --upload-app --type ios \
         #       --file "$IPA_PATH" \
         #       --apiKey "$APP_STORE_CONNECT_KEY_ID" \
         #       --apiIssuer "$APP_STORE_CONNECT_ISSUER_ID" \
         #       --api-key-path "$APP_STORE_KEY_PATH"
