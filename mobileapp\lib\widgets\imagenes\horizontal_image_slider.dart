import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:mia/models/media.dart';

class HorizontalImageSlider extends StatefulWidget {
  final List<Media> mediaList;
  final double height;
  final double spacing;
  final BorderRadius borderRadius;
  final Function(Media)? onTap;
  final Function(int)? onPageChanged;
  final double aspectRatio;

  const HorizontalImageSlider({
    super.key,
    required this.mediaList,
    this.height = 200.0,
    this.spacing = 8.0,
    this.borderRadius = const BorderRadius.all(Radius.circular(8.0)),
    this.onTap,
    this.onPageChanged,
    this.aspectRatio = 16 / 9,
  });

  @override
  State<HorizontalImageSlider> createState() => _HorizontalImageSliderState();
}

class _HorizontalImageSliderState extends State<HorizontalImageSlider> {
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    // Configurar el PageController con el viewportFraction adecuado según la cantidad de imágenes
    double viewportFraction;

    if (widget.mediaList.length == 1) {
      viewportFraction = 1.0; // Imagen completa si solo hay una
    } else if (widget.mediaList.length == 2) {
      viewportFraction =
          0.8; // Primera imagen completa, segunda parcialmente visible
    } else {
      viewportFraction =
          0.65; // Configuración para mostrar imagen actual y un poco de la siguiente
    }

    _pageController = PageController(
      viewportFraction: viewportFraction,
      initialPage: 0,
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.height,
      child: PageView.builder(
        controller: _pageController,
        itemCount: widget.mediaList.length,
        onPageChanged: widget.onPageChanged,
        padEnds: widget.mediaList.length == 1, // Solo centrar si hay una imagen
        pageSnapping: true,
        itemBuilder: (context, index) {
          return _buildMediaItem(widget.mediaList[index], index);
        },
      ),
    );
  }

  Widget _buildMediaItem(Media media, int index) {
    // Calcular el ancho basado en viewportFraction
    final screenWidth = MediaQuery.of(context).size.width;
    final itemWidth = screenWidth * _pageController.viewportFraction;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: widget.spacing / 2),
      width: itemWidth - widget.spacing,
      child: InkWell(
        onTap: widget.onTap != null ? () => widget.onTap!(media) : null,
        child: ClipRRect(
          borderRadius: widget.borderRadius,
          child: AspectRatio(
            aspectRatio: widget.aspectRatio,
            child: CachedNetworkImage(
              imageUrl: media.url,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: Colors.grey[200],
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
              errorWidget: (context, url, error) => Container(
                color: Colors.grey[300],
                child: const Center(
                  child: Icon(Icons.broken_image, color: Colors.grey),
                ),
              ),
              fadeInDuration: const Duration(milliseconds: 300),
              fadeOutDuration: const Duration(milliseconds: 300),
            ),
          ),
        ),
      ),
    );
  }
}
