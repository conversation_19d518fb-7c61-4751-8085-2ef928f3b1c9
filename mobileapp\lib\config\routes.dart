import 'package:flutter/material.dart';
import 'package:mia/models/categoria.dart';
import 'package:mia/models/evento.dart';
import 'package:mia/pages/categorias.dart';
import 'package:mia/pages/cerca_de_mi.dart';
import 'package:mia/pages/evento.dart';
import 'package:mia/pages/eventos.dart';
import 'package:mia/pages/favoritos.dart';
import 'package:mia/pages/negocio.dart';
import 'package:mia/models/negocio.dart';
import 'package:mia/pages/negocio_mapa.dart';
import 'package:mia/pages/negocios.dart';
import 'package:mia/pages/settings.dart';

final Map<String, WidgetBuilder> appRoutes = {
  '/categorias': (context) => const CategoriasPage(),
  '/negocio': (context) {
    final negocio = ModalRoute.of(context)!.settings.arguments as Negocio;
    return NegocioPage(negocio: negocio);
  },
  '/negocio-mapa': (context) {
    final negocio = ModalRoute.of(context)!.settings.arguments as Negocio;
    return NegocioMapaPage(negocio: negocio);
  },
  '/negocios': (context) {
    final categoria = ModalRoute.of(context)!.settings.arguments as Categoria;
    return NegociosPage(categoria: categoria);
  },
  '/eventos': (context) => const EventosPage(),
  '/evento': (context) {
    final evento = ModalRoute.of(context)!.settings.arguments as Evento;
    return EventoPage(evento: evento);
  },
  '/favoritos': (context) => const FavoritosPage(),
  '/cerca-de-mi': (context) => const CercaDeMiPage(),
  '/profile': (context) => const SettingsPage(),
};
