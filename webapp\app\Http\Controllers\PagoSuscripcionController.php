<?php

namespace App\Http\Controllers;

use App\Models\PagoSuscripcion;
use App\Http\Requests\StorePagoSuscripcionRequest;
use App\Http\Requests\UpdatePagoSuscripcionRequest;

class PagoSuscripcionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StorePagoSuscripcionRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(PagoSuscripcion $pagoSuscripcion)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(PagoSuscripcion $pagoSuscripcion)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdatePagoSuscripcionRequest $request, PagoSuscripcion $pagoSuscripcion)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PagoSuscripcion $pagoSuscripcion)
    {
        //
    }
}
