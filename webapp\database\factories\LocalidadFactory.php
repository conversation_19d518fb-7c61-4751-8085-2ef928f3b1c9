<?php

namespace Database\Factories;

use App\Models\Localidad;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Localidad>
 */
class LocalidadFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // crear unos limites con sentido
        $centro = [
            'latitud' => $this->faker->latitude($min = -80, $max = 80),
            'longitud' => $this->faker->longitude($min = -170, $max = 170),
        ];

        // limites de la localidad en un array
        $limites = [
            'latitud_min' => $centro['latitud'] - 0.1,
            'latitud_max' => $centro['latitud'] + 0.1,
            'longitud_min' => $centro['longitud'] - 0.1,
            'longitud_max' => $centro['longitud'] + 0.1,
        ];

        return [
            'nombre' => $this->faker->unique()->city,
            'ubicacion' => [
                'latitud' => $centro['latitud'],
                'longitud' => $centro['longitud'],
            ],
            'limites' => [
                'latitud_min' => $limites['latitud_min'],
                'latitud_max' => $limites['latitud_max'],
                'longitud_min' => $limites['longitud_min'],
                'longitud_max' => $limites['longitud_max'],
            ],
        ];
    }
}
