{"__meta": {"id": "01K6Z13FSPTSPR4SHJ19TDTZ18", "datetime": "2025-10-07 09:47:38", "utime": **********.187656, "method": "GET", "uri": "/mia/api/v1/categorias", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1759830454.601098, "end": **********.187689, "duration": 3.5865910053253174, "duration_str": "3.59s", "measures": [{"label": "Booting", "start": 1759830454.601098, "relative_start": 0, "end": **********.500739, "relative_end": **********.500739, "duration": 1.****************, "duration_str": "1.9s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.500766, "relative_start": 1.****************, "end": **********.187693, "relative_end": 4.0531158447265625e-06, "duration": 1.***************, "duration_str": "1.69s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.054296, "relative_start": 3.***************, "end": **********.06962, "relative_end": **********.06962, "duration": 0.015323877334594727, "duration_str": "15.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.158322, "relative_start": 3.****************, "end": **********.159924, "relative_end": **********.159924, "duration": 0.0016019344329833984, "duration_str": "1.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.159977, "relative_start": 3.****************, "end": **********.160046, "relative_end": **********.160046, "duration": 6.914138793945312e-05, "duration_str": "69μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "mia.test", "Timezone": "UTC", "Locale": "es"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.06475, "accumulated_duration_str": "64.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `cache` where `key` in ('categorias_all')", "type": "query", "params": [], "bindings": ["categorias_all"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 459}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}], "start": **********.083714, "duration": 0.04865, "duration_str": "48.65ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "proyecto-mba", "explain": null, "start_percent": 0, "width_percent": 75.135}, {"sql": "select * from `cache` where `key` in ('last_modified_app_models_categoria')", "type": "query", "params": [], "bindings": ["last_modified_app_models_categoria"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 18, "namespace": null, "name": "app/Services/CacheService.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Services\\CacheService.php", "line": 98}], "start": **********.139047, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "proyecto-mba", "explain": null, "start_percent": 75.135, "width_percent": 0.834}, {"sql": "select * from `sessions` where `id` = 'oL7BBVOo7Z89WfT5SlRFhMO3GDwB7bIsBTGsK8xa' limit 1", "type": "query", "params": [], "bindings": ["oL7BBVOo7Z89WfT5SlRFhMO3GDwB7bIsBTGsK8xa"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 21, "namespace": null, "name": "vendor/php-debugbar/php-debugbar/src/DebugBar/DebugBar.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DebugBar.php", "line": 446}], "start": **********.168664, "duration": 0.015560000000000001, "duration_str": "15.56ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "proyecto-mba", "explain": null, "start_percent": 75.969, "width_percent": 24.031}]}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "304 Not Modified", "full_url": "http://********/mia/api/v1/categorias", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\CategoriaController@index", "uri": "GET api/v1/categorias", "controller": "App\\Http\\Controllers\\Api\\CategoriaController@index<a href=\"phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FHttp%2FControllers%2FApi%2FCategoriaController.php&line=30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/v1/categorias", "file": "<a href=\"phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FHttp%2FControllers%2FApi%2FCategoriaController.php&line=30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/CategoriaController.php:30-63</a>", "middleware": "api", "duration": "3.15s", "peak_memory": "46MB", "response": "text/html", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-605415929 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-605415929\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1902925097 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1902925097\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-614805659 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Dart/3.5 (dart:io)</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>if-modified-since</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">Thu, 02 Oct 2025 17:55:35 +0000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">gzip</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">********</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-614805659\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1114583116 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1114583116\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-151207395 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">must-revalidate, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 07 Oct 2025 09:47:38 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-151207395\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1979303345 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0EN2mdDVoEig92vLCkjNCliX6PWYp2P8P2fcG83N</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1979303345\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "304 Not Modified", "full_url": "http://********/mia/api/v1/categorias", "controller_action": "App\\Http\\Controllers\\Api\\CategoriaController@index"}, "badge": "304 Not Modified"}}