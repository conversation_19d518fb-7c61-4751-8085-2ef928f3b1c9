import 'package:flutter/foundation.dart';

/// Servicio para depuración y visualización de datos
class DebugService {
  /// Muestra un número limitado de elementos con su estructura
  static void showLimitedElements<T>(
      List<T> elementos, int limit, String endpoint) {
    final int actualLimit = limit > elementos.length ? elementos.length : limit;

    debugPrint(
        "\n=== MUESTRA DE $actualLimit/${elementos.length} ELEMENTOS DE $endpoint ===");

    for (int i = 0; i < actualLimit; i++) {
      final element = elementos[i];
      debugPrint("\n--- Elemento ${i + 1} ---");

      // Mostrar contenido detallado según el tipo
      if (element is Map) {
        printMapStructure(element, indentLevel: 1);
      } else {
        // Intentar convertir a Map usando reflection o toString
        debugPrint(prettyPrint(element));
      }
    }

    debugPrint("\n=== FIN DE LA MUESTRA ===\n");
  }

  /// Imprime la estructura de un Map con formato e indentación
  static void printMapStructure(dynamic data, {int indentLevel = 0}) {
    final indent = '  ' * indentLevel;

    if (data is Map) {
      data.forEach((key, value) {
        if (value is Map || value is List) {
          debugPrint('$indent$key:');
          if (value is Map) {
            printMapStructure(value, indentLevel: indentLevel + 1);
          } else if (value is List) {
            printListStructure(value, indentLevel: indentLevel + 1);
          }
        } else {
          debugPrint('$indent$key: $value');
        }
      });
    }
  }

  /// Imprime la estructura de una List con formato e indentación
  static void printListStructure(List data, {int indentLevel = 0}) {
    final indent = '  ' * indentLevel;

    if (data.isEmpty) {
      debugPrint('$indent[]');
      return;
    }

    for (int i = 0; i < data.length; i++) {
      final item = data[i];
      if (item is Map || item is List) {
        debugPrint('$indent- Item $i:');
        if (item is Map) {
          printMapStructure(item, indentLevel: indentLevel + 1);
        } else if (item is List) {
          printListStructure(item, indentLevel: indentLevel + 1);
        }
      } else {
        debugPrint('$indent- Item $i: $item');
      }

      // Limitar la cantidad de elementos mostrados si la lista es grande
      if (data.length > 5 && i == 2) {
        debugPrint('$indent... (${data.length - 3} elementos más)');
        break;
      }
    }
  }

  /// Convierte cualquier objeto a una representación en texto con formato
  static String prettyPrint(dynamic obj) {
    try {
      // Intentar convertir el objeto a un mapa para una mejor visualización
      final String representation = obj.toString();
      return representation;
    } catch (e) {
      return obj.toString();
    }
  }
}
