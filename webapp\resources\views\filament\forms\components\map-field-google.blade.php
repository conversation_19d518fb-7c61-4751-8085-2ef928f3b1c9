{{-- resources/views/filament/forms/components/map-field-google.blade.php --}}
<x-dynamic-component 
    :component="$getFieldWrapperView()" 
    :field="$field"
>
    @php
        // Estado inicial de lat/long
        $state = $getState() ?? ['latitud' => '', 'longitud' => ''];
        $initialLat = $state['latitud'] ?: '';
        $initialLng = $state['longitud'] ?: '';
        $apiKey = config('app.features.google_maps.api_key');
    @endphp

    {{-- Carga el SDK de Google Maps si no está cargado --}}
    <script 
      src="https://maps.googleapis.com/maps/api/js?key={{ $apiKey }}&libraries=places"
      async defer
    ></script>

    <div 
        class="space-y-4" 
        x-data="googleMapComponent({ lat: '{{ $initialLat }}', lng: '{{ $initialLng }}' })" 
        x-init="initMap()"
    >
        {{-- Encabezado --}}
        <div class="flex items-center gap-2">
            <x-heroicon-o-map-pin class="w-5 h-5 text-primary-500" />
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
                  x-text="(
                    latitude && longitude 
                      ? `Ubicación: ${latitude}, ${longitude}` 
                      : 'Selecciona una ubicación'
                  )"></span>
        </div>

        {{-- Botón para pegar coordenadas --}}
        <div class="flex items-center gap-2">
            <button type="button"
                    class="inline-flex items-center px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 rounded"
                    @click="showInput = !showInput">
                📋 Pegar coordenadas
            </button>
        </div>

        {{-- Input de coordenadas --}}
        <div x-show="showInput" class="mt-2 flex items-center gap-2">
            <input
                type="text"
                x-model="coordInput"
                placeholder="lat, lng"
                class="border rounded px-2 py-1 w-48 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
            />
            <button
                type="button"
                class="px-3 py-1 bg-primary-500 text-white rounded"
                @click="applyCoordinates"
            >
                Aplicar
            </button>
        </div>

        {{-- Contenedor del mapa de satélite --}}
        <div class="border rounded shadow bg-gray-100 dark:bg-gray-700 w-full h-72 relative" wire:ignore>
            <div id="map-{{ $getStatePath() }}" class="w-full h-full rounded" style="height:400px;"></div>
        </div>
    </div>

    <script>
    function googleMapComponent(initial) {
        return {
            latitude: initial.lat,
            longitude: initial.lng,
            map: null,
            marker: null,
            showInput: false,
            coordInput: '',

            initMap() {
                const defaultLat = 36.9990019;
                const defaultLng = -6.5478919;
                const lat = parseFloat(this.latitude) || defaultLat;
                const lng = parseFloat(this.longitude) || defaultLng;

                // Inicializar mapa Google Maps en modo satélite
                this.map = new google.maps.Map(
                    document.getElementById('map-{{ $getStatePath() }}'),
                    {
                        center: { lat, lng },
                        zoom: 15,
                        mapTypeId: google.maps.MapTypeId.SATELLITE,
                        disableDefaultUI: false,
                    }
                );

                // Marcador draggable
                this.marker = new google.maps.Marker({
                    position: { lat, lng },
                    map: this.map,
                    draggable: true,
                });

                // Click para posicionar marcador
                this.map.addListener('click', (e) => {
                    this._updatePosition(e.latLng.lat(), e.latLng.lng());
                });

                // Drag del marcador
                this.marker.addListener('dragend', () => {
                    const pos = this.marker.getPosition();
                    this._updatePosition(pos.lat(), pos.lng());
                });
            },

            _updatePosition(lat, lng) {
                this.latitude = lat.toFixed(6);
                this.longitude = lng.toFixed(6);
                @this.set('{{ $getStatePath() }}.latitud', this.latitude);
                @this.set('{{ $getStatePath() }}.longitud', this.longitude);
                this.marker.setPosition({ lat, lng });
            },

            applyCoordinates() {
                const parts = this.coordInput.split(',');
                const lat = parseFloat(parts[0]?.trim());
                const lng = parseFloat(parts[1]?.trim());
                if (!isNaN(lat) && !isNaN(lng)) {
                    this.map.panTo({ lat, lng });
                    this._updatePosition(lat, lng);
                    this.showInput = false;
                    this.coordInput = '';
                } else {
                    alert('Formato inválido. Usa: lat, lng');
                }
            },
        }
    }
    </script>
</x-dynamic-component>