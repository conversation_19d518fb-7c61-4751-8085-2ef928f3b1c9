// lib/config/distance_options.dart

enum DistanceOption {
  oneKm,
  twoPointFiveKm,
  fiveKm,
  fifteenKm,
  thirtyKm,
  fiftyKm,
  none, // “Sin filtro”
}

extension DistanceOptionExt on DistanceOption {
  /// El valor en km que se guardará en prefs
  double get km {
    switch (this) {
      case DistanceOption.oneKm:
        return 1.0;
      case DistanceOption.twoPointFiveKm:
        return 2.5;
      case DistanceOption.fiveKm:
        return 5.0;
      case DistanceOption.fifteenKm:
        return 15.0;
      case DistanceOption.thirtyKm:
        return 30.0;
      case DistanceOption.fiftyKm:
        return 50.0;
      case DistanceOption.none:
        return 0.0;
    }
  }

  /// Etiqueta para mostrar en la UI
  String get label {
    if (this == DistanceOption.none) return 'Sin filtro';
    // convertir 2.5 → “2,5 km”, enteros → “5 km”
    final v = km;
    if (v == v.roundToDouble()) {
      return '${v.toInt()} km';
    }
    return '${v.toStringAsFixed(1).replaceAll('.', ',')} km';
  }
}
