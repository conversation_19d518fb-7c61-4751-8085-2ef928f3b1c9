# Enums del Backend

## Índice
- [EstadoPago](#estadopago)
- [MetodoPago](#metodopago)
- [EstadoSuscripcion](#estadosuscripcion)
- [TipoSuscripcion](#tiposuscripcion)

---

## EstadoPago

**Archivo**: `app/Enums/EstadoPago.php`

### Descripción
Define los estados posibles de un pago de suscripción.

### Valores

```php
enum EstadoPago: string
{
    case PENDIENTE = 'pendiente';
    case COMPLETADO = 'completado';
    case RECHAZADO = 'rechazado';
}
```

### Métodos

#### message(): string
Devuelve un mensaje descriptivo del estado.

```php
public function message(): string
{
    return match ($this) {
        self::PENDIENTE => 'El pago está pendiente de procesamiento',
        self::COMPLETADO => 'El pago ha sido completado exitosamente',
        self::RECHAZADO => 'El pago ha sido rechazado',
    };
}
```

#### label(): string
Devuelve una etiqueta corta para mostrar en UI.

```php
public function label(): string
{
    return match ($this) {
        self::PENDIENTE => 'Pendiente',
        self::COMPLETADO => 'Completado',
        self::RECHAZADO => 'Rechazado',
    };
}
```

#### color(): string
Devuelve un color para badges en Filament.

```php
public function color(): string
{
    return match ($this) {
        self::PENDIENTE => 'warning',
        self::COMPLETADO => 'success',
        self::RECHAZADO => 'danger',
    };
}
```

#### getAsOptions(): array
Devuelve un array para usar en selects.

```php
public static function getAsOptions(): array
{
    $options = [];
    foreach (self::cases() as $case) {
        $options[$case->value] = $case->label();
    }
    return $options;
}
// Resultado: ['pendiente' => 'Pendiente', 'completado' => 'Completado', ...]
```

### Uso en Modelos

```php
// En PagoSuscripcion.php
protected $casts = [
    'estado' => EstadoPago::class,
];

// Uso
$pago->estado = EstadoPago::COMPLETADO;
$pago->estado->label(); // "Completado"
$pago->estado->color(); // "success"
```

### Uso en Validación

```php
// En Form Request
use Illuminate\Validation\Rule;

public function rules(): array
{
    return [
        'estado' => ['nullable', Rule::enum(EstadoPago::class)],
    ];
}
```

### Uso en Filament

```php
// En Resource
Forms\Components\Select::make('estado')
    ->options(EstadoPago::getAsOptions())
    ->placeholder('Seleccionar estado'),

// En tabla
Tables\Columns\TextColumn::make('estado')
    ->badge()
    ->formatStateUsing(fn(?EstadoPago $state): string => $state?->label() ?? 'Sin estado')
    ->color(fn(?EstadoPago $state): string => $state?->color() ?? 'gray'),
```

---

## MetodoPago

**Archivo**: `app/Enums/MetodoPago.php`

### Descripción
Define los métodos de pago disponibles.

### Valores

```php
enum MetodoPago: string
{
    case EFECTIVO = 'efectivo';
    case BIZUM = 'bizum';
    case TRANSFERENCIA = 'transferencia';
}
```

### Métodos

#### message(): string
```php
public function message(): string
{
    return match ($this) {
        self::BIZUM => 'Pago realizado mediante Bizum',
        self::TRANSFERENCIA => 'Pago realizado mediante transferencia bancaria',
        self::EFECTIVO => 'Pago realizado en efectivo',
    };
}
```

#### label(): string
```php
public function label(): string
{
    return match ($this) {
        self::BIZUM => 'Bizum',
        self::TRANSFERENCIA => 'Transferencia',
        self::EFECTIVO => 'Efectivo',
    };
}
```

#### icon(): string
Devuelve un icono de Heroicons para usar en Filament.

```php
public function icon(): string
{
    return match ($this) {
        self::BIZUM => 'heroicon-o-device-phone-mobile',
        self::TRANSFERENCIA => 'heroicon-o-building-library',
        self::EFECTIVO => 'heroicon-o-banknotes',
    };
}
```

#### getAsOptions(): array
```php
public static function getAsOptions(): array
{
    $options = [];
    foreach (self::cases() as $case) {
        $options[$case->value] = $case->label();
    }
    return $options;
}
```

### Uso en Modelos

```php
// En PagoSuscripcion.php
protected $casts = [
    'metodo_pago' => MetodoPago::class,
];

// Uso
$pago->metodo_pago = MetodoPago::BIZUM;
$pago->metodo_pago->label(); // "Bizum"
$pago->metodo_pago->icon();  // "heroicon-o-device-phone-mobile"
```

### Uso en Filament

```php
// En formulario
Forms\Components\Select::make('metodo_pago')
    ->options(MetodoPago::getAsOptions())
    ->required(),

// En tabla
Tables\Columns\TextColumn::make('metodo_pago')
    ->badge()
    ->formatStateUsing(fn(MetodoPago $state): string => $state->label())
    ->icon(fn(MetodoPago $state): string => $state->icon()),
```

---

## EstadoSuscripcion

**Archivo**: `app/Enums/EstadoSuscripcion.php`

### Descripción
Define los estados de una suscripción.

### Valores

```php
enum EstadoSuscripcion: int
{
    case INACTIVE = 0;
    case ACTIVE = 1;
    // case CANCELLED = 2; // Comentado para futuro uso
}
```

**Nota**: Este enum usa `int` como tipo base, no `string`.

### Métodos

#### message(): string
```php
public function message(): string
{
    return match ($this) {
        self::INACTIVE => 'La suscripción está inactiva',
        self::ACTIVE => 'La suscripción está activa',
    };
}
```

#### label(): string
```php
public function label(): string
{
    return match ($this) {
        self::INACTIVE => 'Inactiva',
        self::ACTIVE => 'Activa',
    };
}
```

#### getAsOptions(): array
```php
public static function getAsOptions(): array
{
    $options = [];
    foreach (self::cases() as $case) {
        $options[$case->value] = $case->label();
    }
    return $options;
}
// Resultado: [0 => 'Inactiva', 1 => 'Activa']
```

### Uso en Modelos

```php
// En Suscripcion.php
protected $casts = [
    'status' => EstadoSuscripcion::class,
];

// Uso
$suscripcion->status = EstadoSuscripcion::ACTIVE;
$suscripcion->status->value; // 1
$suscripcion->status->label(); // "Activa"
```

### Uso en Queries

```php
// Scope en Suscripcion.php
public function scopeActivas($query)
{
    return $query->where('status', EstadoSuscripcion::ACTIVE->value);
}

// Uso
$suscripcionesActivas = Suscripcion::activas()->get();
```

---

## TipoSuscripcion

**Archivo**: `app/Enums/TipoSuscripcion.php`

### Descripción
Define los tipos de planes de suscripción disponibles.

### Valores

```php
enum TipoSuscripcion: string
{
    case FREE = 'free';
    case BASIC = 'basic';
    // case PREMIUM = 'premium';  // Comentado para futuro uso
    // case LIFETIME = 'lifetime'; // Comentado para futuro uso
}
```

### Métodos

#### message(): string
```php
public function message(): string
{
    return match ($this) {
        self::FREE => 'Suscripción gratuita',
        self::BASIC => 'Suscripción básica',
    };
}
```

#### label(): string
```php
public function label(): string
{
    return match ($this) {
        self::FREE => 'Gratis',
        self::BASIC => 'Básica',
    };
}
```

#### isBillable(): bool
Indica si el plan es facturable.

```php
public function isBillable(): bool
{
    return match ($this) {
        self::FREE => false,
        self::BASIC => true,
    };
}
```

#### price(): float
Devuelve el precio del plan.

```php
public function price(): float
{
    return match ($this) {
        self::FREE => 0.0,
        self::BASIC => 80.0,
    };
}
```

#### getAsOptions(): array
```php
public static function getAsOptions(): array
{
    $options = [];
    foreach (self::cases() as $case) {
        $options[$case->value] = $case->label();
    }
    return $options;
}
```

### Uso en Modelos

```php
// En Suscripcion.php
protected $casts = [
    'plan' => TipoSuscripcion::class,
];

// Uso
$suscripcion->plan = TipoSuscripcion::BASIC;
$suscripcion->plan->label();      // "Básica"
$suscripcion->plan->price();      // 80.0
$suscripcion->plan->isBillable(); // true
```

### Uso en Queries

```php
// Scope en Suscripcion.php
public function scopeFacturables($query)
{
    return $query->where('plan', '!=', TipoSuscripcion::FREE->value);
}

// Uso
$suscripcionesFacturables = Suscripcion::facturables()->get();
```

---

## Mejores Prácticas

### 1. Siempre usar enums en lugar de strings mágicos

❌ **Mal**:
```php
$pago->estado = 'completado';
if ($pago->estado === 'completado') { ... }
```

✅ **Bien**:
```php
$pago->estado = EstadoPago::COMPLETADO;
if ($pago->estado === EstadoPago::COMPLETADO) { ... }
```

### 2. Usar métodos helper para UI

```php
// En Blade
{{ $pago->estado->label() }}

// En Filament
->formatStateUsing(fn($state) => $state->label())
```

### 3. Validación con Rule::enum()

```php
use Illuminate\Validation\Rule;

public function rules(): array
{
    return [
        'estado' => ['required', Rule::enum(EstadoPago::class)],
        'metodo_pago' => ['required', Rule::enum(MetodoPago::class)],
    ];
}
```

### 4. Casting en modelos

```php
protected $casts = [
    'estado' => EstadoPago::class,
    'metodo_pago' => MetodoPago::class,
];
```

### 5. Comparaciones type-safe

```php
// Comparar con el enum, no con el valor
if ($pago->estado === EstadoPago::COMPLETADO) { ... }

// Si necesitas el valor
if ($pago->estado->value === 'completado') { ... }
```

## Añadir Nuevos Valores

Para añadir un nuevo valor a un enum:

1. Añadir el case:
```php
case NUEVO_VALOR = 'nuevo_valor';
```

2. Actualizar todos los métodos match:
```php
public function label(): string
{
    return match ($this) {
        self::PENDIENTE => 'Pendiente',
        self::COMPLETADO => 'Completado',
        self::RECHAZADO => 'Rechazado',
        self::NUEVO_VALOR => 'Nuevo Valor', // ← Añadir aquí
    };
}
```

3. Actualizar tests y documentación

