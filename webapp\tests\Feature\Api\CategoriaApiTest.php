<?php

namespace Tests\Feature\Api;

use Tests\TestCase;
use App\Models\User;
use App\Models\Categoria;
use Spatie\Permission\Models\Role;
use PHPUnit\Framework\Attributes\Group;
use Database\Seeders\RolesPermissionSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\BaseTest;

#[Group('api')]
#[Group('categoria')]
class CategoriaApiTest extends BaseTest
{
    use RefreshDatabase;

    private array $usuarios;

    private string $api_slug = '/api/v1/categorias';

    // Configuración inicial para cada prueba
    protected function setUp(): void
    {
        parent::setUp();

        $this->seed(RolesPermissionSeeder::class);

        foreach (Role::all() as $rol) {
            $user = User::factory()->create();
            $user->assignRole($rol->name);

            $this->usuarios[$rol->name] = [
                'usuario' => $user,
                'token' => $user->createToken('auth_token_' . $rol->name)->plainTextToken,
            ];
        }
    }


    /**
     * Test that the list of categorias is publicly accessible.
     */
    public function test_el_listado_de_categorias_es_accesible_publicamente(): void
    {
        Categoria::factory(3)->create();
        $response = $this->getJson("{$this->api_slug}");
        $response->assertStatus(200)
            ->assertJsonCount(3);

        // $roles = Role::all();

        // foreach ($roles as $rol) {
        //     /** @var User */
        //     $user = $this->usuarios[$rol->name]['usuario'];
        //     $this->actingAs($user);

        //     $response = $this->getJson("{$this->api_slug}");
        //     if ($user->can('categoria.list')) {
        //         $response->assertStatus(200)
        //             ->assertJsonCount(3);
        //     } else {
        //         $response->assertStatus(403);
        //     }
        // }
    }


    /**
     * Test that a categoria can be viewed publicly.
     */
    public function test_mostrar_una_zona_es_accesible_publicamente(): void
    {
        $categoria = Categoria::factory()->create();
        $response = $this->getJson("{$this->api_slug}/{$categoria->id}");
        $response
            ->assertStatus(200)
            ->assertJson(['id' => $categoria->id]);

        // $roles = Role::all();

        // foreach ($roles as $rol) {
        //     /** @var User */
        //     $user = $this->usuarios[$rol->name]['usuario'];
        //     $this->actingAs($user);
        //     $response = $this->getJson("{$this->api_slug}/{$categoria->id}");
        //     if ($user->can('categoria.read')) {
        //         $response
        //             ->assertStatus(200)
        //             ->assertJson(['id' => $categoria->id]);
        //     } else {
        //         $response->assertStatus(403);
        //     }
        // }
    }


    /**
     * Test that only users with admin rol can create categorias.
     */
    public function test_solo_usuarios_con_permiso_pueden_crear_categorias(): void
    {
        // 🟢 Test positivos

        $test_roles = $this->_getRolesPositivos(['categoria.create']);

        foreach ($test_roles as $rol) {
            $this->actingAs($this->usuarios[$rol]['usuario']);;
            $data = Categoria::factory()->make()->toArray();

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->postJson("{$this->api_slug}", $data);


            $response->assertStatus(201)
                ->assertJsonStructure([
                    'id',
                    'nombre',
                    'descripcion',
                    'parent_id',
                    'visible',
                    'created_at',
                    'updated_at'
                ])
                ->assertJson([
                    'nombre' => $data['nombre'],
                    'descripcion' => $data['descripcion'],
                    'parent_id' => $data['parent_id'],
                    'visible' => $data['visible'],
                ]);

            $this->assertDatabaseHas('categorias', [
                'nombre' => $data['nombre'],
                'descripcion' => $data['descripcion'],
                'parent_id' => $data['parent_id'],
                'visible' => $data['visible'],
            ]);
        }


        // 🔴 Test Negativos

        $test_roles = $this->_getRolesNegativos(['categoria.create']);

        foreach ($test_roles as $rol) {
            $this->actingAs($this->usuarios[$rol]['usuario']);;
            $data = Categoria::factory()->make()->toArray();

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->postJson("{$this->api_slug}", $data);

            $response->assertStatus(403);
        }
    }

    public function test_el_sistema_no_crea_nueva_categoria_con_datos_inválidos(): void
    {
        // Casos de datos inválidos
        $testCases = [
            // Caso 1: Enviar datos vacíos
            [
                'payload' => [],
                'expectedInvalid' => ['nombre']
            ],

        ];

        $rol = $this->_getRolesPositivos(['categoria.create']);

        if (is_array($rol)) {
            $rol = $rol[0] ?? null;
        }

        if (!$rol) {
            return;
        }

        $token = $this->usuarios[$rol]['token'];

        foreach ($testCases as $case) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->postJson("{$this->api_slug}", $case['payload']);
        }

        if (isset($case['expectedForbidden']) && $case['expectedForbidden']) {
            $response->assertForbidden(); // Verificar acceso denegado
        } else {
            $response->assertInvalid($case['expectedInvalid']);
        }
    }

    /**
     * Test that only users with admin rol can update categorias.
     */
    public function test_solo_usuarios_con_permiso_pueden_editar_categorias(): void
    {
        $categoria = Categoria::factory()->create();

        // 🟢 Test positivos

        $test_roles = $this->_getRolesPositivos(['categoria.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];
            $token = $this->usuarios[$rol]['token'];

            $this->actingAs($user);

            $categoria->refresh();
            $data = Categoria::factory()->make()->toArray();

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->putJson("{$this->api_slug}/{$categoria->id}", $data);

            $response->assertStatus(200)
                ->assertJsonStructure([
                    'id',
                    'nombre',
                    'descripcion',
                    'parent_id',
                    'visible',
                    'created_at',
                    'updated_at'
                ])
                ->assertJson([
                    'nombre' => $data['nombre'],
                    'descripcion' => $data['descripcion'],
                    'parent_id' => $data['parent_id'],
                    'visible' => $data['visible'],
                ]);

            $this->assertDatabaseHas('categorias', [
                'id' => $categoria->id,
                'nombre' => $data['nombre'],
                'descripcion' => $data['descripcion'],
                'parent_id' => $data['parent_id'],
                'visible' => $data['visible'],
            ]);
        }

        // 🔴 Test negativos

        $test_roles = $this->_getRolesNegativos(['categoria.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];
            $token = $this->usuarios[$rol]['token'];

            $this->actingAs($user);

            $categoria = Categoria::factory()->create();
            $data = Categoria::factory()->make()->toArray();

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->putJson("{$this->api_slug}/{$categoria->id}", $data);

            $response->assertStatus(403);
        }
    }

    public function test_el_sistema_no_edita_una_zona_con_datos_invalidos(): void
    {
        $categoria = Categoria::factory()->create();
        $subcategoria = Categoria::factory()->create(['parent_id' => $categoria->id]);

        // Casos de datos inválidos
        $testCases = [
            // Caso 1: Enviar datos vacíos
            [
                'payload' => [],
                'expectedInvalid' => ['nombre']
            ],

            // Caso 2: Referencia circular
            [
                'payload' => [
                    'nombre' => $categoria->nombre,
                    'parent_id' => $subcategoria->id,
                ],
                'expectedInvalid' => ['parent_id']
            ],

        ];

        $rol = $this->_getRolesPositivos(['categoria.update']);

        if (is_array($rol)) {
            $rol = $rol[0] ?? null;
        }

        if (!$rol) {
            return;
        }

        $token = $this->usuarios[$rol]['token'];

        foreach ($testCases as $case) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])
                ->putJson("{$this->api_slug}/{$categoria->id}", $case['payload']);
        }

        if (isset($case['expectedForbidden']) && $case['expectedForbidden']) {
            $response->assertForbidden(); // Verificar acceso denegado
        } else {
            $response->assertInvalid($case['expectedInvalid']);
        }
    }

    /**
     * Test that only users with admin rol can delete zonas.
     */
    public function test_solo_los_usuarios_con_permiso_pueden_borrar_categorias(): void
    {
        // 🟢 Test positivos

        $test_roles = $this->_getRolesPositivos(['categoria.delete']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];
            $token = $this->usuarios[$rol]['token'];

            $this->actingAs($user);

            $categoria = Categoria::factory()->create();

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->deleteJson("{$this->api_slug}/{$categoria->id}");

            $response->assertNoContent();

            // Verificar que el negocio no existe en la base de datos
            $this->assertDatabaseMissing(Categoria::class, [
                'id' => $categoria->id,
            ]);
        }


        // 🔴 Test negativos

        $test_roles = $this->_getRolesNegativos(['categoria.delete']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];
            $token = $this->usuarios[$rol]['token'];

            $this->actingAs($user);

            $categoria = Categoria::factory()->create();

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->deleteJson("{$this->api_slug}/{$categoria->id}");

            $response->assertStatus(403);

            // Verificar que la categoría existe en la base de datos
            $this->assertDatabaseHas(Categoria::class, [
                'id' => $categoria->id,
            ]);
        }
    }
}
