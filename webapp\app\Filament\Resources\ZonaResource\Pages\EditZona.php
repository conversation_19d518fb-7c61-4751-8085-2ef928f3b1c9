<?php

namespace App\Filament\Resources\ZonaResource\Pages;

use App\Models\Zona;
use Filament\Actions;
use App\Models\Negocio;
use App\Services\CacheService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Model;
use App\Filament\Resources\ZonaResource;
use Filament\Resources\Pages\EditRecord;

class EditZona extends EditRecord
{
    protected static string $resource = ZonaResource::class;

    protected CacheService $cacheService;

    public function boot(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $record->update($data);

        $this->cacheService->invalidateCache(Zona::class, 'zonas_all');
        $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');

        return $record;
    }
}
