<?php

namespace App\Policies;

use App\Models\User;
use App\Services\DataGeneratorService;
use Spatie\Permission\Models\Permission;

class PermissionPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('system.permissions');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Permission $permission): bool
    {
        return $user->can('system.permissions');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('system.permissions');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Permission $permission): bool
    {
        $esenciales = DataGeneratorService::getPermisosEsenciales();
        return $user->can('system.permissions') && !in_array($permission->name, $esenciales);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Permission $permission): bool
    {
        $esenciales = DataGeneratorService::getPermisosEsenciales();
        return $user->can('system.permissions') && !in_array($permission->name, $esenciales);
    }
}
