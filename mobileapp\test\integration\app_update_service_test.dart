import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:mia/services/app_update_service.dart';
import 'package:mia/widgets/dialogos/dialogo_app_update.dart';

/// Fake http client que devuelve una respuesta simulada
class FakeHttpClient extends http.BaseClient {
  final int statusCode;
  final String responseBody;

  FakeHttpClient({
    required this.statusCode,
    required this.responseBody,
  });

  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) async {
    final stream = Stream<List<int>>.fromIterable([responseBody.codeUnits]);
    return http.StreamedResponse(stream, statusCode);
  }
}

/// Cliente HTTP que rastrea las peticiones realizadas
class TrackingHttpClient extends http.BaseClient {
  final http.Client inner;
  final void Function(http.BaseRequest request)? onSend;

  TrackingHttpClient({
    required this.inner,
    this.onSend,
  });

  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) {
    if (onSend != null) {
      onSend!(request);
    }
    return inner.send(request);
  }
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  // Guardamos el valor original para restaurarlo después
  final originalPlatform = debugDefaultTargetPlatformOverride;

  setUp(() {
    // Configuramos el entorno para cada test
    dotenv.testLoad(fileInput: '''
API_URL=https://fakeapi.com
''');
  });

  tearDown(() {
    // Aseguramos que se restaure la plataforma después de cada test
    debugDefaultTargetPlatformOverride = originalPlatform;
  });

  /// Función de ayuda para renderizar el AppUpdateService en un widget
  Future<void> pumpAppUpdateServiceWidget({
    required WidgetTester tester,
    required AppUpdateService service,
  }) async {
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              // Ejecución diferida para asegurar que el contexto esté listo
              WidgetsBinding.instance.addPostFrameCallback((_) {
                service.checkForUpdates(context);
              });
              return const Center(child: Text('Testing Updates'));
            },
          ),
        ),
      ),
    );

    // Damos tiempo para que se procese el frame inicial
    await tester.pump();

    // Damos tiempo para que se ejecute el callback y la llamada a la API
    await tester.pump(const Duration(milliseconds: 100));

    // Esperamos a que se completen todas las animaciones y diálogos
    await tester.pumpAndSettle();
  }

  group('Pruebas de actualización obligatoria', () {
    testWidgets(
        'Muestra diálogo con actualización obligatoria cuando build actual < minimum_build_allowed',
        (WidgetTester tester) async {
      // Simulamos respuesta de la API donde el build mínimo permitido es mayor que el actual
      final simulatedResponse = json.encode({
        "id": 2,
        "platform": "android",
        "latest_build": 3,
        "minimum_build_allowed": 2,
        "update_url": "https://update.url"
      });

      final fakeHttpClient = FakeHttpClient(
        statusCode: 200,
        responseBody: simulatedResponse,
      );

      // El build actual es 1, menor que minimum_build_allowed (2)
      final appUpdateService = AppUpdateService(
        httpClient: fakeHttpClient,
        getCurrentBuild: () async => 1,
        isAndroid: true,
        isIOS: false,
      );

      await pumpAppUpdateServiceWidget(
        tester: tester,
        service: appUpdateService,
      );

      // Verificamos que se muestra el diálogo como actualización obligatoria
      expect(find.byType(DialogoAppUpdate), findsOneWidget);
      expect(find.textContaining('obligatoria'), findsOneWidget);
      expect(find.text('Actualizar'), findsOneWidget);
      expect(find.text('Más tarde'),
          findsNothing); // No debería mostrar el botón "Más tarde"
    });

    testWidgets(
        'Muestra diálogo con actualización opcional cuando build actual < latest_build pero >= minimum_build',
        (WidgetTester tester) async {
      // Simulamos respuesta donde el build actual es mayor o igual al mínimo pero menor al último
      final simulatedResponse = json.encode({
        "id": 2,
        "platform": "android",
        "latest_build": 3,
        "minimum_build_allowed": 1,
        "update_url": "https://update.url"
      });

      final fakeHttpClient = FakeHttpClient(
        statusCode: 200,
        responseBody: simulatedResponse,
      );

      // El build actual es 2, mayor que minimum_build_allowed (1) pero menor que latest_build (3)
      final appUpdateService = AppUpdateService(
        httpClient: fakeHttpClient,
        getCurrentBuild: () async => 2,
        isAndroid: true,
        isIOS: false,
      );

      await pumpAppUpdateServiceWidget(
        tester: tester,
        service: appUpdateService,
      );

      // Verificamos que se muestra el diálogo como actualización opcional
      expect(find.byType(DialogoAppUpdate), findsOneWidget);
      expect(find.textContaining('obligatoria'),
          findsNothing); // No debe mencionar que es obligatoria
      expect(find.textContaining('nueva actualización disponible'),
          findsOneWidget);
      expect(find.text('Actualizar'), findsOneWidget);
      expect(find.text('Más tarde'),
          findsOneWidget); // Debe mostrar el botón "Más tarde"
    });
  });

  group('Pruebas de no actualización', () {
    testWidgets('No muestra diálogo cuando el build actual >= latest_build',
        (WidgetTester tester) async {
      // Simulamos respuesta donde el build actual es mayor o igual al último
      final simulatedResponse = json.encode({
        "id": 2,
        "platform": "android",
        "latest_build": 2,
        "minimum_build_allowed": 1,
        "update_url": "https://update.url"
      });

      final fakeHttpClient = FakeHttpClient(
        statusCode: 200,
        responseBody: simulatedResponse,
      );

      // El build actual es 3, mayor que latest_build (2)
      final appUpdateService = AppUpdateService(
        httpClient: fakeHttpClient,
        getCurrentBuild: () async => 3,
        isAndroid: true,
        isIOS: false,
      );

      await pumpAppUpdateServiceWidget(
        tester: tester,
        service: appUpdateService,
      );

      // Verificamos que NO se muestra el diálogo
      expect(find.byType(DialogoAppUpdate), findsNothing);
    });

    testWidgets(
        'No muestra diálogo cuando el build actual es igual a latest_build',
        (WidgetTester tester) async {
      final simulatedResponse = json.encode({
        "id": 2,
        "platform": "android",
        "latest_build": 2,
        "minimum_build_allowed": 1,
        "update_url": "https://update.url"
      });

      final fakeHttpClient = FakeHttpClient(
        statusCode: 200,
        responseBody: simulatedResponse,
      );

      // El build actual es 2, igual a latest_build (2)
      final appUpdateService = AppUpdateService(
        httpClient: fakeHttpClient,
        getCurrentBuild: () async => 2,
        isAndroid: true,
        isIOS: false,
      );

      await pumpAppUpdateServiceWidget(
        tester: tester,
        service: appUpdateService,
      );

      // Verificamos que NO se muestra el diálogo
      expect(find.byType(DialogoAppUpdate), findsNothing);
    });
  });

  group('Pruebas de error y respuesta inválida', () {
    testWidgets('No muestra diálogo cuando la API devuelve un error 500',
        (WidgetTester tester) async {
      final fakeHttpClient = FakeHttpClient(
        statusCode: 500, // Error del servidor
        responseBody: 'Server Error',
      );

      final appUpdateService = AppUpdateService(
        httpClient: fakeHttpClient,
        getCurrentBuild: () async => 1,
        isAndroid: true,
        isIOS: false,
      );

      await pumpAppUpdateServiceWidget(
        tester: tester,
        service: appUpdateService,
      );

      // Verificamos que NO se muestra el diálogo
      expect(find.byType(DialogoAppUpdate), findsNothing);
    });

    testWidgets(
        'No muestra diálogo cuando la API devuelve una respuesta con formato inválido',
        (WidgetTester tester) async {
      final fakeHttpClient = FakeHttpClient(
        statusCode: 200,
        responseBody: '{"id": 2, "platform": "android"}', // Falta latest_build
      );

      final appUpdateService = AppUpdateService(
        httpClient: fakeHttpClient,
        getCurrentBuild: () async => 1,
        isAndroid: true,
        isIOS: false,
      );

      await pumpAppUpdateServiceWidget(
        tester: tester,
        service: appUpdateService,
      );

      // Verificamos que NO se muestra el diálogo
      expect(find.byType(DialogoAppUpdate), findsNothing);
    });

    testWidgets(
        'No muestra diálogo cuando la API devuelve una respuesta con JSON inválido',
        (WidgetTester tester) async {
      final fakeHttpClient = FakeHttpClient(
        statusCode: 200,
        responseBody: 'Not a JSON', // JSON inválido
      );

      final appUpdateService = AppUpdateService(
        httpClient: fakeHttpClient,
        getCurrentBuild: () async => 1,
        isAndroid: true,
        isIOS: false,
      );

      await pumpAppUpdateServiceWidget(
        tester: tester,
        service: appUpdateService,
      );

      // Verificamos que NO se muestra el diálogo
      expect(find.byType(DialogoAppUpdate), findsNothing);
    });

    testWidgets('No muestra diálogo cuando falla getCurrentBuild()',
        (WidgetTester tester) async {
      final simulatedResponse = json.encode({
        "id": 2,
        "platform": "android",
        "latest_build": 2,
        "minimum_build_allowed": 1,
        "update_url": "https://update.url"
      });

      final fakeHttpClient = FakeHttpClient(
        statusCode: 200,
        responseBody: simulatedResponse,
      );

      // Simulamos un fallo al obtener el build actual
      Future<int> failingGetCurrentBuild() async {
        throw Exception('Error al obtener el build');
      }

      final appUpdateService = AppUpdateService(
        httpClient: fakeHttpClient,
        getCurrentBuild: failingGetCurrentBuild,
        isAndroid: true,
        isIOS: false,
      );

      await pumpAppUpdateServiceWidget(
        tester: tester,
        service: appUpdateService,
      );

      // Verificamos que NO se muestra el diálogo
      expect(find.byType(DialogoAppUpdate), findsNothing);
    });
  });

  group('Pruebas específicas de plataforma', () {
    testWidgets('Usa el endpoint correcto para Android',
        (WidgetTester tester) async {
      final simulatedResponse = json.encode({
        "latest_build": 2,
        "minimum_build_allowed": 1,
      });

      final fakeHttpClient = FakeHttpClient(
        statusCode: 200,
        responseBody: simulatedResponse,
      );

      Uri? requestedUri;
      final trackedClient = TrackingHttpClient(
        inner: fakeHttpClient,
        onSend: (request) {
          requestedUri = request.url;
        },
      );

      final appUpdateService = AppUpdateService(
        httpClient: trackedClient,
        getCurrentBuild: () async => 1,
        isAndroid: true,
        isIOS: false,
      );

      await pumpAppUpdateServiceWidget(
        tester: tester,
        service: appUpdateService,
      );

      // Verificamos la URL correcta para Android
      expect(requestedUri?.path, contains('/android-version'));
    });

    testWidgets('Usa el endpoint correcto para iOS',
        (WidgetTester tester) async {
      final simulatedResponse = json.encode({
        "latest_build": 2,
        "minimum_build_allowed": 1,
      });

      final fakeHttpClient = FakeHttpClient(
        statusCode: 200,
        responseBody: simulatedResponse,
      );

      Uri? requestedUri;
      final trackedClient = TrackingHttpClient(
        inner: fakeHttpClient,
        onSend: (request) {
          requestedUri = request.url;
        },
      );

      final appUpdateService = AppUpdateService(
        httpClient: trackedClient,
        getCurrentBuild: () async => 1,
        isAndroid: false,
        isIOS: true,
      );

      await pumpAppUpdateServiceWidget(
        tester: tester,
        service: appUpdateService,
      );

      // Verificamos la URL correcta para iOS
      expect(requestedUri?.path, contains('/ios-version'));
    });
  });
}
