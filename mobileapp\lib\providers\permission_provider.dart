// lib/providers/permission_provider.dart

import 'package:flutter/material.dart';
import 'package:mia/services/permission_service.dart';
import 'package:permission_handler/permission_handler.dart';

enum LocationPermissionStatus {
  unknown,
  granted,
  denied,
  permanentlyDenied,
}

class PermissionProvider extends ChangeNotifier {
  final PermissionService _permissionService;

  LocationPermissionStatus _status = LocationPermissionStatus.unknown;
  LocationPermissionStatus get status => _status;

  PermissionProvider({PermissionService? service})
      : _permissionService = service ?? PermissionService();

  Future<void> checkPermission() async {
    final st = await _permissionService.getPermissionStatus();

    if (st.isPermanentlyDenied) {
      _status = LocationPermissionStatus.permanentlyDenied;
    } else if (st.isGranted) {
      _status = LocationPermissionStatus.granted;
    } else {
      _status = LocationPermissionStatus.denied;
    }

    notifyListeners();
  }

  Future<void> requestPermission() async {
    // pide y recibe el estado exacto
    final st = await _permissionService.requestLocationPermissionStatus();
    if (st.isPermanentlyDenied) {
      _status = LocationPermissionStatus.permanentlyDenied;
    } else if (st.isGranted) {
      _status = LocationPermissionStatus.granted;
    } else {
      _status = LocationPermissionStatus.denied;
    }
    notifyListeners();
  }
}
