<?php

namespace App\Filament\Widgets;

use App\Enums\EstadoPago;
use App\Models\Suscripcion;
use Filament\Widgets\Widget;
use App\Enums\TipoSuscripcion;
use App\Models\PagoSuscripcion;
use App\Enums\EstadoSuscripcion;

class PaymentSummary extends Widget
{
    protected static string $view = 'filament.widgets.payment-summary';

    protected array|string|int $columnSpan = 'full';

    public $pagosPendientes;
    public $pagosCompletados;
    public $pagosRechazados;
    public $pagosTotales;

    public $suscripcionesSinPagosEnTemporadaActual;
    public $numSuscripcionesSinPagosEnTemporadaActual;
    public $importeDeSuscripcionesSinPagosEnTemporadaActual;

    public $importePendiente;
    public $importeCompletado;
    public $importeRechazado;
    public $importeTotal;
    public $importeEsperado;

    public function mount(): void
    {

        $this->pagosPendientes = PagoSuscripcion::enTemporadaActual()->pendientes()->count();
        $this->pagosCompletados = PagoSuscripcion::enTemporadaActual()->completados()->count();
        $this->pagosRechazados = PagoSuscripcion::where('estado', EstadoPago::RECHAZADO->value)->count();
        $this->pagosTotales = $this->pagosPendientes + $this->pagosCompletados;

        $this->suscripcionesSinPagosEnTemporadaActual = Suscripcion::facturables()->activas()->sinPagosEnTemporadaActual()->get();
        $this->numSuscripcionesSinPagosEnTemporadaActual = $this->suscripcionesSinPagosEnTemporadaActual->count();

        // Sumar sobre la colección ya obtenida
        $this->importeDeSuscripcionesSinPagosEnTemporadaActual = $this->suscripcionesSinPagosEnTemporadaActual->sum('precio');

        $this->importePendiente = PagoSuscripcion::enTemporadaActual()->pendientes()->sum('importe');
        $this->importeCompletado = PagoSuscripcion::enTemporadaActual()->completados()->sum('importe');
        $this->importeRechazado = PagoSuscripcion::where('estado', EstadoPago::RECHAZADO->value)->sum('importe');
        $this->importeTotal = $this->importePendiente + $this->importeCompletado;

        $this->importeEsperado = $this->importeTotal + $this->importeDeSuscripcionesSinPagosEnTemporadaActual;
    }

    protected static ?int $sort = 2;
}
