import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Pruebas de API', () {
    test('Ejemplo de test', () {
      // Cuerpo del test
    });
  });
}

// import 'package:flutter_test/flutter_test.dart';
// import 'package:flutter_dotenv/flutter_dotenv.dart';
// import 'package:http/http.dart' as http;
// import 'package:mia/models/categoria.dart';
// import 'package:mia/models/negocio.dart';
// import 'package:mia/models/zona.dart';
// import 'package:mia/services/api_service.dart';
// import 'package:mockito/mockito.dart';
// import 'package:mockito/annotations.dart';

// import 'api_test.mocks.dart';
// // Importa tus modelos y servicios aquí

// // Genera los mocks
// @GenerateMocks([http.Client])
// void main() {
//   group('API Tests', () {
//     late MockClient mockClient;

//     setUp(() async {
//       // mockClient = MockClient();
//       // await dotenv.load(fileName: ".env.test");
//     });

//     test('convertir respuesta JSON de categorías a modelo Categoria', () async {
//       // final jsonResponse = '''[
//       // {
//       //   "id": 1,
//       //   "nombre": "Restaurantes",
//       //   "descripcion": "Lugares para comer",
//       //   "parent_id": null,
//       //   "visible": true,
//       //   "iconos": [],
//       //   "created_at": "2024-01-01T00:00:00.000000Z",
//       //   "updated_at": "2024-01-01T00:00:00.000000Z"
//       // }]
//       // ''';

//       // when(mockClient.get(Uri.parse('${ApiService.getBaseUri()}categorias')))
//       //     .thenAnswer((_) async => http.Response(jsonResponse, 200));

//       // final apiService = ApiService(client: mockClient);
//       // var categoria = await apiService.getCategorias();

//       // expect(categoria.length, 1);
//       // expect(categoria.first, isA<Categoria>());
//       // expect(categoria.first.id, 1);
//       // expect(categoria.first.nombre, 'Restaurantes');
//       // expect(categoria.first.descripcion, 'Lugares para comer');
//       // expect(categoria.first.parentId, null);
//       // expect(categoria.first.iconos, []);
//     });

//     test('convertir respuesta JSON de negocios a modelo Negocio', () async {
//       // final jsonResponse = '''[
//       // {
//       //   "id": 1,
//       //   "nombre": "Mi Negocio",
//       //   "descripcion": "Descripción del negocio",
//       //   "direccion": "Calle Principal 123",
//       //   "ubicacion": {"latitud": "10.123", "longitud": "-84.123"},
//       //   "categoria_id": 1,
//       //   "zona_id": 1,
//       //   "user_id": 1,
//       //   "suscripcion": {
//       //     "status": "active",
//       //     "ends_at": "2024-12-31T23:59:59.000000Z"
//       //   }
//       // }]
//       // ''';

//       // when(mockClient.get(Uri.parse('${ApiService.getBaseUri()}negocios')))
//       //     .thenAnswer((_) async => http.Response(jsonResponse, 200));

//       // final apiService = ApiService(client: mockClient);
//       // final negocio = await apiService.getNegocios();

//       // expect(negocio.length, 1);
//       // expect(negocio.first, isA<Negocio>());
//       // expect(negocio.first.id, 1);
//       // expect(negocio.first.nombre, 'Mi Negocio');
//       // expect(negocio.first.direccion, 'Calle Principal 123');
//       // expect(negocio.first.ubicacion,
//       //     {'latitud': '10.123', 'longitud': '-84.123'});
//       // // expect(negocio.first.suscripcion, {'status': 'active'});
//     });

//     test('convertir respuesta JSON de zonas a modelo Zona', () async {
//       // final jsonResponse = '''[
//       // {
//       //   "id": 1,
//       //   "nombre": "Centro",
//       //   "descripcion": "Descripción de la zona"
//       // }]
//       // ''';

//       // when(mockClient.get(Uri.parse('${ApiService.getBaseUri()}zonas')))
//       //     .thenAnswer((_) async => http.Response(jsonResponse, 200));

//       // final apiService = ApiService(client: mockClient);
//       // final zona = await apiService.getZonas();

//       // expect(zona.length, 1);
//       // expect(zona.first, isA<Zona>());
//       // expect(zona.first.id, 1);
//       // expect(zona.first.nombre, 'Centro');
//       // expect(zona.first.descripcion, 'Descripción de la zona');
//     });

//     test('manejar errores de API correctamente', () async {
//       // when(mockClient.get(Uri.parse('tu-api-url/negocios/999'))).thenAnswer(
//       //     (_) async => http.Response('{"error": "No encontrado"}', 404));

//       // final apiService = ApiService(client: mockClient);

//       // expect(
//       //   () => apiService.getNegocio(999),
//       //   throwsA(isA<ApiException>()),
//       // );
//     });
//   });
// }
