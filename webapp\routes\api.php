<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\ZonaController;
use App\Http\Controllers\Api\EventoController;
use App\Http\Controllers\Api\NegocioController;
use App\Http\Controllers\Api\CategoriaController;
use App\Http\Controllers\Api\LocalidadController;
use App\Http\Controllers\Api\AppVersionController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');


// Autenticación
Route::post('/login', [AuthController::class, 'login']);
// Route::post('/register', [AuthController::class, 'register']);
Route::post('/logout', [AuthController::class, 'logout'])->middleware('auth:sanctum');


Route::prefix('v1')->group(function () {

    Route::get('/ios-version', [AppVersionController::class, 'getIosVersion']);
    Route::get('/android-version', [AppVersionController::class, 'getAndroidVersion']);

    Route::prefix('negocios')->group(function () {
        Route::get('/', [NegocioController::class, 'index']); // Público
        Route::get('/{negocio}', [NegocioController::class, 'show']); // Público

        // Requieren autenticación
        Route::middleware('auth:sanctum')->group(function () {
            Route::post('/', [NegocioController::class, 'store']);
            Route::put('/{negocio}', [NegocioController::class, 'update']);
            Route::delete('/{negocio}', [NegocioController::class, 'destroy']);
        });
    });


    Route::prefix('zonas')->group(function () {
        Route::get('/', [ZonaController::class, 'index']); // Público
        Route::get('/{zona}', [ZonaController::class, 'show']); // Público

        // Requieren autenticación
        Route::middleware('auth:sanctum')->group(function () {
            Route::post('/', [ZonaController::class, 'store']);
            Route::put('/{zona}', [ZonaController::class, 'update']);
            Route::delete('/{zona}', [ZonaController::class, 'destroy']);
        });
    });


    Route::prefix('categorias')->group(function () {
        Route::get('/', [CategoriaController::class, 'index']); // Público
        Route::get('/{categoria}', [CategoriaController::class, 'show']); // Público

        // Requieren autenticación
        Route::middleware('auth:sanctum')->group(function () {
            Route::post('/', [CategoriaController::class, 'store']);
            Route::put('/{categoria}', [CategoriaController::class, 'update']);
            Route::delete('/{categoria}', [CategoriaController::class, 'destroy']);
        });
    });


    Route::prefix('localidades')->group(function () {
        Route::get('/', [LocalidadController::class, 'index']); // Público
        Route::get('/{localidad}', [LocalidadController::class, 'show']); // Público

        // Requieren autenticación
        Route::middleware('auth:sanctum')->group(function () {
            Route::post('/', [LocalidadController::class, 'store']);
            Route::put('/{localidad}', [LocalidadController::class, 'update']);
            Route::delete('/{localidad}', [LocalidadController::class, 'destroy']);
        });
    });

    Route::prefix('eventos')->group(function () {
        Route::get('/', [EventoController::class, 'index']); // Público
        Route::get('/{evento}', [EventoController::class, 'show']); // Público

        // Requieren autenticación
        Route::middleware('auth:sanctum')->group(function () {
            Route::post('/', [EventoController::class, 'store']);
            Route::put('/{evento}', [EventoController::class, 'update']);
            Route::delete('/{evento}', [EventoController::class, 'destroy']);
        });
    });
});
