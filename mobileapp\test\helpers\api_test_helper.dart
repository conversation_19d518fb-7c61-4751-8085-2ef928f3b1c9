// import 'package:flutter_test/flutter_test.dart';
// import 'package:flutter_dotenv/flutter_dotenv.dart';
// import '../base_test.dart';

// /// Helper para tests de servicios API
// class ApiTestHelper {
//   /// Configura un test para un endpoint GET
//   static Future<void> testGetEndpoint({
//     required String endpoint,
//     required String mockResponse,
//     required Future<dynamic> Function() apiCall,
//     required void Function(dynamic) assertions,
//   }) async {
//     await BaseTest.initialize();
    
//     final baseUrl = dotenv.env['API_URL'] ?? '';
//     final fullUrl = '$baseUrl$endpoint';
    
//     BaseTest.mockHttpResponse(fullUrl, mockResponse, 200);
    
//     final result = await apiCall();
//     assertions(result);
//   }
  
//   /// Configura un test para un endpoint POST
//   static Future<void> testPostEndpoint({
//     required String endpoint,
//     required String mockResponse,
//     required Future<dynamic> Function() apiCall,
//     required void Function(dynamic) assertions,
//   }) async {
//     await BaseTest.initialize();
    
//     final baseUrl = dotenv.env['API_URL'] ?? '';
//     final fullUrl = '$baseUrl$endpoint';
    
//     BaseTest.mockHttpPostResponse(fullUrl, mockResponse, 200);
    
//     final result = await apiCall();
//     assertions(result);
//   }
  
//   /// Configura un test para un error de API
//   static Future<void> testApiError({
//     required String endpoint,
//     required int statusCode,
//     required Future<dynamic> Function() apiCall,
//   }) async {
//     await BaseTest.initialize();
    
//     final baseUrl = dotenv.env['API_URL'] ?? '';
//     final fullUrl = '$baseUrl$endpoint';
    
//     BaseTest.mockHttpResponse(fullUrl, 'Error', statusCode);
    
//     expect(apiCall(), throwsException);
//   }
// }