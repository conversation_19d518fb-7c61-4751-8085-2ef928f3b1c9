<?php

namespace App\Filament\Resources\CategoriaResource\Pages;

use App\Models\User;
use Filament\Actions;
use App\Models\Negocio;
use App\Models\Categoria;
use App\Services\CacheService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Validator;
use Filament\Resources\Pages\CreateRecord;
use App\Http\Requests\StoreCategoriaRequest;
use App\Filament\Resources\CategoriaResource;

class CreateCategoria extends CreateRecord
{
    protected static string $resource = CategoriaResource::class;

    protected CacheService $cacheService;

    public function boot(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Aquí puedes modificar los datos antes de crear el registro
        return $data;
    }

    protected function handleRecordCreation(array $data): Model
    {
        /** @var User */
        $user = auth()->guard('web')->user();

        // Verifica la autorización usando la lógica de StoreCategoriaRequest
        if (!$user->can('create', Categoria::class)) {
            abort(403, 'No tienes permiso para crear una categoría.');
        }

        // Validar datos usando las reglas de StoreCategoriaRequest
        $validator = Validator::make($data, (new StoreCategoriaRequest())->rules());
        $validatedData = $validator->validate();

        // Crea el registro
        $model = static::getModel()::create($validatedData);

        $this->cacheService->invalidateCache(Categoria::class, 'categorias_all');
        $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');

        return $model;
    }
}
