<?php

namespace Database\Factories;

use App\Enums\EstadoPago;
use App\Enums\MetodoPago;
use App\Models\Suscripcion;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PagoSuscripcion>
 */
class PagoSuscripcionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'suscripcion_id' => Suscripcion::factory(),
            'metodo_pago' => $this->faker->randomElement(MetodoPago::cases())->value,
            'importe' => $this->faker->randomFloat(2, 10, 100),
            'fecha_pago' => Carbon::now()->subDays(rand(1, 30)),
            'transaccion_id' => $this->faker->uuid(),
            'estado' => $this->faker->randomElement(EstadoPago::cases())->value,
        ];
    }
}
