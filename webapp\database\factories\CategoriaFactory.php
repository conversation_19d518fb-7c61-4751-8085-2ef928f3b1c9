<?php

namespace Database\Factories;

use App\Models\Categoria;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Categoria>
 */
class CategoriaFactory extends Factory
{
    protected $model = \App\Models\Categoria::class;

    public function definition(): array
    {
        return [
            'nombre' => $this->faker->unique()->word(),
            'descripcion' => $this->faker->sentence(),
            'parent_id' => null,
            'visible' => true,
        ];
    }

    public function conIcono()
    {
        return $this->afterCreating(function (Categoria $categoria) {
            if (app()->environment('testing') || app()->runningUnitTests()) {
                $svgContent = <<<SVG
                    <svg xmlns="http://www.w3.org/2000/svg" width="100" height="100">
                        <rect width="100" height="100" fill="gray" />
                    </svg>
                    SVG;

                $categoria->addMediaFromString($svgContent)
                    ->usingFileName($this->getIconoAleatorio())
                    ->toMediaCollection('iconos');

                return;
            }

            $categoria->addMedia(Storage::disk('default-images')->path('iconos_cat/' . $this->getIconoAleatorio()))
                ->preservingOriginal()
                ->toMediaCollection('iconos');
        });
    }

    private function getIconoAleatorio(): string
    {
        $iconos = array_unique([
            'chiringuito.svg',
            'restaurante.svg',
            'bar.svg',
            'tienda_ropa.svg',
            'tienda_playa.svg',
            'hostel.svg',
            'hotel.svg',
            'villas.svg',
        ]);
        return $this->faker->randomElement($iconos);
    }
}
