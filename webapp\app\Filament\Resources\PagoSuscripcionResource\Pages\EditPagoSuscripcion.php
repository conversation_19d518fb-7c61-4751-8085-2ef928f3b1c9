<?php

namespace App\Filament\Resources\PagoSuscripcionResource\Pages;

use Filament\Actions;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Validator;
use App\Filament\Resources\PagoSuscripcionResource;
use App\Http\Requests\UpdatePagoSuscripcionRequest;

class EditPagoSuscripcion extends EditRecord
{
    protected static string $resource = PagoSuscripcionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $request = new UpdatePagoSuscripcionRequest();
        $validator = Validator::make($data, $request->rules(), $request->messages());
        try {
            $validatedData = $validator->validate();
            $this->record->update($validatedData);

            return $this->record;
        } catch (\Illuminate\Validation\ValidationException $e) {
            Notification::make()
                ->title('Error al actualizar el pago.')
                // Mostrar en el body una lista de los errores de validación a partir de UpdatePagoSuscripcionRequest::messages()
                ->body(function () use ($e) {
                    $errors = $e->validator->errors();
                    $errorMessages = [];
                    foreach ($errors->all() as $error) {
                        $errorMessages[] = $error;
                    }
                    return implode('<br>', $errorMessages);
                })
                ->danger()
                ->send();
            throw $e;
        }
    }
}
