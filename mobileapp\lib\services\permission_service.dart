// lib/services/permission_service.dart

import 'package:permission_handler/permission_handler.dart';

abstract class IPermissionHandler {
  Future<PermissionStatus> request();
  Future<PermissionStatus> status(); // ← ahora es método
}

class PermissionWrapper implements IPermissionHandler {
  final Permission _permission;
  PermissionWrapper(this._permission);

  @override
  Future<PermissionStatus> request() => _permission.request();

  @override
  Future<PermissionStatus> status() => _permission.status; // ← adaptado
}

class PermissionService {
  final IPermissionHandler _handler;
  PermissionService({IPermissionHandler? handler})
      : _handler = handler ?? PermissionWrapper(Permission.locationWhenInUse);

  Future<PermissionStatus> getPermissionStatus() {
    return _handler.status();
  }

  Future<bool> hasLocationPermission() async {
    final st = await _handler.status(); // ← llamamos al método
    return st.isGranted;
  }

  Future<bool> requestLocationPermission() async {
    final st = await _handler.request();
    return st.isGranted;
  }

  /// Devuelve el estado crudo para detectar deniedForever
  Future<PermissionStatus> requestLocationPermissionStatus() {
    return _handler.request();
  }
}
