import 'package:flutter_test/flutter_test.dart';
import 'package:mia/models/categoria.dart';
import 'package:mia/models/media.dart';
import '../../helpers/base_test_helper.dart';
import 'utils/model_test_utils.dart';

void main() async {
  TestWidgetsFlutterBinding.ensureInitialized();
  await BaseTest.initialize();

  group('Categoria Model Tests', () {
    ModelTestUtils.runCommonModelTests<Categoria>(
      validFixturePaths: [
        'categoria/valid_categoria_1.json',
        'categoria/valid_categoria_2.json',
      ],
      invalidFixturePaths: [
        'categoria/invalid_categoria_type.json',
        'categoria/invalid_categoria_missing.json',
      ],
      fromJson: Categoria.fromJson,
      toJson: (cat) => cat.toJson(),
      getExpectedValues: (fixturePath) =>
          _getCategoriaExpectedValues(fixturePath),
      customModel: {
        'id': 99,
        'nombre': 'Categoria de Prueba',
        'descripcion': 'Descripción de prueba',
        'parentId': null,
        'iconos': [
          Media.fromJson({
            'id': 1,
            'url': 'https://mia.test/storage/categorias/1/chiringuito.svg',
            'thumb':
                'https://mia.test/storage/categorias/1/conversions/chiringuito-thumb.jpg',
            'name': 'chiringuito'
          })
        ],
        'nivel': 1,
      },
      requiredFields: ['id', 'nombre'],
    );

    _runSpecificTests();
  });
}

void _runSpecificTests() {
  group('Funciones específicas de Categoria:', () {
    // test('find encuentra una categoría por su ID', () {
    //   final categorias = [
    //     Categoria(id: 1, nombre: 'Chiringuitos', nivel: 1),
    //     Categoria(id: 2, nombre: 'Restaurantes', nivel: 1),
    //   ];

    //   final categoriaEncontrada = Categoria.find(categorias, 1);
    //   expect(categoriaEncontrada, isNotNull);
    //   expect(categoriaEncontrada!.id, 1);
    // });
  });
}

Map<String, dynamic> _getCategoriaExpectedValues(String fixturePath) {
  return {
    'valid_categoria_1.json': {'id': 1, 'nombre': 'Chiringuitos', 'nivel': 1},
    'valid_categoria_2.json': {'id': 2, 'nombre': 'Restaurantes', 'nivel': 1}
  }[fixturePath.split('/').last]!;
}
