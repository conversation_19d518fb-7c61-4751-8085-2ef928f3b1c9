<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class Localidad extends Model
{
    use HasFactory;

    protected $table = 'localidades';

    protected $fillable = ['nombre', 'ubicacion', 'limites'];

    protected $casts = [
        'ubicacion' => 'array',
        'limites' => 'array',
    ];

    public function zonas(): HasMany
    {
        return $this->hasMany(Zona::class);
    }

    // HasManyThrough negocios a traves de zonas
    public function negocios(): HasManyThrough
    {
        return $this->hasManyThrough(Negocio::class, Zona::class);
    }

    public function setUbicacionAttribute($value)
    {
        // dd($value);
        $lat = (float) $value['latitud'];
        $lng = (float) $value['longitud'];

        // Fuerza, por ejemplo, 6 decimales en ambos
        $value['latitud'] = number_format($lat, 6, '.', '');
        $value['longitud'] = number_format($lng, 6, '.', '');

        $this->attributes['ubicacion'] = json_encode($value);
    }

    public function setLimitesAttribute($value)
    {
        $lat_min = (float) $value['latitud_min'];
        $lat_max = (float) $value['latitud_max'];
        $lng_min = (float) $value['longitud_min'];
        $lng_max = (float) $value['longitud_max'];

        // Fuerza, por ejemplo, 6 decimales en ambos
        $value['latitud_min'] = number_format($lat_min, 6, '.', '');
        $value['latitud_max'] = number_format($lat_max, 6, '.', '');
        $value['longitud_min'] = number_format($lng_min, 6, '.', '');
        $value['longitud_max'] = number_format($lng_max, 6, '.', '');

        $this->attributes['limites'] = json_encode($value);
    }
}
