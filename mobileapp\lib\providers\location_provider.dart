// lib/providers/location_provider.dart

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:latlong2/latlong.dart' as latlng;
import 'package:geolocator/geolocator.dart' as geo;
import 'package:mia/services/location_service.dart';

/// Estado posible de la obtención de la ubicación.
enum LocationStatus {
  initial, // aún no se ha intentado
  loading, // estamos pidiendo la ubicación
  success, // ya tenemos userLocation
  error, // hubo un error
}

/// Provider que expone la ubicación del usuario y soporta tracking continuo.
class LocationProvider extends ChangeNotifier {
  final LocationService _service;

  LocationProvider({LocationService? service})
      : _service = service ?? LocationService();

  LocationStatus _status = LocationStatus.initial;
  String? _errorMessage;
  latlng.LatLng? _userLocation;

  /// Subscripción al stream de Geolocator.
  StreamSubscription<geo.Position>? _positionSubscription;

  LocationStatus get status => _status;
  String? get errorMessage => _errorMessage;
  latlng.LatLng? get userLocation => _userLocation;

  /// Petición puntual (one‐shot).
  Future<void> fetchLocation() async {
    _status = LocationStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final pos = await _service.getCurrentPosition();
      _userLocation = latlng.LatLng(pos.latitude, pos.longitude);
      _status = LocationStatus.success;
    } on LocationServiceDisabledException catch (e) {
      _errorMessage = e.toString();
      _status = LocationStatus.error;
    } on geo.PermissionDeniedException catch (e) {
      _errorMessage = 'Permiso denegado por el usuario: $e';
      _status = LocationStatus.error;
    } catch (e) {
      _errorMessage = e.toString();
      _status = LocationStatus.error;
    }

    notifyListeners();
  }

  /// Inicia tracking continuo. Cada vez que el usuario se mueva más de [distanceFilter] metros,
  /// _userLocation y [status] se actualizarán automáticamente.
  void startTracking({int distanceFilter = 5}) {
    // Evita múltiples subscripciones
    if (_positionSubscription != null) return;

    _status = LocationStatus.loading;
    _errorMessage = null;
    notifyListeners();

    _positionSubscription = _service
        .getPositionStream(distanceFilter: distanceFilter)
        .listen((pos) {
      _userLocation = latlng.LatLng(pos.latitude, pos.longitude);
      _status = LocationStatus.success;
      notifyListeners();
    }, onError: (e) {
      _errorMessage = e.toString();
      _status = LocationStatus.error;
      notifyListeners();
    });
  }

  /// Detiene el tracking continuo para liberar recursos.
  void stopTracking() {
    _positionSubscription?.cancel();
    _positionSubscription = null;
  }

  @override
  void dispose() {
    // Asegura cancelar la subscripción al destruir el provider
    _positionSubscription?.cancel();
    super.dispose();
  }
}
