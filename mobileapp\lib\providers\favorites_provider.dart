import 'package:flutter/material.dart';
import 'package:mia/services/global_data_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FavoritesProvider with ChangeNotifier {
  static const _prefsKey = 'favoriteBusinesses';

  // Ahora guardamos un Set<int>
  Set<int> _favorites = {};

  FavoritesProvider() {
    _load();
  }

  Set<int> get favorites => _favorites;

  bool isFavorite(int id) => _favorites.contains(id);

  Future<void> toggleFavorite(int id) async {
    debugPrint('[FavoritesProvider] toggleFavorite BEFORE: $_favorites');
    if (_favorites.contains(id)) {
      _favorites.remove(id);
    } else {
      _favorites.add(id);
    }
    debugPrint('[FavoritesProvider] toggleFavorite AFTER: $_favorites');
    notifyListeners();

    // Convertimos a StringList al guardar
    final prefs = await SharedPreferences.getInstance();
    final stringList = _favorites.map((i) => i.toString()).toList();
    await prefs.setStringList(_prefsKey, stringList);
  }

  Future<void> _load() async {
    final prefs = await SharedPreferences.getInstance();
    final list = prefs.getStringList(_prefsKey) ?? [];
    debugPrint('[FavoritesProvider] _load raw list = $list');

    // Parseamos de String a int, ignorando valores no numéricos
    final loadedIds = list
        .map((s) {
          try {
            return int.parse(s);
          } catch (_) {
            return null;
          }
        })
        .whereType<int>()
        .toSet();

    debugPrint('[FavoritesProvider] _load parsed set = $_favorites');

    // Nuevo: filtrar sólo los IDs que existan en GlobalDataService().negocios
    final existingIds =
        GlobalDataService().negocios?.map((n) => n.id).toSet() ?? {};

    _favorites = loadedIds.intersection(existingIds);
    notifyListeners();

    // (opcional) si quieres limpiar SharedPreferences:
    final stringList = _favorites.map((i) => i.toString()).toList();
    await prefs.setStringList(_prefsKey, stringList);
  }
}
