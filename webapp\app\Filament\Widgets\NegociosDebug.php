<?php

namespace App\Filament\Widgets;

use Filament\Widgets\Widget;
use Illuminate\Support\Collection;
use Illuminate\Contracts\View\View;

class NegociosDebug extends Widget
{
    protected static string $view = 'filament.widgets.negocios-debug';

    protected array|string|int $columnSpan = 'full';

    public ?Collection $negocios = null;
    public array $filters = []; // Receive filters here

    public bool $_flag = false;

    protected $listeners = [
        'refreshNegocios' => 'refreshNegociosDebug',
    ];

    public function mount(?Collection $negocios = null, array $filters = []): void
    {
        $this->negocios = $negocios;
        $this->filters = $filters; // Initialize filters

        // dd($this->negocios, $this->filters);
    }

    public function render(): View
    {
        if ($this->_flag) {
            // $this->_flag = false;
            dd("RENDER despues de ejecutar funcion refreshNegociosDebug()", $this->getViewData());
        }
        return view(static::$view, $this->getViewData());
    }

    protected function getViewData(): array
    {
        return [
            'negocios' => $this->negocios,
            'filters' => $this->filters,
        ];
    }

    public function refreshNegociosDebug(array $params = [])
    {

        $this->negocios = collect($params['negocios'] ?? []);
        $this->filters = $params['filters'] ?? [];

        // dd($this->negocios, $this->filters);
        // $this->_flag = true;

        $this->dispatch('$refresh')->self();
        // $this->render();
    }
}
