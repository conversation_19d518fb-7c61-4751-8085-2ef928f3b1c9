import 'package:flutter/material.dart';
import 'package:mia/config/colors.dart';
import 'package:mia/config/styles.dart';

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final String type;

  const CustomButton(
      {super.key,
      required this.text,
      required this.onPressed,
      required this.type});

  @override
  Widget build(BuildContext context) {
    switch (type) {
      case "link":
        return TextButton(
          onPressed: onPressed,
          style: TextButton.styleFrom(
            foregroundColor: Colors.white, // Color del texto e icono
            padding: EdgeInsets.zero, // Elimina el padding interno
            minimumSize: Size(0, 0), // Evita espacio extra
            tapTargetSize:
                MaterialTapTargetSize.shrinkWrap, // Reduce área de toque
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min, // Ajusta al contenido
            children: [
              Icon(Icons.arrow_back, color: Colors.white),
              SizedBox(width: 8),
              Text(
                text,
                style: AppStyles.getStyle(context, 'h5',
                    fontWeight: 'bold', color: Colors.white),
              ),
            ],
          ),
        );
      case "ghost":
        return ElevatedButton(
          onPressed: onPressed,
          style: OutlinedButton.styleFrom(
            side: BorderSide(
                color: AppColors
                    .current.secondaryColor), // Borde de color secundario
            backgroundColor: Colors.transparent, // Fondo transparente
            padding: const EdgeInsets.symmetric(
                horizontal: 16, vertical: 8), // Espaciado interno
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8)), // Bordes redondeados
          ),
          child: Text(
            text,
            style: AppStyles.getStyle(context, 'base',
                fontWeight: 'bold', color: AppColors.current.secondaryColor),
          ),
        );
      default:
        return ElevatedButton(
          onPressed: onPressed,
          child: Text(
            text,
            style: AppStyles.getStyle(context, 'base'),
          ),
        );
    }
  }
}
