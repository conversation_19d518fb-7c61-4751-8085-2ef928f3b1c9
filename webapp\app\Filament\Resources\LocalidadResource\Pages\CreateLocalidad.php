<?php

namespace App\Filament\Resources\LocalidadResource\Pages;

use App\Models\Zona;
use Filament\Actions;
use App\Models\Negocio;
use App\Models\Localidad;
use App\Services\CacheService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\Pages\CreateRecord;
use App\Filament\Resources\LocalidadResource;

class CreateLocalidad extends CreateRecord
{
    protected static string $resource = LocalidadResource::class;

    public static bool $isEditMode = false;

    protected CacheService $cacheService;

    public function boot(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    public function getView(): string
    {
        return 'filament.pages.record-form-with-map';
    }

    public function isEdit(): bool
    {
        return false;
    }

    protected function handleRecordCreation(array $data): Model
    {
        $model = static::getModel()::create($data);

        // $this->cacheService->invalidateCache(Localidad::class, 'localidades_all');
        // $this->cacheService->invalidateCache(Zona::class, 'zonas_all');
        // $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');

        return $model;
    }
}
