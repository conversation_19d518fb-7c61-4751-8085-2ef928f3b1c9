// test/models/negocio_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mia/models/negocio.dart';
import 'package:mia/models/categoria.dart';
import '../../helpers/base_test_helper.dart';
import 'utils/model_test_utils.dart';
import 'package:intl/date_symbol_data_local.dart';

void main() async {
  TestWidgetsFlutterBinding.ensureInitialized();
  await BaseTest.initialize();

  group('Negocio Model Tests', () {
    ModelTestUtils.runCommonModelTests<Negocio>(
      validFixturePaths: [
        'negocio/valid_negocio_completo.json',
        'negocio/valid_negocio_minimo.json',
        'negocio/valid_negocio_parcial.json',
      ],
      invalidFixturePaths: [
        'negocio/invalid_negocio_missing_id.json',
        'negocio/invalid_negocio_missing_nombre.json',
        'negocio/invalid_negocio_missing_direccion.json',
        'negocio/invalid_negocio_missing_contacto.json',
        'negocio/invalid_negocio_missing_suscripcion.json',
        'negocio/invalid_negocio_id_type.json',
        'negocio/invalid_negocio_suscripcion_type.json',
      ],
      fromJson: Negocio.fromJson,
      toJson: (negocio) => negocio.toJson(),
      getExpectedValues: (fixturePath) => _getExpectedValues(fixturePath),
      customModel: {
        'id': 99,
        'nombre': 'Negocio de Prueba',
        'direccion': 'Calle de Test, 99',
        'contacto': '+34699999999',
        'suscripcion': {
          'id': 99,
          'negocio_id': 99,
          'plan': 'test',
          'status': 1,
          'started_at': '2025-01-01',
          'ends_at': '2025-12-31'
        }
      },
      requiredFields: ['id', 'nombre', 'direccion', 'contacto', 'suscripcion'],
    );

    _runSpecificTests();
  });
}

Future<void> _runSpecificTests() async {
  group('Funciones específicas de Negocio:', () {
    final mockHorario = {
      'lunes': ['09:00-14:00', '17:00-21:00'],
      'martes': ['09:00-18:00', '19:00-21:00', '22:00-23:00', '23:00-03:00'],
      'miércoles': ['18:00-04:00'],
      'jueves': ['09:00-18:00'],
      'viernes': ['09:00-12:30', '17:00-21:00'],
      'sábado': ['09:00-18:00'],
      'domingo': []
    };

    Negocio createNegocioWithSchedule(Map<String, List<String>>? schedule) {
      return Negocio(
          id: 1,
          nombre: 'Negocio de Prueba',
          direccion: 'Calle de Test, 99',
          contacto: '+34699999999',
          suscripcion: {
            'id': 99,
            'negocio_id': 99,
            'plan': 'test',
            'status': 1,
            'started_at': '2025-01-01',
            'ends_at': '2025-12-31'
          },
          horario: schedule,
          categorias: [
            Categoria.fromJson({
              'id': 1,
              'nombre': 'Categoria de Prueba',
              'descripcion': 'Descripción de prueba',
              'parent_id': null,
              'nivel': 1,
              'iconos': []
            })
          ]);
    }

    group('Estado Abierto/Cerrado:', () {
      // Inicializamos la localización para 'es_ES' antes de ejecutar estos tests
      setUpAll(() async {
        await initializeDateFormatting('es_ES', null);
      });

      test('Horario null devuelve cerrado', () {
        final negocio = createNegocioWithSchedule(null);
        expect(
            negocio.isOpenNow(dateTime: DateTime(2024, 1, 15, 12, 0)), false);
      });

      test('Horario vacío devuelve cerrado', () {
        final negocio = createNegocioWithSchedule({});
        expect(
            negocio.isOpenNow(dateTime: DateTime(2024, 1, 15, 12, 0)), false);
      });

      group('Con horario completo', () {
        final negocio = createNegocioWithSchedule(mockHorario.map(
          (key, value) =>
              MapEntry(key, value.map((e) => e.toString()).toList()),
        ));

        test('Lunes 12:00 - abierto (primer tramo)', () {
          expect(
              negocio.isOpenNow(dateTime: DateTime(2024, 1, 15, 12, 0)), true);
        });

        test('Martes 00:30 - cerrado (tramo nocturno)', () {
          expect(
              negocio.isOpenNow(dateTime: DateTime(2024, 1, 16, 0, 30)), false);
        });

        test('Miércoles 02:59 - abierto (tramo que cruza medianoche)', () {
          expect(
              negocio.isOpenNow(dateTime: DateTime(2024, 1, 17, 3, 0)), true);
        });

        test('Domingo 12:00 - cerrado', () {
          expect(
              negocio.isOpenNow(dateTime: DateTime(2024, 1, 21, 12, 0)), false);
        });

        test('Viernes 12:30 - cerrado (entre tramos)', () {
          expect(negocio.isOpenNow(dateTime: DateTime(2024, 1, 19, 12, 30)),
              false);
        });
      });

      test('Horario con formato inválido devuelve cerrado', () {
        final negocio = createNegocioWithSchedule({
          'lunes': ['25:00-30:00'],
          'martes': ['09:00'], // Formato incompleto
        });

        // Test en horario que debería ser válido pero con formato malo
        expect(
            negocio.isOpenNow(dateTime: DateTime(2024, 1, 15, 10, 0)), false);
      });

      test('Horario con hora exacta de cierre', () {
        final negocio = createNegocioWithSchedule({
          'jueves': ['09:00-18:00'],
        });

        // Justo a la hora de cierre (18:00) debería estar cerrado
        expect(
            negocio.isOpenNow(dateTime: DateTime(2024, 1, 18, 18, 0)), false);
      });

      test('Horario que termina a medianoche', () {
        final negocio = createNegocioWithSchedule({
          'viernes': ['20:00-24:00'],
        });

        expect(negocio.isOpenNow(dateTime: DateTime(2024, 1, 19, 23, 59, 59)),
            true);
        expect(
            negocio.isOpenNow(dateTime: DateTime(2024, 1, 20, 0, 0, 0)), false);
      });

      test(
          'Miércoles 03:01 - cerrado (después del cierre del martes, antes de apertura del miércoles)',
          () {
        final negocio = createNegocioWithSchedule({
          'martes': ['18:00-03:00'],
          'miércoles': ['18:00-04:00'],
        });
        expect(negocio.isOpenNow(dateTime: DateTime(2024, 1, 17, 3, 1)), false);
      });

      test(
          'Miércoles 03:00 - abierto (exactamente a la hora de cierre del martes)',
          () {
        final negocio = createNegocioWithSchedule({
          'martes': ['18:00-03:00'],
          'miércoles': ['18:00-04:00'],
        });
        expect(negocio.isOpenNow(dateTime: DateTime(2024, 1, 17, 3, 0)), true);
      });

      test('Miércoles 18:00 - abierto (hora de apertura del miércoles)', () {
        final negocio = createNegocioWithSchedule({
          'martes': ['18:00-03:00'],
          'miércoles': ['18:00-04:00'],
        });
        expect(negocio.isOpenNow(dateTime: DateTime(2024, 1, 17, 18, 0)), true);
      });

      test('Negocio con horario 24 horas', () {
        final negocio = createNegocioWithSchedule({
          'lunes': ['00:00-24:00'],
          'martes': ['00:00-24:00'],
          'miércoles': ['00:00-24:00'],
          'jueves': ['00:00-24:00'],
          'viernes': ['00:00-24:00'],
          'sábado': ['00:00-24:00'],
          'domingo': ['00:00-24:00'],
        });

        // Comprobar diferentes momentos del día/semana
        expect(negocio.isOpenNow(dateTime: DateTime(2024, 1, 15, 0, 0)),
            true); // Lunes 00:00
        expect(negocio.isOpenNow(dateTime: DateTime(2024, 1, 15, 12, 0)),
            true); // Lunes mediodía
        expect(negocio.isOpenNow(dateTime: DateTime(2024, 1, 15, 23, 59)),
            true); // Lunes final del día

        // Comprobar otro día de la semana
        expect(negocio.isOpenNow(dateTime: DateTime(2024, 1, 19, 3, 30)),
            true); // Viernes madrugada

        // Si el sistema interpreta 24:00 como final del día (no inicio del siguiente)
        expect(negocio.isOpenNow(dateTime: DateTime(2024, 1, 20, 0, 0)),
            true); // Sábado 00:00
      });

      test('Negocio con horario 24 horas formato alternativo', () {
        final negocio = createNegocioWithSchedule({
          'lunes': ['00:00-00:00'], // Formato alternativo para indicar 24h
        });

        expect(negocio.isOpenNow(dateTime: DateTime(2024, 1, 15, 0, 0)),
            true); // Inicio del día
        expect(negocio.isOpenNow(dateTime: DateTime(2024, 1, 15, 12, 0)),
            true); // Mediodía
        expect(negocio.isOpenNow(dateTime: DateTime(2024, 1, 15, 23, 59)),
            true); // Final del día
      });

      test('Horarios solapados', () {
        final negocio = createNegocioWithSchedule({
          'lunes': ['09:00-14:00', '13:00-18:00'],
        });

        // Caso de prueba: hora actual es 13:30
        // Se espera que getTramoHorario retorne el primer tramo ("09:00-14:00"), pues es el primer coincidente.
        DateTime testTime = DateTime(2024, 1, 15, 13, 30); // Un lunes

        // Llamada a getTramoHorario
        String? tramoActual = negocio.getTramoHorario(dateTime: testTime);

        // Assert
        expect(tramoActual, equals("09:00-14:00"),
            reason:
                "Para horarios solapados, se espera que se retorne el primer tramo coincidente.");
      });
    });
  });
}

Map<String, dynamic> _getExpectedValues(String fixturePath) {
  // Extraer el nombre del archivo del path completo
  final fileName = fixturePath.split('/').last;

  switch (fileName) {
    case 'valid_negocio_completo.json':
      return {
        'id': 1,
        'nombre': 'Bar La Esquina',
        'direccion': 'Calle Principal 123',
        'contacto': '+34612345678'
      };
    case 'valid_negocio_minimo.json':
      return {
        'id': 2,
        'nombre': 'Café Central',
        'direccion': 'Plaza Mayor 5',
        'contacto': '+34698765432'
      };
    case 'valid_negocio_parcial.json':
      return {
        'id': 3,
        'nombre': 'Restaurante El Mirador',
        'direccion': 'Avenida Principal 45',
        'contacto': '+34654321987'
      };
    default:
      throw Exception('Fixture de Negocio no definido: $fileName');
  }
}
