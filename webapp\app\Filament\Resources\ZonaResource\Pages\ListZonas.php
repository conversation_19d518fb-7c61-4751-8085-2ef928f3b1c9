<?php

namespace App\Filament\Resources\ZonaResource\Pages;

use App\Models\Zona;
use Filament\Actions;
use App\Models\Negocio;
use App\Services\CacheService;
use Filament\Tables\Actions\BulkAction;
use App\Filament\Resources\ZonaResource;
use Filament\Resources\Pages\ListRecords;

class ListZonas extends ListRecords
{
    protected static string $resource = ZonaResource::class;

    protected CacheService $cacheService;

    public function boot(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    protected function getTableBulkActions(): array
    {
        return [
            BulkAction::make('delete')
                ->label('Eliminar')
                ->action(function ($records) {
                    $records->each(function ($record) {
                        $record->delete();
                    });

                    $this->cacheService->invalidateCache(Zona::class, 'zonas_all');
                    $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');
                })
                ->requiresConfirmation(),
        ];
    }
}
