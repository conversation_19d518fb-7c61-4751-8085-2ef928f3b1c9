<?php

namespace App\Http\Controllers\Api;

use App\Models\Zona;
use App\Models\Negocio;
use Illuminate\Http\Request;
use App\Services\CacheService;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
use App\Http\Requests\StoreZonaRequest;
use App\Http\Requests\UpdateZonaRequest;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class ZonaController extends Controller
{
    use AuthorizesRequests;

    protected $cacheService;

    public function __construct(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // if (!$this->authorize('viewAny', Zona::class)) {
        //     return response()->json(['error' => 'No autorizado'], 403);
        // }

        $zonas = Cache::rememberForever('zonas_all', function () {
            return Zona::all();
        });

        return $this->cacheService->handleCacheableResponse(
            $zonas,
            function ($data) {
                return $data;
            }
        );
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        try {
            $zona = Zona::findOrFail($id);

            // if (!$this->authorize('view', $zona)) {
            //     return response()->json(['error' => 'No autorizado'], 403);
            // }

            return $this->cacheService->handleCacheableResponse(
                $zona,
                function ($data) {
                    return $data;
                }
            );
        } catch (\Exception $e) {
            return response()->json(['error' => 'Zona no encontrada'], 404);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreZonaRequest $request)
    {
        if (!$request->authorize()) {
            return response()->json(['error' => 'No autorizado'], 403);
        }

        $validated = $request->validated();
        $zona = Zona::create($validated);

        $this->cacheService->invalidateCache(Zona::class, 'zonas_all');
        $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');

        return response()->json($zona, 201);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateZonaRequest $request, Zona $zona)
    {
        if (!$request->authorize()) {
            return response()->json(['error' => 'No autorizado'], 403);
        }

        $validated = $request->validated();
        $zona->update($validated);

        $this->cacheService->invalidateCache(Zona::class, 'zonas_all');
        $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');

        return response()->json($zona);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            $zona = Zona::findOrFail($id);

            if (!$this->authorize('delete', $zona)) {
                return response()->json(['error' => 'No autorizado'], 403);
            }

            $zona->delete();

            $this->cacheService->invalidateCache(Zona::class, 'zonas_all');
            $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');

            return response()->noContent();
        } catch (ModelNotFoundException $e) {
            return response()->json(['error' => 'Zona no encontrada'], 404);
        }
    }
}
