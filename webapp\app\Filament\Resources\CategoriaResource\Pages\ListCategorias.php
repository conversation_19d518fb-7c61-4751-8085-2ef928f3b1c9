<?php

namespace App\Filament\Resources\CategoriaResource\Pages;

use Filament\Actions;
use App\Models\Negocio;
use App\Models\Categoria;
use App\Services\CacheService;
use Filament\Tables\Actions\BulkAction;
use Filament\Resources\Pages\ListRecords;
use App\Filament\Resources\CategoriaResource;

class ListCategorias extends ListRecords
{
    protected static string $resource = CategoriaResource::class;

    protected CacheService $cacheService;

    public function boot(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    protected function getTableBulkActions(): array
    {
        return [
            BulkAction::make('delete')
                ->label('Eliminar')
                ->action(function ($records) {
                    $records->each(function ($record) {
                        $record->delete();
                    });

                    $this->cacheService->invalidateCache(Categoria::class, 'categorias_all');
                    $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');
                })
                ->requiresConfirmation(),
        ];
    }

    public function reorderTable(array $order): void
    {
        parent::reorderTable($order);

        $this->cacheService->invalidateCache(Categoria::class, 'categorias_all');
    }
}
