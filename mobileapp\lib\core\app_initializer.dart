import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:intl/intl.dart';
import 'package:flutter/services.dart';

class AppInitializer {
  static Future<void> initialize() async {
    WidgetsFlutterBinding.ensureInitialized();
    await _loadEnvironment();
    await _configureLocale();
    await _configureOrientation();
  }

  static Future<void> _loadEnvironment() async {
    await dotenv.load();
  }

  static Future<void> _configureLocale() async {
    Intl.defaultLocale = 'es';
  }

  static Future<void> _configureOrientation() async {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }
}
