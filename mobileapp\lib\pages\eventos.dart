import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mia/config/colors.dart';
import 'package:mia/config/styles.dart';
import 'package:mia/models/evento.dart';
import 'package:provider/provider.dart';
import 'package:mia/providers/favorites_provider.dart';
// import 'package:mia/services/core_service.dart';
import 'package:mia/services/global_data_service.dart';
import 'package:mia/widgets/app_bottom_navigation_bar.dart';
import 'package:mia/widgets/app_scaffold.dart';
// import 'package:mia/widgets/lista_con_separador.dart';
import 'package:mia/widgets/modales/evento_modal.dart';

class EventosPage extends StatefulWidget {
  const EventosPage({super.key});

  @override
  State<EventosPage> createState() => _EventosPageState();
}

class _EventosPageState extends State<EventosPage> {
  List<Evento> eventos = [];

  @override
  void initState() {
    super.initState();
    // Carga inicial
    eventos = GlobalDataService().eventos ?? [];
    // Ordena incorporando favoritos
    _sortEventos();
  }

  void _sortEventos() {
    // Accede al provider de favoritos sin escuchar cambios
    final favProv = Provider.of<FavoritesProvider>(context, listen: false);

    eventos.sort((a, b) {
      // Comprueba si a o b pertenecen a un negocio favorito
      final na = a.getNegocio();
      final nb = b.getNegocio();
      final fa = na != null && favProv.isFavorite(na.id);
      final fb = nb != null && favProv.isFavorite(nb.id);

      // Si uno es favorito y el otro no, el favorito va primero
      if (fa && !fb) return -1;
      if (!fa && fb) return 1;

      // Si ambos son (o no son) favoritos, aplicar orden por fecha
      DateTime now = DateTime.now();
      DateTime ai = DateTime.parse(a.fechaInicio);
      DateTime bi = DateTime.parse(b.fechaInicio);
      DateTime af = DateTime.parse(a.fechaFin);
      DateTime bf = DateTime.parse(b.fechaFin);

      if (ai.isBefore(now) && bi.isBefore(now)) {
        return af.compareTo(bf);
      } else {
        return ai.compareTo(bi);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      // floatingActionButton: FloatingActionButton(
      //   onPressed: () {
      //     Navigator.pop(context);
      //   },
      //   child: const Icon(Icons.arrow_back),
      // ),
      bottomNavigationBar: const AppBottomNavigationBar(currentIndex: 3),
      child: eventos.isEmpty
          ? Center(
              child: Text(
                'No hay eventos',
                style: AppStyles.getStyle(
                  context,
                  'h5',
                  fontWeight: 'bold',
                  color: AppColors.current.secondaryColor,
                ),
              ),
            )
          : _buildSeccionEventos(eventos),
    );
  }

  Widget _buildSeccionEventos(List<Evento> eventos) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Título fijo
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 16.0),
          child: Text(
            'Próximos Eventos',
            style: AppStyles.getStyle(
              context,
              'h5',
              fontWeight: 'bold',
              color: AppColors.current.secondaryColor,
            ),
          ),
        ),
        // Lista que ocupa el resto de la pantalla y tiene fondo blanco
        Expanded(
          child: Container(
            decoration: BoxDecoration(color: AppColors.current.surfaceColor),
            padding: const EdgeInsets.all(8.0),
            child: _buildEventList(eventos),
          ),
        ),
      ],
    );
  }

  Widget _buildEventList(List<Evento> eventos) {
    return ListView.separated(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      itemCount: eventos.length,
      separatorBuilder: (context, index) => Divider(
        height: 1,
        color: AppColors.current.accentColor,
      ),
      itemBuilder: (context, index) {
        final evento = eventos[index];
        final negocio = evento.getNegocio();
        if (negocio == null) return Container();

        // Obtenemos el provider aquí para saber si es favorito
        final isFav = context.watch<FavoritesProvider>().isFavorite(negocio.id);

        return ListTile(
          leading: SizedBox(
            width: 50,
            height: 50,
            child: _getPrimeraImagenEvento(evento) ?? Container(),
          ),
          title: Text(
            evento.nombre,
            style: AppStyles.getStyle(context, 'h6', fontWeight: 'bold'),
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AutoSizeText(
                evento.getHorarioString(),
                minFontSize: 8,
                maxLines: 2,
                style:
                    AppStyles.getStyle(context, 'base', fontWeight: 'medium'),
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              // Aquí el Row con el corazón y el nombre
              Row(
                children: [
                  if (isFav)
                    Icon(
                      Icons.favorite,
                      size: 14,
                      color: AppColors.current.errorColor,
                    ),
                  if (isFav) const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      negocio.nombre,
                      style: AppStyles.getStyle(
                        context,
                        's',
                        fontWeight: 'medium',
                        color: AppColors.current.textColor,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],
          ),
          trailing: Icon(
            Icons.arrow_forward,
            size: 32,
            color: AppColors.current.textColor,
          ),
          onTap: () {
            mostrarEventoModal(context, evento);
          },
        );
      },
    );
  }

  Widget? _getPrimeraImagenEvento(Evento evento) {
    if (evento.imagenes != null && evento.imagenes!.isNotEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(10.0),
        child: CachedNetworkImage(
          imageUrl: evento.imagenes![0].url,
          width: 50,
          height: 50,
          fit: BoxFit.cover,
          placeholder: (context, url) => const Center(
            child: CircularProgressIndicator(),
          ),
          errorWidget: (context, url, error) => SvgPicture.asset(
            'assets/la_piedra.svg',
            width: 50,
            height: 50,
          ),
        ),
      );
    } else {
      return null;
    }
  }
}
