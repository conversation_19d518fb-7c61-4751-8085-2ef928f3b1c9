// lib/providers/theme_provider.dart

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:mia/config/colors.dart'; // contiene AppColors
import 'package:mia/config/theme_schemes.dart'; // contiene AppTheme & appThemeData

class ThemeProvider with ChangeNotifier {
  // Estado interno: el tema activo como enum
  AppTheme _currentTheme = AppTheme.claro;
  ThemeMode get themeMode =>
      _currentTheme == AppTheme.oscuro ? ThemeMode.dark : ThemeMode.light;
  AppTheme get currentTheme => _currentTheme;

  ThemeProvider() {
    _loadTheme();
  }

  AppColorScheme get scheme => AppColors.current;

  Future<void> _loadTheme() async {
    final prefs = await SharedPreferences.getInstance();
    final stored = prefs.getString('theme') ?? AppTheme.claro.name;
    // convierto String → AppTheme (por defecto claro)
    _currentTheme = AppTheme.values.firstWhere(
      (t) => t.name == stored,
      orElse: () => AppTheme.claro,
    );
    // actualizo esquema de colores global
    AppColors.setScheme(_currentTheme);
    notifyListeners();
  }

  Future<void> setTheme(AppTheme theme) async {
    _currentTheme = theme;
    // guardo elección
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('theme', theme.name);
    // actualizo esquema global
    AppColors.setScheme(theme);
    notifyListeners();
  }
}
