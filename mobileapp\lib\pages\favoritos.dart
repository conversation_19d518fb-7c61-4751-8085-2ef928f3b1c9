// lib/pages/favoritos_page.dart

import 'package:flutter/material.dart';
import 'package:mia/widgets/cards/card_negocio_fav.dart';
import 'package:provider/provider.dart';
import 'package:mia/widgets/app_bottom_navigation_bar.dart';
import 'package:mia/widgets/app_scaffold.dart';
import 'package:mia/providers/favorites_provider.dart';
import 'package:mia/services/global_data_service.dart';
import 'package:mia/models/negocio.dart';
import 'package:mia/models/categoria.dart';
import 'package:mia/config/colors.dart';
import 'package:mia/config/styles.dart';

class FavoritosPage extends StatefulWidget {
  const FavoritosPage({super.key});

  @override
  State<FavoritosPage> createState() => _FavoritosPageState();
}

class _FavoritosPageState extends State<FavoritosPage> {
  int? _selectedCategoryId; // null = todas

  @override
  void initState() {
    super.initState();
    // Añadir un post frame callback para verificar favoritos al iniciar la página
    WidgetsBinding.instance.addPostFrameCallback((_) => _checkEmptyFavorites());
  }

  // Verificar si la lista está vacía y navegar si es necesario
  void _checkEmptyFavorites() {
    final favProv = Provider.of<FavoritesProvider>(context, listen: false);
    if (favProv.favorites.isEmpty) {
      Navigator.pushReplacementNamed(context, '/categorias');
    }
  }

  @override
  Widget build(BuildContext context) {
    final favProv = context.watch<FavoritesProvider>();
    final favIds = favProv.favorites;
    final allNegocios = GlobalDataService().negocios ?? <Negocio>[];
    final favNegocios =
        allNegocios.where((n) => favIds.contains(n.id)).toList();

    // Si los favoritos están vacíos después de alguna acción, navegar a categorías
    if (favNegocios.isEmpty) {
      // Usar un post frame callback para evitar errores durante el build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.pushReplacementNamed(context, '/categorias');
      });
    }

    // Obtener todas las categorías de los negocios favoritos
    List<Categoria> allCats = [];
    for (var n in favNegocios) {
      if (n.categorias != null) {
        allCats.addAll(n.categorias!);
      }
    }
    // Únicas por id
    final uniqCats = {for (var c in allCats) c.id: c}.values.toList();

    // Filtrar negocios según categoría seleccionada
    List<Negocio> shownNegocios = (_selectedCategoryId == null)
        ? favNegocios
        : favNegocios
            .where((n) =>
                n.categorias?.any((c) => c.id == _selectedCategoryId) ?? false)
            .toList();

    // Si shownNegocios está vacío, cambiar a todas
    if (shownNegocios.isEmpty && _selectedCategoryId != null) {
      _selectedCategoryId = null;
      shownNegocios = favNegocios;
    }

    return AppScaffold(
      bottomNavigationBar: AppBottomNavigationBar(currentIndex: 2),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Título
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              'Favoritos',
              style: AppStyles.getStyle(
                context,
                'h4',
                fontWeight: 'bold',
                color: AppColors.current.secondaryColor,
              ),
            ),
          ),

          // Chips de filtro
          if (uniqCats.isNotEmpty)
            SizedBox(
              height: 40,
              child: ListView(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 12),
                children: [
                  Padding(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: ChoiceChip(
                      label: const Text('Todas'),
                      selected: _selectedCategoryId == null,
                      onSelected: (_) =>
                          setState(() => _selectedCategoryId = null),
                    ),
                  ),
                  ...uniqCats.map((cat) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: ChoiceChip(
                        label: Text(cat.nombre),
                        selected: _selectedCategoryId == cat.id,
                        onSelected: (_) =>
                            setState(() => _selectedCategoryId = cat.id),
                      ),
                    );
                  }),
                ],
              ),
            ),

          const SizedBox(height: 8),

          // Lista de favoritos (o mensaje si está vacía)
          Expanded(
            child: shownNegocios.isEmpty
                ? Center(
                    child: Text(
                      'Todavía no tienes favoritos',
                      style: AppStyles.getStyle(
                        context,
                        'base',
                        fontWeight: 'medium',
                        color: AppColors.current.textColor,
                      ),
                    ),
                  )
                : Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: ListView.separated(
                      itemCount: shownNegocios.length,
                      separatorBuilder: (_, __) => const SizedBox(height: 8),
                      itemBuilder: (context, index) {
                        return CardNegocioFav(negocio: shownNegocios[index]);
                      },
                    ),
                  ),
          ),
        ],
      ),
    );
  }
}
