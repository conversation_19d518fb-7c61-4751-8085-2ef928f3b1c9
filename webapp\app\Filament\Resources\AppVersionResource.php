<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use App\Models\AppVersion;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Illuminate\Support\Facades\Auth;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\AppVersionResource\Pages;
use App\Filament\Resources\AppVersionResource\RelationManagers;

class AppVersionResource extends Resource
{
    protected static ?string $model = AppVersion::class;

    protected static ?string $navigationGroup = 'Administración';
    protected static ?string $navigationIcon = 'heroicon-o-cpu-chip';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('platform')
                    ->required()
                    ->unique(ignoreRecord: true)
                    ->rules(['in:ios,android']),

                TextInput::make('latest_build')
                    ->required()
                    ->numeric()
                    ->rules(['integer', 'min:0']),

                TextInput::make('minimum_build_allowed')
                    ->required()
                    ->numeric()
                    ->rules(['integer', 'min:0']),

                TextInput::make('update_url')
                    ->url()
                    ->nullable()
                    ->dehydrateStateUsing(fn($state) => $state ?? ''),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('platform')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('latest_build')
                    ->sortable(),
                TextColumn::make('minimum_build_allowed')
                    ->sortable(),
                TextColumn::make('update_url'),
            ])
            ->filters([])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAppVersions::route('/'),
            'create' => Pages\CreateAppVersion::route('/create'),
            'edit' => Pages\EditAppVersion::route('/{record}/edit'),
        ];
    }

    public static function shouldRegisterNavigation(): bool
    {
        /** @var User */
        $user = Auth::user();

        return $user->can('system.admin');
    }
}
