<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;
use Sushi\Sushi;

class BackupFilesWrapper extends Model
{
    use HasFactory;
    use Sushi;

    public function getRows(): array
    {
        $files = Storage::disk('backup')->allFiles(config('app.name'));

        $rows = Arr::map($files, function (string $value, string $key) {
            $size = Storage::disk('backup')->size($value);
            $fecha = Storage::disk('backup')->lastModified($value);

            $fecha = Carbon::parse($fecha)->format('d/m/Y H:i:s');

            return [
                'nombre' => $value,
                'fecha' => $fecha,
                'size' => $size,
            ];
        });

        return $rows;
    }
}
