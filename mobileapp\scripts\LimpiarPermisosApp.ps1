param(
    [Parameter(Mandatory = $true)]
    [string]$AdbPath,

    [Parameter(Mandatory = $true)]
    [string]$AppId,

    [Parameter(Mandatory = $true)]
    [string[]]$Permissions
)

# Función para verificar si el ADB está disponible
function Test-AdbPath {
    if (-not (Test-Path $AdbPath)) {
        Write-Error "Error: No se puede encontrar ADB en la ruta especificada: $AdbPath"
        exit 1
    }
    
    # Verificar que sea un ejecutable válido
    try {
        $adbVersion = & $AdbPath version
        Write-Host "ADB encontrado: $adbPath" -ForegroundColor Green
        Write-Host $adbVersion -ForegroundColor Gray
    }
    catch {
        Write-Error "Error: El archivo en la ruta especificada no es un ejecutable ADB válido."
        exit 1
    }
}

# Función para verificar si un dispositivo está conectado
function Test-DeviceConnected {
    $devices = & $AdbPath devices
    if ($devices -notmatch "\w+\s+device$") {
        Write-Error "Error: No hay dispositivos Android conectados. Conecte un dispositivo e intente nuevamente."
        exit 1
    }
    
    Write-Host "Dispositivo Android conectado correctamente" -ForegroundColor Green
}

# Función para verificar si la aplicación existe
function Test-AppExists {
    $appInfo = & $AdbPath shell pm list packages | Select-String -Pattern $AppId
    if (-not $appInfo) {
        Write-Error "Error: La aplicación con ID '$AppId' no está instalada en el dispositivo."
        exit 1
    }
    
    Write-Host "Aplicación '$AppId' encontrada en el dispositivo" -ForegroundColor Green
}

# Función para obtener el estado actual de un permiso específico
function Get-PermissionState {
    param(
        [string]$Permission
    )
    
    $permissionState = & $AdbPath shell dumpsys package $AppId | Select-String -Pattern $Permission -Context 0, 5
    return $permissionState
}

# Función para comprobar si el dispositivo tiene acceso root
function Test-RootAccess {
    $rootTest = & $AdbPath shell "su -c 'id'" 2>&1
    if ($rootTest -match "uid=0") {
        Write-Host "Acceso root disponible en el dispositivo" -ForegroundColor Green
        return $true
    }
    else {
        Write-Host "No se detectó acceso root en el dispositivo" -ForegroundColor Yellow
        return $false
    }
}

# Función para limpiar un permiso específico
function Clear-Permission {
    param(
        [string]$Permission
    )
    
    Write-Host "Procesando permiso: $Permission" -ForegroundColor Yellow
    
    # Verificar el estado actual del permiso
    $permissionState = Get-PermissionState -Permission $Permission
    Write-Host "  → Estado actual:" -ForegroundColor Gray
    if ($permissionState) {
        Write-Host $permissionState -ForegroundColor Gray
    }
    else {
        Write-Host "    No se encontró información para este permiso" -ForegroundColor Gray
    }
    
    # Revocar el permiso primero si está concedido
    $revokeOutput = & $AdbPath shell pm revoke $AppId $Permission
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  → Permiso revocado exitosamente: $Permission" -ForegroundColor Green
    }
    else {
        Write-Host "  → El permiso no estaba concedido o no se pudo revocar: $Permission" -ForegroundColor Yellow
    }
    
    # Intentar métodos para eliminar el flag USER_FIXED
    Write-Host "  → Intentando eliminar flag USER_FIXED..." -ForegroundColor Cyan
    
    # Método 1: Usar clear-permission-flags específicamente para USER_FIXED
    $clearFixedOutput = & $AdbPath shell pm clear-permission-flags --user 0 $AppId $Permission USER_FIXED 2>&1
    
    # Método 2: Intento alternativo si está disponible en esta versión de Android
    $clearFixedAltOutput = & $AdbPath shell pm clear-permission-flags $AppId $Permission user_fixed 2>&1
    
    # Método 3: Intentar limpiar todas las flags
    $clearAllFlags = & $AdbPath shell pm clear-permission-flags $AppId $Permission user 2>&1
    
    # Método 4: Usar pm reset-permissions específicamente para esta app
    $resetPermsOutput = & $AdbPath shell pm reset-permissions -p $AppId 2>&1
    Write-Host "    ✓ Reset de permisos intentado para la aplicación" -ForegroundColor Green
    
    # Si hay acceso root, intentar métodos más directos
    $hasRoot = Test-RootAccess
    if ($hasRoot) {
        Write-Host "    Usando métodos con privilegios root..." -ForegroundColor Cyan
        
        # Eliminar archivos de base de datos de permisos (requiere root)
        & $AdbPath shell "su -c 'rm -f /data/system/users/0/runtime-permissions.xml'" 2>&1 | Out-Null
        Write-Host "    ✓ Archivo de permisos eliminado con privilegios root" -ForegroundColor Green
        
        # Reiniciar servicio packagemanager
        & $AdbPath shell "su -c 'killall com.android.packagemanager'" 2>&1 | Out-Null
        Write-Host "    ✓ Servicio packagemanager reiniciado" -ForegroundColor Green
    }
    else {
        # Método 5: Intento específico para Android 10+
        Write-Host "    Intentando comandos específicos para Android 10+..." -ForegroundColor Cyan
        & $AdbPath shell appops set $AppId $Permission ignore 2>&1 | Out-Null
        Start-Sleep -Milliseconds 500
        & $AdbPath shell appops set $AppId $Permission default 2>&1 | Out-Null
        Write-Host "    ✓ AppOps configuración intentada" -ForegroundColor Green
        
        # Método 6: Configuración específica de permisos a través de android.permission.*
        $permName = $Permission -replace "android\.permission\.", ""
        & $AdbPath shell settings put package $AppId $permName 2 2>&1 | Out-Null
        Start-Sleep -Milliseconds 500
        & $AdbPath shell settings delete package $AppId $permName 2>&1 | Out-Null
        Write-Host "    ✓ Settings configuración intentada" -ForegroundColor Green
    }
    
    # Forzar detención de la aplicación para aplicar cambios
    & $AdbPath shell am force-stop $AppId
    Write-Host "    ✓ Aplicación detenida para aplicar cambios" -ForegroundColor Green
    
    # Verificar el estado final
    $finalState = Get-PermissionState -Permission $Permission
    Write-Host "  → Estado final:" -ForegroundColor Gray
    if ($finalState) {
        Write-Host $finalState -ForegroundColor Gray
        
        # Verificar si todavía tiene USER_FIXED
        if ($finalState -match "USER_FIXED") {
            Write-Host "  → ADVERTENCIA: El flag USER_FIXED persiste, puede requerir acción manual" -ForegroundColor Red
            Write-Host "     Sugerencia: Intente borrar los datos de la aplicación desde los ajustes del dispositivo" -ForegroundColor Yellow
        }
        else {
            Write-Host "  → Limpieza exitosa: Flag USER_FIXED eliminado" -ForegroundColor Green
        }
    }
    else {
        Write-Host "    No se encontró información para este permiso" -ForegroundColor Gray
    }
}

# Verificaciones iniciales
Write-Host "Iniciando limpieza avanzada de permisos para la aplicación '$AppId'..." -ForegroundColor Cyan
Test-AdbPath
# Test-DeviceConnected
# Test-AppExists

# Procesar cada permiso
Write-Host "`nProcesando permisos...`n" -ForegroundColor Cyan
foreach ($permission in $Permissions) {
    Clear-Permission -Permission $permission
    Write-Host "" # Línea en blanco para separar los permisos
}

# Como último recurso, sugerir limpiar los datos de la aplicación
Write-Host "`nSi los permisos aún muestran USER_FIXED, se recomienda:" -ForegroundColor Yellow
Write-Host "1. Limpiar datos de la aplicación con este comando:" -ForegroundColor Yellow
Write-Host "   $AdbPath shell pm clear $AppId" -ForegroundColor White
Write-Host "   ADVERTENCIA: Esto eliminará todos los datos de la aplicación" -ForegroundColor Red
Write-Host "2. O desinstalar y reinstalar la aplicación" -ForegroundColor Yellow
Write-Host "   $AdbPath uninstall $AppId" -ForegroundColor White

Write-Host "`nProcesamiento de permisos completado." -ForegroundColor Cyan
Write-Host "Resumen de operación:" -ForegroundColor Cyan
Write-Host "  - Aplicación: $AppId" -ForegroundColor White
Write-Host "  - Permisos procesados: $($Permissions.Count)" -ForegroundColor White

# Ejemplo de uso del script:
# .\LimpiarPermisosApp.ps1 -AdbPath "C:\devtools\android-sdk\platform-tools\adb.exe" -AppId "es.lbcdev.mia" -Permissions @("android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION")