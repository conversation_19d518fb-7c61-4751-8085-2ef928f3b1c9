# Referencia Rápida - Proyecto MIA

## 🚀 Comandos Esenciales

### Backend (Laravel)

```bash
# Desarrollo
php artisan serve                    # Iniciar servidor
php artisan migrate                  # Ejecutar migraciones
php artisan db:seed                  # Ejecutar seeders
php artisan cache:clear              # Limpiar caché

# Testing
php artisan test                     # Todos los tests
php artisan test --filter NegocioApiTest  # Test específico
php artisan test --group api         # Tests por grupo

# Generación de código
php artisan make:model Modelo        # Crear modelo
php artisan make:migration create_tabla  # Crear migración
php artisan make:controller Api/ModeloController --api  # Controlador API
php artisan make:request StoreModeloRequest  # Form Request
php artisan make:policy ModeloPolicy --model=Modelo  # Policy
php artisan make:filament-resource Modelo --generate  # Recurso Filament
```

### Frontend (Flutter)

```bash
# Desarrollo
flutter pub get                      # Instalar dependencias
flutter run                          # Ejecutar app
flutter run -d chrome                # Ejecutar en web
flutter clean                        # Limpiar build

# Testing
flutter test                         # Todos los tests
flutter test test/unit/models/negocio_test.dart  # Test específico
flutter test --coverage              # Con cobertura

# Build
flutter build apk                    # Build Android
flutter build ios                    # Build iOS
```

## 📁 Estructura de Archivos Clave

### Backend
```
webapp/
├── app/
│   ├── Models/              # Modelos Eloquent
│   ├── Http/Controllers/Api/  # Controladores API
│   ├── Http/Requests/       # Validación
│   ├── Policies/            # Autorización
│   ├── Enums/               # Enumeraciones
│   └── Filament/Resources/  # Panel admin
├── routes/api.php           # Rutas API
├── database/migrations/     # Migraciones
└── tests/Feature/Api/       # Tests API
```

### Frontend
```
mobileapp/
├── lib/
│   ├── models/              # Modelos Dart
│   ├── services/            # Servicios (API, storage)
│   ├── providers/           # Estado (Provider)
│   ├── pages/               # Pantallas
│   └── widgets/             # Widgets reutilizables
└── test/
    ├── unit/models/         # Tests de modelos
    └── fixture/json/        # Datos de prueba
```

## 🔑 Patrones Comunes

### Backend: Crear Endpoint API

```php
// 1. Ruta (routes/api.php)
Route::get('/v1/modelos', [ModeloController::class, 'index']);

// 2. Controlador (app/Http/Controllers/Api/ModeloController.php)
public function index()
{
    $modelos = Modelo::with(['relacion'])->get();
    return response()->json($modelos);
}

// 3. Policy (app/Policies/ModeloPolicy.php)
public function viewAny(User $user): bool
{
    return $user->can('modelo.list');
}
```

### Frontend: Consumir API

```dart
// 1. Modelo (lib/models/modelo.dart)
class Modelo {
  final int id;
  final String nombre;
  
  factory Modelo.fromJson(Map<String, dynamic> json) {
    return Modelo(
      id: json['id'] as int,
      nombre: json['nombre'] as String,
    );
  }
}

// 2. Servicio (lib/services/api_service.dart)
Future<List<Modelo>> getModelos() {
  return fetchData<Modelo>(
    endpoint: 'modelos',
    fromJson: Modelo.fromJson,
  );
}

// 3. Uso en Widget
final modelos = await ApiService().getModelos();
```

## 🧪 Plantillas de Testing

### Backend: Test de API

```php
public function test_puede_listar_recursos(): void
{
    // Arrange
    Modelo::factory(3)->create();
    
    // Act
    $response = $this->getJson('/api/v1/modelos');
    
    // Assert
    $response->assertStatus(200)
        ->assertJsonCount(3);
}
```

### Frontend: Test de Modelo

```dart
test('Debe deserializar JSON correctamente', () {
  final json = {'id': 1, 'nombre': 'Test'};
  
  final modelo = Modelo.fromJson(json);
  
  expect(modelo.id, equals(1));
  expect(modelo.nombre, equals('Test'));
});
```

## 📊 Modelos Principales

### Backend (Laravel)

| Modelo | Tabla | Relaciones Principales |
|--------|-------|------------------------|
| User | users | hasMany(Negocio) |
| Localidad | localidades | hasMany(Zona) |
| Zona | zonas | belongsTo(Localidad), hasMany(Negocio) |
| Negocio | negocios | belongsTo(Zona), belongsToMany(Categoria) |
| Categoria | categorias | belongsToMany(Negocio) |
| Suscripcion | suscripciones | belongsTo(Negocio), hasMany(PagoSuscripcion) |
| Evento | eventos | belongsTo(Negocio) |

### Enums

| Enum | Valores | Uso |
|------|---------|-----|
| EstadoPago | pendiente, completado, rechazado | PagoSuscripcion |
| MetodoPago | efectivo, bizum, transferencia | PagoSuscripcion |
| EstadoSuscripcion | 0 (inactivo), 1 (activo) | Suscripcion |
| TipoSuscripcion | free, basic | Suscripcion |

## 🔐 Sistema de Permisos

### Roles
- `admin` - Acceso total
- `owner` - Propietario de negocios
- `cliente` - Cliente con acceso limitado
- `usuario` - Usuario básico

### Formato de Permisos
```
{modelo}.{accion}

Ejemplos:
- negocio.list
- negocio.create
- negocio.update
- system.access-panel
```

## 🌐 API Endpoints

### Públicos
```
GET  /api/v1/negocios          # Listar negocios
GET  /api/v1/negocios/{id}     # Ver negocio
GET  /api/v1/categorias        # Listar categorías
GET  /api/v1/zonas             # Listar zonas
GET  /api/v1/eventos           # Listar eventos
```

### Autenticados (requieren Bearer token)
```
POST   /api/v1/negocios        # Crear negocio
PUT    /api/v1/negocios/{id}   # Actualizar negocio
DELETE /api/v1/negocios/{id}   # Eliminar negocio
```

## 🎨 Providers (Flutter)

| Provider | Propósito | Métodos Clave |
|----------|-----------|---------------|
| ThemeProvider | Tema claro/oscuro | setTheme(AppTheme) |
| SettingsProvider | Configuración app | setMapPlatform(), setDistanceKm() |
| FavoritesProvider | Favoritos usuario | toggleFavorite(int id) |
| LocationProvider | Ubicación GPS | fetchLocation(), startTracking() |
| PermissionProvider | Permisos | checkPermission(), requestPermission() |

## 📝 Convenciones de Código

### Nomenclatura

**Backend (PHP)**:
- Clases: `PascalCase` (ej: `NegocioController`)
- Métodos: `camelCase` (ej: `getNegocios`)
- Variables: `camelCase` (ej: `$negocioId`)
- Tablas: `snake_case` plural (ej: `negocios`)
- Columnas: `snake_case` (ej: `zona_id`)

**Frontend (Dart)**:
- Clases: `PascalCase` (ej: `NegocioCard`)
- Métodos: `camelCase` (ej: `getNegocios`)
- Variables: `camelCase` (ej: `negocioId`)
- Archivos: `snake_case` (ej: `negocio_card.dart`)

## 🔧 Troubleshooting

### Backend

```bash
# Caché problemático
php artisan cache:clear
php artisan config:clear
php artisan route:clear

# Permisos
php artisan permissions:generate

# Regenerar autoload
composer dump-autoload
```

### Frontend

```bash
# Problemas de dependencias
flutter pub get
flutter pub upgrade

# Problemas de build
flutter clean
flutter pub get
flutter run

# Regenerar código generado
flutter pub run build_runner build --delete-conflicting-outputs
```

## 📚 Documentación Completa

- **Inicio**: [docs/README.md](./README.md)
- **Arquitectura**: [docs/01-vision-general/README.md](./01-vision-general/README.md)
- **Backend**: [docs/02-backend/README.md](./02-backend/README.md)
- **Frontend**: [docs/03-frontend/README.md](./03-frontend/README.md)
- **API**: [docs/05-api/README.md](./05-api/README.md)
- **Testing**: [docs/06-testing/README.md](./06-testing/README.md)
- **Guías**: [docs/07-guias/como-anadir-modelo.md](./07-guias/como-anadir-modelo.md)

## 🆘 Recursos Útiles

### Laravel
- [Documentación Laravel](https://laravel.com/docs)
- [Filament](https://filamentphp.com/docs)
- [Spatie Permissions](https://spatie.be/docs/laravel-permission)

### Flutter
- [Documentación Flutter](https://flutter.dev/docs)
- [Provider](https://pub.dev/packages/provider)
- [Flutter Map](https://pub.dev/packages/flutter_map)

---

**Tip**: Guarda este archivo como referencia rápida. Para detalles completos, consulta la documentación específica de cada sección.

