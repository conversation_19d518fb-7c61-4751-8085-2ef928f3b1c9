<?php

namespace App\Filament\Resources\CategoriaResource\Pages;

use Filament\Actions;
use App\Models\Negocio;
use App\Models\Categoria;
use App\Services\CacheService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Validator;
use App\Filament\Resources\CategoriaResource;
use App\Http\Requests\UpdateCategoriaRequest;

class EditCategoria extends EditRecord
{
    protected static string $resource = CategoriaResource::class;

    protected CacheService $cacheService;

    public function boot(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        /** @var User */
        $user = auth()->guard('web')->user();

        // Verifica la autorización usando la lógica de StoreCategoriaRequest
        if (!$user->can('update', $record)) {
            abort(403, 'No tienes permiso para crear una categoría.');
        }

        $data['record'] = $record->id;
        $request = new UpdateCategoriaRequest();
        $request->merge($data);

        // Validar datos usando las reglas de StoreCategoriaRequest
        $validator = Validator::make($request->all(), $request->rules());
        $validatedData = $validator->validate();

        $record->update($validatedData);

        $this->cacheService->invalidateCache(Categoria::class, 'categorias_all');
        $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');

        return $record;
    }
}
