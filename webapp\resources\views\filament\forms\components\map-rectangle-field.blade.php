<x-dynamic-component :component="$getFieldWrapperView()" :field="$field">
    @php
        $state = $getState() ?? [
            'latitud_min' => '',
            'latitud_max' => '',
            'longitud_min' => '',
            'longitud_max' => '',
        ];
        // Si se definieron límites, calcular centro para el mapa
        $centerLat = ($state['latitud_min'] && $state['latitud_max']) 
            ? (($state['latitud_min'] + $state['latitud_max']) / 2) 
            : 36.9990019;
        $centerLng = ($state['longitud_min'] && $state['longitud_max']) 
            ? (($state['longitud_min'] + $state['longitud_max']) / 2) 
            : -6.5478919;
    @endphp

    <div 
        x-data="mapRectangleComponent({
            latitud_min: '{{ $state['latitud_min'] ?? $centerLat - 0.01 }}',
            latitud_max: '{{ $state['latitud_max'] ?? $centerLat + 0.01 }}',
            longitud_min: '{{ $state['longitud_min'] ?? $centerLng - 0.01 }}',
            longitud_max: '{{ $state['longitud_max'] ?? $centerLng + 0.01 }}',
            centerLat: '{{ $centerLat }}',
            centerLng: '{{ $centerLng }}'
        })" 
        x-init="initMap()"
        class="w-full h-72 relative space-y-4"
        wire:ignore
    >
        <div class="flex items-center gap-2">
            <x-heroicon-o-map-pin class="w-5 h-5 text-primary-500" />
            
            <span 
                class="text-sm font-medium text-gray-700 dark:text-gray-300"
                x-text="
                    (bounds.latitud_min && bounds.latitud_max && bounds.longitud_min && bounds.longitud_max)
                        ? `Límites: ${bounds.latitud_min}, ${bounds.longitud_min} – ${bounds.latitud_max}, ${bounds.longitud_max}`
                        : 'Selecciona los límites'
                "
            ></span>
        </div>
        <div id="map-{{ $getStatePath() }}" class="w-full h-full rounded" style="height: 400px;"></div>
    </div>

    <script>
        function mapRectangleComponent(initial) {
            return {
                // Valores iniciales del rectángulo
                bounds: {
                    latitud_min: initial.latitud_min,
                    latitud_max: initial.latitud_max,
                    longitud_min: initial.longitud_min,
                    longitud_max: initial.longitud_max,
                },
                map: null,
                rectangle: null,
                drawnItems: null,
                dreawControl: null,
                initMap() {
                    // Definir centro del mapa
                    let centerLat = parseFloat(initial.centerLat) || 36.9990019;
                    let centerLng = parseFloat(initial.centerLng) || -6.5478919;

                    this.map = L.map('map-{{ $getStatePath() }}').setView([centerLat, centerLng], 13);

                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        maxZoom: 19,
                        attribution: '© OpenStreetMap contributors'
                    }).addTo(this.map);

                    // Crear el grupo de capas editables y añadirlo al mapa
                    this.drawnItems = new L.FeatureGroup();
                    this.map.addLayer(this.drawnItems);

                    // Configurar el control de dibujo/edición
                    this.drawControl = new L.Control.Draw({
                        draw: {
                            polygon: false,
                            polyline: false,
                            circle: false,
                            circlemarker: false,
                            marker: false,
                            rectangle: false, // no permitimos crear nuevos rectángulos
                        },
                        edit: {
                            featureGroup: this.drawnItems, // Asocia el grupo editable
                            edit: true,
                            remove: false,
                        }
                    });
                    this.map.addControl(this.drawControl);

                    // Definir límites iniciales
                    let southWest, northEast;
                    if (initial.latitud_min && initial.longitud_min && initial.latitud_max && initial.longitud_max) {
                        southWest = L.latLng(initial.latitud_min, initial.longitud_min);
                        northEast = L.latLng(initial.latitud_max, initial.longitud_max);
                    } else {
                        // Límites por defecto basados en el centro
                        southWest = L.latLng(centerLat - 0.01, centerLng - 0.01);
                        northEast = L.latLng(centerLat + 0.01, centerLng + 0.01);
                    }
                    let rectangleBounds = L.latLngBounds(southWest, northEast);

                    // Crear el rectángulo y agregarlo al grupo editable
                    this.rectangle = L.rectangle(rectangleBounds, {color: "#ff7800", weight: 1});
                    this.drawnItems.addLayer(this.rectangle);

                    // Ajustar el mapa para mostrar el rectángulo
                    this.map.fitBounds(rectangleBounds);

                    // Iniciar el modo de edición programáticamente para activar los "handlers"
                    // Nota: el acceso a _toolbars.edit puede cambiar según la versión de Leaflet Draw
                    setTimeout(() => {
                        this.drawControl._toolbars.edit._modes.edit.handler.enable();
                    }, 500);

                    // Escuchar el evento 'edit' para actualizar el estado
                    this.map.on('draw:edited', (e) => {
                        e.layers.eachLayer((layer) => {
                            if (layer._leaflet_id === this.rectangle._leaflet_id) {
                                let b = layer.getBounds();
                                let newState = {
                                    latitud_min: b.getSouthWest().lat.toFixed(6),
                                    longitud_min: b.getSouthWest().lng.toFixed(6),
                                    latitud_max: b.getNorthEast().lat.toFixed(6),
                                    longitud_max: b.getNorthEast().lng.toFixed(6),
                                };
                                this.bounds = newState; // Actualiza la variable para la etiqueta dinámica
                                @this.set('{{ $getStatePath() }}', newState);
                            }
                        });
                    });
                }
            }
        }
    </script>
</x-dynamic-component>
