<?php

namespace App\Filament\Resources\AppUserResource\Pages;

use Filament\Actions;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Validator;
use App\Filament\Resources\AppUserResource;
use App\Http\Requests\UpdateClienteRequest;

class EditAppUser extends EditRecord
{
    protected static string $resource = AppUserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        if ($data['password'] == '' || $data['password_confirmation'] == '') {
            unset($data['password']);
            unset($data['password_confirmation']);
        }

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        /** @var User */
        $user = Auth::user();

        if (!$user->can('user.update', $record)) {
            Notification::make()
                ->title('No tienes permiso para editar este usuario.')
                ->danger()
                ->send();
            abort(403, 'No tienes permiso para editar este usuario');
        }

        $request = new UpdateClienteRequest($user);
        $validator = Validator::make($data, $request->rules(), $request->messages());
        try {
            $validatedData = $validator->validate();
            $record->update($validatedData);

            return $record;
        } catch (\Illuminate\Validation\ValidationException $e) {
            Notification::make()
                ->title('Error al actualizar el usuario.')
                // Mostrar en el body una lista de los errores de validación a partir de StoreClienteRequest::messages()
                ->body(function () use ($e) {
                    $errors = $e->validator->errors();
                    $errorMessages = [];
                    foreach ($errors->all() as $error) {
                        $errorMessages[] = $error;
                    }
                    return implode('<br>', $errorMessages);
                })
                ->danger()
                ->send();
            throw $e;
        }
    }
}
