{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Listen for Xdebug",
            "type": "php",
            "request": "launch",
            "port": 9003
        },
        // {
        //     "name": "Flutter (Local)",
        //     "type": "dart",
        //     "request": "launch",
        //     "program": "lib/main.dart",
        //     "cwd": "mobileapp",
        //     "args": [
        //         "--dart-define=FLAVOR=local",
        //         "--dart-define=ENV_FILE=.env.local"
        //     ]
        // },
        // {
        //     "name": "Flutter (Staging)",
        //     "type": "dart",
        //     "request": "launch",
        //     "program": "lib/main.dart",
        //     "cwd": "mobileapp",
        //     "args": [
        //         "--dart-define=FLAVOR=staging",
        //         "--dart-define=ENV_FILE=.env.staging"
        //     ]
        // },
        // {
        //     "name": "Flutter (Production)",
        //     "type": "dart",
        //     "request": "launch",
        //     "program": "lib/main.dart",
        //     "cwd": "mobileapp",
        //     "flutterMode": "release",
        //     "args": [
        //         "--dart-define=FLAVOR=production",
        //         "--dart-define=ENV_FILE=.env.production"
        //     ]
        // },
        {
            "name": "Flutter (Local)",
            "request": "launch",
            "type": "dart",
            "preLaunchTask": "generate-env-local",
            "cwd": "mobileapp",
            "program": "lib/main.dart"
        },
        {
            "name": "Flutter (Staging)",
            "request": "launch",
            "type": "dart", 
            "preLaunchTask": "generate-env-staging",
            "cwd": "mobileapp",
            "program": "lib/main.dart"
        },
        {
            "name": "Flutter (Production)",
            "request": "launch",
            "type": "dart",
            "preLaunchTask": "generate-env-production",
            "cwd": "mobileapp",
            "program": "lib/main.dart"
        },
        {
            "name": "Flutter (Profile Mode)",
            "type": "dart",
            "request": "launch",
            "program": "lib/main.dart",
            "cwd": "mobileapp",
            "flutterMode": "profile"
        }
    ],
    "compounds": [
        {
            "name": "All Devices (Local)",
            "configurations": ["Flutter (Local)"]
        }
    ]
}