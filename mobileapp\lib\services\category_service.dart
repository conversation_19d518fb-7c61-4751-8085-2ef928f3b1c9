import 'package:mia/models/categoria.dart';

class CategoryService {
  List<Categoria> getMainCategories(List<Categoria> categorias) {
    return categorias.where((categoria) => categoria.parentId == null).toList();
  }

  List<Categoria> getSubCategorias(
      List<Categoria> categorias, Categoria categoriaPadre) {
    return categorias
        .where((categoria) => categoria.parentId == categoriaPadre.id)
        .toList();
  }

  Map<String, List<Categoria>> cargarSubcategorias(
      List<Categoria> categorias, Categoria categoria) {
    Map<String, List<Categoria>> subcategorias = {};
    List<Categoria> nuevasSubcategorias =
        getSubCategorias(categorias, categoria);

    if (nuevasSubcategorias.isNotEmpty) {
      subcategorias[categoria.nombre] = nuevasSubcategorias;
      for (var nuevaSubcategoria in nuevasSubcategorias) {
        subcategorias
            .addAll(cargarSubcategorias(categorias, nuevaSubcategoria));
      }
    }
    return subcategorias;
  }

  void removeSubcategorias(List<Categoria> categoriasSeleccionadas,
      Map<String, List<Categoria>> subcategorias, Categoria subcategoria) {
    subcategorias.remove(subcategoria.nombre);

    List<String> keysToRemove = [];
    subcategorias.forEach((key, value) {
      if (value.any((element) => element.parentId == subcategoria.id)) {
        keysToRemove.add(key);
      }
    });

    for (var key in keysToRemove) {
      subcategorias[key]!
          .removeWhere((element) => element.parentId == subcategoria.id);
    }

    categoriasSeleccionadas.removeWhere(
        (cat) => cat.id == subcategoria.id || cat.parentId == subcategoria.id);

    if (subcategorias.containsKey(subcategoria.nombre)) {
      for (var sub in subcategorias[subcategoria.nombre]!) {
        removeSubcategorias(categoriasSeleccionadas, subcategorias, sub);
      }
    }
  }
}
