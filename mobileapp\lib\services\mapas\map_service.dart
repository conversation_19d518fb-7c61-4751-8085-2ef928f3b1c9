import 'package:latlong2/latlong.dart';
import 'package:mia/models/negocio.dart';

class MapService {
  /// Retorna el centro de las coordenadas de [negocios].
  /// Para ello, calculamos el bounding box (minLat, maxLat, minLng, maxLng)
  /// y luego sacamos el punto medio.
  static LatLng getCenter(List<Negocio> negocios) {
    if (negocios.isEmpty) {
      // Lista vacía => valor por defecto
      return LatLng(0.0, 0.0);
    }

    final double minLat = getMinLat(negocios);
    final double maxLat = getMaxLat(negocios);
    final double minLng = getMinLng(negocios);
    final double maxLng = getMaxLng(negocios);

    final double centerLat = (minLat + maxLat) / 2.0;
    final double centerLng = (minLng + maxLng) / 2.0;

    return LatLng(centerLat, centerLng);
  }

  /// Obtiene el máximo valor de latitud en la lista [negocios].
  static double getMaxLat(List<Negocio> negocios) {
    if (negocios.isEmpty) return 0.0;

    double maxLat = double.negativeInfinity;
    for (final negocio in negocios) {
      final lat = _getLat(negocio);
      if (lat > maxLat) {
        maxLat = lat;
      }
    }
    return maxLat == double.negativeInfinity ? 0.0 : maxLat;
  }

  /// Obtiene el mínimo valor de latitud en la lista [negocios].
  static double getMinLat(List<Negocio> negocios) {
    if (negocios.isEmpty) return 0.0;

    double minLat = double.infinity;
    for (final negocio in negocios) {
      final lat = _getLat(negocio);
      if (lat < minLat) {
        minLat = lat;
      }
    }
    return minLat == double.infinity ? 0.0 : minLat;
  }

  /// Obtiene el máximo valor de longitud en la lista [negocios].
  static double getMaxLng(List<Negocio> negocios) {
    if (negocios.isEmpty) return 0.0;

    double maxLng = double.negativeInfinity;
    for (final negocio in negocios) {
      final lng = _getLng(negocio);
      if (lng > maxLng) {
        maxLng = lng;
      }
    }
    return maxLng == double.negativeInfinity ? 0.0 : maxLng;
  }

  /// Obtiene el mínimo valor de longitud en la lista [negocios].
  static double getMinLng(List<Negocio> negocios) {
    if (negocios.isEmpty) return 0.0;

    double minLng = double.infinity;
    for (final negocio in negocios) {
      final lng = _getLng(negocio);
      if (lng < minLng) {
        minLng = lng;
      }
    }
    return minLng == double.infinity ? 0.0 : minLng;
  }

  /// Función privada para extraer la latitud de un [Negocio].
  static double _getLat(Negocio negocio) {
    if (negocio.ubicacion == null) return 0.0;
    final latString = negocio.ubicacion!['latitud'];
    if (latString == null) return 0.0;
    return double.tryParse(latString) ?? 0.0;
  }

  /// Función privada para extraer la longitud de un [Negocio].
  static double _getLng(Negocio negocio) {
    if (negocio.ubicacion == null) return 0.0;
    final lngString = negocio.ubicacion!['longitud'];
    if (lngString == null) return 0.0;
    return double.tryParse(lngString) ?? 0.0;
  }
}
