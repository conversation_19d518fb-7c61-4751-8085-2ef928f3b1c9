<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class LogService
{
    public static function log($message, $level = 'info')
    {
        Log::$level($message);
    }



    // Escribir en la consola al estilo de console.log de javasrcipt

    // Método 1: Usando error_log para escribir en la salida de error estándar (stderr)
    static function console_log($data)
    {
        if (is_array($data) || is_object($data)) {
            error_log(print_r($data, true));
        } else {
            error_log($data);
        }
    }

    // Método 2: Usando fwrite para escribir en la salida estándar (stdout)
    static function console_stdout($data)
    {
        $stdout = fopen('php://stdout', 'w');
        if (is_array($data) || is_object($data)) {
            fwrite($stdout, print_r($data, true) . "\n");
        } else {
            fwrite($stdout, $data . "\n");
        }
        fclose($stdout);
    }

    // Método 3: Usando echo para la salida estándar cuando se ejecuta desde CLI
    static function cli_log($data)
    {
        if (php_sapi_name() === 'cli') {
            if (is_array($data) || is_object($data)) {
                echo print_r($data, true) . PHP_EOL;
            } else {
                echo $data . PHP_EOL;
            }
        }
    }
}
