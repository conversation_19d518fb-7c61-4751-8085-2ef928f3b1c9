<?php

namespace App\Http\Requests;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateClienteRequest extends FormRequest
{

    private $usuario;

    public function __construct()
    {
        $this->usuario = $this->route('user');
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('user.update');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        if ($this->usuario != null) {
            $reglaUnique = Rule::unique('users', 'email')->ignore($this->usuario->id);
        } else {
            $reglaUnique = 'email';
        }

        return [
            'name' => 'sometimes|string|max:255',
            // 'email' => 'sometimes|string|email|max:255|unique:users,email',
            'email' => [
                'sometimes',
                'string',
                'email',
                'max:255',
                $reglaUnique,

            ],
            'password' => 'sometimes|string|min:8|max:255|same:password_confirmation',
            'password_confirmation' => 'sometimes|string|min:8|max:255',
        ];
    }

    public function messages(): array
    {
        return [
            'name.max' => 'El nombre no puede tener más de 255 caracteres.',
            'email.email' => 'El email debe ser una dirección de correo válida.',
            'email.max' => 'El email no puede tener más de 255 caracteres.',
            'email.unique' => 'El email ya está en uso.',
            'password.min' => 'La contraseña debe tener al menos 8 caracteres.',
            'password.max' => 'La contraseña no puede tener más de 255 caracteres.',
            'password.same' => 'La confirmación de la contraseña no coincide.',
        ];
    }
}
