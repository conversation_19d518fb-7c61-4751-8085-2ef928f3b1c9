// test/unit/models/categoria_factory.dart

import 'package:mia/models/categoria.dart';
import 'package:mia/models/media.dart';

Categoria createCategoria({
  int id = 1,
  String nombre = 'Categoria de Prueba',
  String? descripcion = 'Descripción de prueba',
  int? parentId,
  List<Media>? iconos,
  int nivel = 1,
  int orden = 0,
}) {
  return Categoria(
    id: id,
    nombre: nombre,
    descripcion: descripcion,
    parentId: parentId,
    iconos: iconos,
    nivel: nivel,
    orden: orden,
  );
}
