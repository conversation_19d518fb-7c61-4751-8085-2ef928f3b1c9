<?php

namespace App\Http\Requests;

use App\Models\Localidad;
use Illuminate\Foundation\Http\FormRequest;

class StoreLocalidadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('create', Localidad::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'nombre' => 'required|string|max:255',
            'ubicacion' => 'required|array',
            'ubicacion.latitud' => 'required|numeric|between:-90,90',
            'ubicacion.longitud' => 'required|numeric|between:-180,180',
            'limites' => 'required|array',
            'limites.latitud_min' => 'required|numeric|between:-90,90|lt:limites.latitud_max',
            'limites.latitud_max' => 'required|numeric|between:-90,90|gt:limites.latitud_min',
            'limites.longitud_min' => 'required|numeric|between:-180,180|lt:limites.longitud_max',
            'limites.longitud_max' => 'required|numeric|between:-180,180|gt:limites.longitud_min',
        ];
    }
}
