// Mantener este comentario con el nombre de archivo
// lib/widgets/app_bottom_navigation_bar.dart

import 'package:flutter/material.dart';
import 'package:mia/config/colors.dart';
import 'package:mia/services/core_service.dart';
import 'package:mia/services/global_data_service.dart';
import 'package:provider/provider.dart';
import 'package:mia/providers/favorites_provider.dart';
import 'package:mia/providers/permission_provider.dart';

class AppBottomNavigationBar extends StatefulWidget {
  final int currentIndex;
  final Function(int)? onIndexChanged;

  const AppBottomNavigationBar({
    super.key,
    this.currentIndex = 0,
    this.onIndexChanged,
  });

  @override
  State<AppBottomNavigationBar> createState() => _AppBottomNavigationBarState();
}

class _AppBottomNavigationBarState extends State<AppBottomNavigationBar>
    with WidgetsBindingObserver {
  final double enabledOpacity = 1.0;
  final double disabledIconOpacity = 0.2;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    context.read<PermissionProvider>().checkPermission();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      context.read<PermissionProvider>().checkPermission();
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool hasEvents = (GlobalDataService().eventos != null &&
        GlobalDataService().eventos!.isNotEmpty);

    final bool hasFavorites =
        context.watch<FavoritesProvider>().favorites.isNotEmpty;
    debugPrint('[AppBottomNav] build hasFavorites = $hasFavorites');

    final permissionProvider = context.watch<PermissionProvider>();
    double locationIconOpacity =
        (permissionProvider.status == LocationPermissionStatus.granted)
            ? enabledOpacity
            : disabledIconOpacity;

    return BottomNavigationBar(
      key: ValueKey<bool>(hasFavorites),
      backgroundColor: AppColors.current.surfaceColor,
      type: BottomNavigationBarType.fixed,
      showUnselectedLabels: true,
      currentIndex: widget.currentIndex,
      selectedItemColor: AppColors.current.accentColor,
      unselectedItemColor: AppColors.current.textColor,
      onTap: (index) {
        widget.onIndexChanged?.call(index);

        if (index == 3 && !hasEvents) {
          CoreService.muestraError('No hay eventos disponibles');
          return;
        }
        if (index == 2 && !hasFavorites) {
          CoreService.muestraError('Todavía no tienes favoritos guardados');
          return;
        }
        if (index == 1) {
          _handleLocationNavigation(context.read<PermissionProvider>());
          return;
        }
        switch (index) {
          case 0:
            Navigator.pushReplacementNamed(context, '/categorias');
            break;
          case 2:
            Navigator.pushReplacementNamed(context, '/favoritos');
            break;
          case 3:
            Navigator.pushReplacementNamed(context, '/eventos');
            break;
          case 4:
            Navigator.pushReplacementNamed(context, '/profile');
            break;
        }
      },
      items: <BottomNavigationBarItem>[
        BottomNavigationBarItem(
          icon: Icon(Icons.search),
          label: 'Explorar',
        ),
        BottomNavigationBarItem(
          icon: Opacity(
              opacity: locationIconOpacity, child: Icon(Icons.location_on)),
          label: 'Cerca de mí',
        ),
        BottomNavigationBarItem(
          icon: Opacity(
            opacity: hasFavorites ? enabledOpacity : disabledIconOpacity,
            child: Icon(Icons.favorite),
          ),
          label: 'Favoritos',
        ),
        BottomNavigationBarItem(
          icon: Opacity(
              opacity: hasEvents ? enabledOpacity : disabledIconOpacity,
              child: Icon(Icons.event)),
          label: 'Eventos',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.settings),
          label: 'Configuración',
        ),
      ],
    );
  }

  Future<void> _handleLocationNavigation(PermissionProvider p) async {
    switch (p.status) {
      case LocationPermissionStatus.granted:
        Navigator.pushReplacementNamed(context, '/cerca-de-mi');
        break;
      case LocationPermissionStatus.denied:
      case LocationPermissionStatus.unknown:
        await p.requestPermission();
        if (p.status == LocationPermissionStatus.granted) {
          // Verificar si el widget sigue montado después de la operación asíncrona
          if (mounted) {
            if (p.status == LocationPermissionStatus.granted) {
              Navigator.pushReplacementNamed(context, '/cerca-de-mi');
            } else {
              CoreService.muestraError(
                  'El permiso de localización es necesario para usar esta funcionalidad');
            }
          }
        } else {
          CoreService.muestraError(
              'El permiso de localización es necesario para usar esta funcionalidad');
        }
        break;
      case LocationPermissionStatus.permanentlyDenied:
        CoreService.openAppSystemSettings(context);
        break;
    }
  }
}
