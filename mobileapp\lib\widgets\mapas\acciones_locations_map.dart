// Mantener este comentario con el nombre de archivo
// mobileapp\lib\widgets\mapas\acciones_locations_map.dart

import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:mia/models/negocio.dart';
import 'package:mia/services/core_service.dart';
import 'package:mia/widgets/mapas/locations_map.dart';

class AccionesLocationsMap extends StatelessWidget {
  const AccionesLocationsMap({
    super.key,
    required this.widget,
    required this.negociosList,
  });

  final LocationsMap widget;
  final List<Negocio> negociosList;

  @override
  Widget build(BuildContext context) {
    return Positioned(
        right: 10,
        top: 10,
        child: Column(children: [
          FloatingActionButton(
              heroTag: 'zoomIn',
              mini: true,
              onPressed: () => {
                    widget.mapController.animateTo(
                        zoom:
                            widget.mapController.mapController.camera.zoom + 1,
                        customId: 'useTransformerId')
                  },
              child: const Icon(Icons.add)),
          FloatingActionButton(
              heroTag: 'zoomOut',
              mini: true,
              onPressed: () => {
                    widget.mapController.animateTo(
                        zoom:
                            widget.mapController.mapController.camera.zoom - 1,
                        customId: 'useTransformerId')
                  },
              child: const Icon(Icons.remove)),
          widget.selectedNegocio != null
              ? FloatingActionButton(
                  heroTag: 'CentrarNegocio',
                  mini: true,
                  onPressed: () => widget.mapController.animateTo(
                      dest: CoreService.getLatLng(widget.selectedNegocio!),
                      zoom: widget.mapController.mapController.camera.zoom,
                      customId: 'useTransformerId'),
                  child: const Icon(Icons.location_pin))
              : negociosList.length == 1
                  ? FloatingActionButton(
                      heroTag: 'CentrarNegocio',
                      mini: true,
                      onPressed: () => widget.mapController.animateTo(
                          dest: CoreService.getLatLng(negociosList.first),
                          zoom: widget.mapController.mapController.camera.zoom,
                          customId: 'useTransformerId'),
                      child: const Icon(Icons.location_pin))
                  : SizedBox(
                      height: 0,
                    ),
          if (widget.showUserLocation && widget.userLocation != null)
            FloatingActionButton(
              heroTag: 'centerUser',
              mini: true,
              onPressed: () => widget.mapController.animateTo(
                dest: widget.userLocation,
                zoom: widget.mapController.mapController.camera.zoom,
                customId: 'centerUser',
              ),
              child: const Icon(Icons.my_location),
            ),
          // fit both
          if (widget.showUserLocation &&
              widget.userLocation != null &&
              (negociosList.length + 1 >= 2))
            FloatingActionButton(
              heroTag: 'fitBounds',
              mini: true,
              onPressed: () async {
                final points = <LatLng>[
                  widget.userLocation!,
                  // si hay negocio seleccionado, solo ese…
                  if (widget.selectedNegocio != null)
                    CoreService.getLatLng(widget.selectedNegocio!)
                  else
                    // …en otro caso, añadimos todos los negocios
                    for (final n in negociosList) CoreService.getLatLng(n),
                ];

                final bounds = LatLngBounds.fromPoints(points);

                await widget.mapController.animatedFitCamera(
                  cameraFit: CameraFit.bounds(
                      bounds: bounds, padding: const EdgeInsets.all(20)),
                  curve: Curves.easeInOut,
                  duration: const Duration(milliseconds: 500),
                );
              },
              child: const Icon(Icons.center_focus_strong),
            ),
        ]));
  }
}
