// lib/models/evento.dart

import 'package:flutter/foundation.dart';
import 'package:mia/models/media.dart';
import 'package:mia/models/negocio.dart';
import 'package:mia/services/global_data_service.dart';
import 'package:mia/services/core_service.dart';

class Evento {
  final int id;
  final String nombre;
  final String descripcion;
  final String url;
  final String fechaInicio;
  final String fechaFin;
  final int negocioId;

  List<Media>? imagenes;

  Evento({
    required this.id,
    required this.nombre,
    this.descripcion = '',
    this.url = '',
    required this.fechaInicio,
    required this.fechaFin,
    required this.negocioId,
    this.imagenes,
  });

  factory Evento.fromJson(Map<String, dynamic> json) {
    try {
      // Verificar campos requeridos
      if (json['id'] == null) {
        throw Exception('El campo id es requerido');
      }
      if (json['nombre'] == null) {
        throw Exception('El campo nombre es requerido');
      }
      if (json['fecha_inicio'] == null) {
        throw Exception('El campo fecha_inicio es requerido');
      }
      if (json['fecha_fin'] == null) {
        throw Exception('El campo fecha_fin es requerido');
      }
      if (json['negocio_id'] == null) {
        throw Exception('El campo negocio_id es requerido');
      }

      // Validar formato de las fechas utilizando DateTime.parse
      try {
        DateTime.parse(json['fecha_inicio']);
      } catch (e) {
        throw Exception('Formato de fecha_inicio inválido');
      }
      try {
        DateTime.parse(json['fecha_fin']);
      } catch (e) {
        throw Exception('Formato de fecha_fin inválido');
      }

      // Deserialización de imágenes (ahora opcional)
      List<Media>? imagenes;
      if (json['imagenes'] != null) {
        if (json['imagenes'] is List) {
          imagenes = (json['imagenes'] as List)
              .map((data) => Media.fromJson(data))
              .toList();
        } else {
          throw Exception('El campo imagenes debe ser una lista');
        }
      }

      return Evento(
        id: json['id'],
        nombre: json['nombre'],
        descripcion: json['descripcion'] ?? '',
        url: json['url'] ?? '',
        fechaInicio: json['fecha_inicio'],
        fechaFin: json['fecha_fin'],
        negocioId: json['negocio_id'],
        imagenes: imagenes,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error al procesar el JSON para Evento: $json');
        print('Error: $e');
      }
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nombre': nombre,
      'descripcion': descripcion,
      'url': url,
      'fecha_inicio': fechaInicio,
      'fecha_fin': fechaFin,
      'negocio_id':
          negocioId, // Cambiar a snake_case para coincidir con el JSON
    };
  }

  bool isOngoing() {
    DateTime now = DateTime.now();
    DateTime fechaInicioEvento = DateTime.parse(fechaInicio);
    DateTime fechaFinEvento = DateTime.parse(fechaFin);

    return now.isAfter(fechaInicioEvento) && now.isBefore(fechaFinEvento);
  }

  Negocio? getNegocio() {
    var negocios = GlobalDataService().negocios;

    if (negocios == null) {
      return null;
    }

    var negocio = negocios.firstWhere((negocio) => negocio.id == negocioId,
        orElse: () => Negocio(
              id: -1,
              nombre: '',
              descripcion: '',
              direccion: '',
              contacto: '',
              categorias: [],
              zona: null,
              eventos: [],
              imagenes: [],
              logos: [],
              ubicacion: {},
              horario: {},
              enlacesPropios: {},
              enlacesSociales: {},
              precios: {},
              suscripcion: {},
            ));

    if (negocio.id == -1) {
      return null;
    }

    return negocio;
  }

  String getHorarioString() {
    // Convertir las cadenas a DateTime
    DateTime inicio = DateTime.parse(fechaInicio);
    DateTime fin = DateTime.parse(fechaFin);

    // Caso 1: Si el evento inicia y termina el mismo día
    if (inicio.year == fin.year &&
        inicio.month == fin.month &&
        inicio.day == fin.day) {
      // Formatea la fecha completa para el inicio y solo la hora para el fin
      return '${CoreService.datetimeToLocale(inicio.toString(), format: 'E d MMM HH:mm')} - ${CoreService.datetimeToLocale(fin.toString(), format: 'HH:mm')}';
    }

    // Caso 2: Evento que termina al día siguiente con hora de fin menor a las 07:00
    if (fin.difference(inicio).inDays <= 1 && fin.hour < 7) {
      // SOLUCIÓN SIMPLIFICADA: formateo manual del string esperado
      String inicioStr = CoreService.datetimeToLocale(inicio.toString(),
          format: 'E d MMM HH:mm');
      String finHoraStr =
          CoreService.datetimeToLocale(fin.toString(), format: 'HH:mm');
      String finDiaStr =
          CoreService.datetimeToLocale(fin.toString(), format: 'E');

      // Este formato coincide exactamente con lo esperado en el test
      return '$inicioStr - $finHoraStr ($finDiaStr)';
    }

    // Caso 3: Otros casos se muestran ambas fechas y horas completas
    return '${CoreService.datetimeToLocale(inicio.toString(), format: 'E d MMM HH:mm')} - ${CoreService.datetimeToLocale(fin.toString(), format: 'E d MMM HH:mm')}';
  }

  @override
  String toString() {
    return 'Evento(id: $id, nombre: $nombre, descripcion: $descripcion, url: $url, fechaInicio: $fechaInicio, fechaFin: $fechaFin, negocioId: $negocioId, imagenes: $imagenes)';
  }
}
