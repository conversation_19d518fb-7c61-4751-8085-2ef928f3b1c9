<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Localidad;
use Illuminate\Auth\Access\HandlesAuthorization;

class LocalidadPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any localidades.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('localidad.list');
    }

    /**
     * Determine whether the user can view the localidad.
     */
    public function view(User $user, Localidad $localidad): bool
    {
        return $user->can('localidad.read');
    }

    /**
     * Determine whether the user can create localidades.
     */
    public function create(User $user): bool
    {
        return $user->can('localidad.create');
    }

    /**
     * Determine whether the user can update the localidad.
     */
    public function update(User $user, Localidad $localidad): bool
    {
        return $user->can('localidad.update');
    }

    /**
     * Determine whether the user can delete the localidad.
     */
    public function delete(User $user, Localidad $localidad): bool
    {
        return $user->can('localidad.delete');
    }

    /**
     * Determine whether the user can restore the localidad.
     */
    public function restore(User $user, Localidad $localidad): bool
    {
        return $user->can('localidad.restore');
    }

    /**
     * Determine whether the user can permanently delete the localidad.
     */
    public function forceDelete(User $user, Localidad $localidad): bool
    {
        return $user->can('localidad.force-delete');
    }
}
