// lib/pages/categorias.dart

import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:mia/config/colors.dart';
import 'package:mia/config/styles.dart';
import 'package:mia/models/categoria.dart';
import 'package:mia/models/media.dart';
import 'package:mia/services/core_service.dart';
import 'package:mia/services/global_data_service.dart';
import 'package:mia/widgets/app_bottom_navigation_bar.dart';
import 'package:mia/widgets/app_scaffold.dart';
import 'package:mia/widgets/item_categoria.dart';

class CategoriasPage extends StatefulWidget {
  const CategoriasPage({super.key});

  @override
  State<CategoriasPage> createState() => _CategoriasPageState();
}

class _CategoriasPageState extends State<CategoriasPage> {
  // int _tapCount = 0;
  final bool _secretModeActive = false;
  // final int _requiredTaps = 7;
  // final LocalStorageService _storageService = LocalStorageService();

  // static const String _secretModeKey = 'secret_mode_status';

  final String _baseUri =
      dotenv.env['ADMIN_PANEL_URL'] ?? 'http://10.0.2.2/mia/admin';

  @override
  void initState() {
    super.initState();
    // _loadSecretModeStatus();
  }

  // Cargar el estado del modo secreto usando LocalStorageService
  // Future<void> _loadSecretModeStatus() async {
  //   try {
  //     final data = await _storageService.getData(_secretModeKey);
  //     if (data != null && data.isNotEmpty) {
  //       setState(() {
  //         _secretModeActive = data[0]['active'] ?? false;
  //       });
  //     }
  //   } catch (e) {
  //     print('Error cargando el modo secreto: $e');
  //   }
  // }

  // Guardar el estado del modo secreto usando LocalStorageService
  // Future<void> _saveSecretModeStatus(bool value) async {
  //   try {
  //     final secretModeData = [
  //       {'active': value}
  //     ];
  //     await _storageService.setData(_secretModeKey, secretModeData);
  //   } catch (e) {
  //     print('Error guardando el modo secreto: $e');
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    List<Categoria>? categorias = GlobalDataService().categorias;
    categorias?.sort((a, b) => a.orden.compareTo(b.orden));

    // Filtrar las categorías que NO tienen negocios asociados
    List<Categoria>? categoriasSinNegocios = categorias?.where((cat) {
      return GlobalDataService().negocios?.any((negocio) {
            return negocio.categorias?.contains(cat) ?? false;
          }) ==
          false;
    }).toList();

    return AppScaffold(
      floatingActionButton: _secretModeActive
          ? FloatingActionButton(
              onPressed: () {
                // Acción del botón secreto
                CoreService.launchWeb(_baseUri);
              },
              backgroundColor: AppColors.current.primaryColor,
              child: const Icon(Icons.vpn_key),
            )
          : null,
      bottomNavigationBar: const AppBottomNavigationBar(currentIndex: 0),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            GestureDetector(
              onTap: () {},
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 8.0, vertical: 16.0),
                child: Text(
                  'Bienvenid@',
                  style: AppStyles.getStyle(
                    context,
                    'h3',
                    fontWeight: 'bold',
                    color: AppColors.current.secondaryColor,
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Text(
                '¿Qué quieres hacer hoy?',
                style: AppStyles.getStyle(context, 'h5',
                    fontWeight: 'bold', color: AppColors.current.textColor),
              ),
            ),
            const SizedBox(height: 15),
            Padding(
              padding: _secretModeActive
                  ? const EdgeInsets.fromLTRB(0, 0, 0, 32.0)
                  : const EdgeInsets.all(0),
              child: GridView.builder(
                shrinkWrap:
                    true, // Para que el grid se expanda según su contenido
                physics:
                    const NeverScrollableScrollPhysics(), // Deshabilitar scroll interno
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  childAspectRatio: 1.0,
                  crossAxisSpacing: 16.0,
                  mainAxisSpacing: 16.0,
                ),
                itemCount: categorias?.length ?? 0,
                itemBuilder: (context, index) {
                  final categoria = categorias![index];
                  // Se determina si la categoría tiene negocios
                  bool activo =
                      !(categoriasSinNegocios?.contains(categoria) ?? false);
                  Media? icono;

                  if (categoria.iconos != null &&
                      categoria.iconos!.isNotEmpty) {
                    icono = categoria.iconos!.first;
                  }

                  return CategoryItem(
                    nombre: categoria.nombre,
                    icono: icono,
                    categoria: categoria,
                    activo: activo,
                  );
                },
              ),
            ),
            const SizedBox(height: 30),
          ],
        ),
      ),
    );
  }
}
