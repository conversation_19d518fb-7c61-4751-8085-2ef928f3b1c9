// lib/widgets/filtros/filtro_negocio_categoria.dart

import 'package:flutter/material.dart';
import 'package:mia/models/categoria.dart';

class FiltroNegocioCategoria extends StatelessWidget {
  final List<Categoria> categorias;
  final int? selectedCategoryId;
  final Function(int?) onCategorySelected;

  const FiltroNegocioCategoria({
    super.key,
    required this.categorias,
    required this.selectedCategoryId,
    required this.onCategorySelected,
  });

  @override
  Widget build(BuildContext context) {
    // Crear una copia de la lista y ordenarla por el campo 'orden'
    final sortedCategorias = List<Categoria>.from(categorias)
      ..sort((a, b) => a.orden.compareTo(b.orden));

    return SizedBox(
      height: 48,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: sortedCategorias.map((cat) {
          final sel = selectedCategoryId == cat.id;
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            child: ChoiceChip(
              label: Text(cat.nombre),
              selected: sel,
              onSelected: (yes) => onCategorySelected(yes ? cat.id : null),
            ),
          );
        }).toList(),
      ),
    );
  }
}
