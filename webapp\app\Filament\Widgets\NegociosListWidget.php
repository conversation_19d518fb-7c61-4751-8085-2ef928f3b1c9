<?php

namespace App\Filament\Widgets;

use Filament\Tables;
use App\Models\Negocio;
use Filament\Tables\Table;
use App\Enums\EstadoSuscripcion;
use App\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Builder;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Support\Facades\Auth;

class NegociosListWidget extends BaseWidget
{
    protected static ?string $heading = 'Mis Negocios';

    protected int|string|array $columnSpan = 'full';

    public ?Collection $negocios = null;

    public function mount($negocios = null)
    {
        $this->negocios = $negocios;
    }

    public function table(Table $table): Table
    {
        /** @var User */
        $user = Auth::user();

        return $table

            ->query(
                // Si hay negocios pasados como parámetro, usarlos
                $this->negocios !== null
                    ? Negocio::whereIn('id', $this->negocios->pluck('id'))->where('user_id', $user->id)
                    : Negocio::query()->where('user_id', $user->id)
            )
            ->columns([
                Tables\Columns\TextColumn::make('suscripcion.status')
                    ->label('Suscripción')
                    ->badge()
                    ->color(fn(EstadoSuscripcion $state): string => match ($state) {
                        EstadoSuscripcion::ACTIVE => 'success',
                        EstadoSuscripcion::INACTIVE => 'danger',
                        default => 'warning',
                    })
                    // anchura minima
                    ->width('100px')
                    ->formatStateUsing(fn(EstadoSuscripcion $state): string => $state->label()),
                Tables\Columns\TextColumn::make('nombre')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('categorias.nombre')
                    ->label('Categorías')
                    ->badge()
                    ->searchable(),
                // Tables\Columns\TextColumn::make('zona.localidad.nombre')
                //     ->label('Localidad')
                //     ->searchable()
                //     ->sortable(),
                Tables\Columns\TextColumn::make('zona.nombre')
                    ->label('Zona')
                    ->searchable()
                    ->sortable(),
            ])
            ->defaultSort('nombre', 'asc')
            ->actions([
                Tables\Actions\Action::make('ver')
                    ->label('Editar')
                    ->icon('heroicon-o-pencil-square')
                    ->modal(true)
                    ->url(fn(Negocio $record): string => route('filament.admin.resources.negocios.edit', [
                        'record' => $record->id,
                        'origin' => 'dashboard',
                    ])),
            ])

            ->bulkActions([
                // Puedes agregar acciones en masa si corresponde
            ]);
    }
}
