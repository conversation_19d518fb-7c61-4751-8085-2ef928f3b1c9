<?php

namespace App\Filament\Resources\AppUserResource\Pages;

use App\Models\User;
use Filament\Actions;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\StoreClienteRequest;
use Filament\Resources\Pages\CreateRecord;
use App\Filament\Resources\AppUserResource;

class CreateAppUser extends CreateRecord
{
    protected static string $resource = AppUserResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function handleRecordCreation(array $data): Model
    {
        /** @var User */
        $user = Auth::user();

        if (!$user->can('user.create')) {
            Notification::make()
                ->title('No tienes permiso para crear un usuario.')
                ->danger()
                ->send();
            abort(403, 'No tienes permiso para crear un usuario.');
        }

        // Validar datos usando las reglas de StoreClienteRequest
        $request = new StoreClienteRequest();
        $validator = Validator::make($data, $request->rules(), $request->messages());

        try {
            $validatedData = $validator->validate();

            $validatedData['password'] = bcrypt($validatedData['password']);

            // Crea el registro
            $model = static::getModel()::create($validatedData);
            $model->syncRoles([Role::where('name', 'usuario')->first()->id]);

            return $model;
        } catch (\Illuminate\Validation\ValidationException $e) {
            Notification::make()
                ->title('Error al crear el usuario.')
                // Mostrar en el body una lista de los errores de validación a partir de StoreClienteRequest::messages()
                ->body(function () use ($e) {
                    $errors = $e->validator->errors();
                    $errorMessages = [];
                    foreach ($errors->all() as $error) {
                        $errorMessages[] = $error;
                    }
                    return implode('<br>', $errorMessages);
                })
                ->danger()
                ->send();
            throw $e;
        }
    }
}
