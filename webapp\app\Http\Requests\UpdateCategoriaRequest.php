<?php

namespace App\Http\Requests;

use App\Models\Categoria;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class UpdateCategoriaRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Hay parametro record => Viene desde EditCategoria.handleRecordUpdate
        if ($this->input('record')) {
            $categoria = Categoria::find($this->input('record'));
        } else {
            $categoria = $this->route('categoria');
        }


        return $this->user()->can('update', $categoria);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {

        // Hay parametro record => Viene desde EditCategoria.handleRecordUpdate
        if ($this->input('record')) {
            $categoriaActual = Categoria::find($this->input('record'));
        } else {
            $categoriaActual = $this->route('categoria');
        }

        return [
            'nombre' => 'required|string|max:255',
            'descripcion' => 'nullable|string|max:255',
            'parent_id' => [
                'nullable',
                'exists:categorias,id',
                function ($attribute, $value, $fail) use ($categoriaActual) {
                    $categoriaPosiblePadre = Categoria::find($value);
                    if ($categoriaPosiblePadre->hasAntecesor($categoriaActual)) {
                        $fail("La categoría no puede tener como antecesor a una de sus subcategorias");
                    }
                }
            ],
            'visible' => 'sometimes|boolean',
            'permitir_eventos' => 'sometimes|boolean',
            'permitir_promociones' => 'sometimes|boolean',
        ];
    }
}
