// lib/widgets/card_negocio_fav.dart

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mia/services/core_service.dart';
import 'package:mia/widgets/botones/fav_button.dart';
import 'package:provider/provider.dart';
import 'package:mia/config/colors.dart';
import 'package:mia/config/styles.dart';
import 'package:mia/models/media.dart';
import 'package:mia/models/negocio.dart';
import 'package:mia/providers/favorites_provider.dart';
import 'package:mia/widgets/imagenes/horizontal_image_slider.dart';

class CardNegocioFav extends StatelessWidget {
  final Negocio negocio;

  const CardNegocioFav({super.key, required this.negocio});

  @override
  Widget build(BuildContext context) {
    final favProv = context.watch<FavoritesProvider>();

    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10.0)),
      child: Stack(
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 4.0, vertical: 8.0),
            decoration: BoxDecoration(
                // gradient: AppColors.current.reverseGradient,
                color: AppColors.current.surfaceColor,
                borderRadius: BorderRadius.circular(10.0)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildImagen(negocio),
                // Título y subtítulo
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 4.0, vertical: 8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(negocio.nombre,
                                    style: AppStyles.getStyle(context, 'h6',
                                        fontWeight: 'bold')),
                              ],
                            ),
                          ),
                          FavButton(
                            negocio: negocio,
                            favoritesProvider: favProv,
                            size: 32,
                          ),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                negocio.horario == null
                                    ? Container()
                                    : _buildHeaderHorario(
                                        negocio.isOpenNow(),
                                        negocio.getAllHorariosHoy() ?? '',
                                        context,
                                        mostarLink: false),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 10),
                      Divider(height: 1, color: AppColors.current.accentColor),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          TextButton.icon(
                            onPressed: () {
                              Navigator.pushNamed(
                                context,
                                '/negocio',
                                arguments: negocio,
                              );
                            },
                            icon: Icon(Icons.info,
                                color: AppColors.current.accentColor),
                            label: Text(
                              'Ver más',
                              style: TextStyle(
                                  color: AppColors.current.accentColor),
                            ),
                          ),
                          TextButton.icon(
                            onPressed: () => CoreService.launchMap(negocio),
                            icon: Icon(Icons.directions,
                                color: AppColors.current.accentColor),
                            label: Text(
                              'Cómo llegar',
                              style: TextStyle(
                                  color: AppColors.current.accentColor),
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImagen(Negocio negocio) {
    if (negocio.imagenes != null && negocio.imagenes!.isNotEmpty) {
      return ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(10.0),
          topRight: Radius.circular(10.0),
        ),
        child: HorizontalImageSlider(mediaList: negocio.imagenes!),
      );
    } else {
      return ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(10.0),
          topRight: Radius.circular(10.0),
        ),
        child: Media.getDefaultImage(width: double.infinity, height: 200),
      );
    }
  }

  Widget _buildHeaderHorario(
      bool isOpen, String horarioHoy, BuildContext context,
      {bool mostarLink = true}) {
    String horarioConDia = horarioHoy;
    if (horarioHoy.isNotEmpty) {
      final List<String> weekdays = [
        'lunes',
        'martes',
        'miércoles',
        'jueves',
        'viernes',
        'sábado',
        'domingo'
      ];
      final now = DateTime.now();
      final currentDay = weekdays[now.weekday - 1];
      horarioConDia = '${_capitalize(currentDay)} - $horarioHoy';
    }

    String debugTime = '';
    if (kDebugMode) {
      final now = DateTime.now();
      debugTime =
          ' (${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')})';
    }

    return Wrap(
      crossAxisAlignment: WrapCrossAlignment.center,
      spacing: 4,
      runSpacing: 4,
      children: [
        Icon(Icons.access_time, size: 16, color: AppColors.current.accentColor),
        Text(
          isOpen ? 'Abierto' : 'Cerrado',
          style: AppStyles.getStyle(
            context,
            'base',
            fontWeight: 'bold',
            color: isOpen
                ? AppColors.current.successColor
                : AppColors.current.errorColor,
          ),
        ),
        if (kDebugMode)
          Text(
            debugTime,
            style: AppStyles.getStyle(context, 'base',
                color: AppColors.current.infoColor),
          ),
        Text(
          horarioConDia,
          style: AppStyles.getStyle(context, 'base'),
        ),
      ],
    );
  }

  String _capitalize(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1);
  }
}
