<?php

namespace Tests\Feature\Web;

use Tests\BaseTest;
use App\Models\User;
use Livewire\Livewire;
use App\Models\Negocio;
use App\Models\Categoria;
use App\Models\Suscripcion;
use App\Enums\EstadoSuscripcion;
use Illuminate\Http\UploadedFile;
use Spatie\Permission\Models\Role;
use PHPUnit\Framework\Attributes\Group;
use Filament\Tables\Actions\DeleteAction;
use App\Filament\Resources\NegocioResource;
use Database\Seeders\RolesPermissionSeeder;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Filament\Resources\NegocioResource\Pages\EditNegocio;
use App\Filament\Resources\NegocioResource\Pages\ListNegocios;
use App\Filament\Resources\NegocioResource\Pages\CreateNegocio;

#[Group('negocio')]
#[Group('webapp')]
#[Group('negocio_webapp')]
class NegocioTest extends BaseTest
{
    use RefreshDatabase;
    use WithFaker;

    private array $usuarios;

    // Configuración inicial para cada prueba
    protected function setUp(): void
    {
        parent::setUp();

        $this->seed(RolesPermissionSeeder::class);

        foreach (Role::all() as $rol) {
            $user = User::factory()->create();
            $user->assignRole($rol->name);

            $this->usuarios[$rol->name] = [
                'usuario' => $user,
            ];
        }
    }

    public function test_el_sistema_muestra_las_paginas_de_negocio_correctamente_segun_el_rol_del_usuario(): void
    {

        // 🟢 Test positivos

        $negocio = Negocio::factory()
            ->conSuscripcion()
            ->create([
                'user_id' => $this->usuarios['cliente']['usuario']
            ]);

        $test_roles = $this->_getRolesPositivos(['negocio.list', 'negocio.create', 'negocio.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $this->get(\App\Filament\Resources\NegocioResource::getUrl('index'))->assertSuccessful();
            $this->get(\App\Filament\Resources\NegocioResource::getUrl('create'))->assertSuccessful();
            $this->get(\App\Filament\Resources\NegocioResource::getUrl('edit', ['record' => $negocio->id]))->assertSuccessful();
        }


        // 🔴 Test negativos

        $test_roles = $this->_getRolesNegativos(['negocio.list', 'negocio.create', 'negocio.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $this->get(NegocioResource::getUrl('index'))->assertForbidden();
            $this->get(NegocioResource::getUrl('create'))->assertForbidden();
            $this->get(NegocioResource::getUrl('edit', ['record' => $negocio->id]))->assertForbidden();
        }
    }


    // public function __test_el_sistema_crea_un_nuevo_negocio_solo_con_datos_validos(): void
    // {
    //     // 🟢 Test positivos
    //     $test_roles = $this->_getRolesPositivos(['negocio.create']);

    //     // Crear 3 categorías
    //     $cat1 = Categoria::factory()->create();
    //     $cat2 = Categoria::factory()->create();
    //     $cat3 = Categoria::factory()->create();

    //     foreach ($test_roles as $rol) {
    //         $user = $this->usuarios[$rol]['usuario'];

    //         $this->actingAs($user);

    //         $newData = Negocio::factory()->make()->toArray();

    //         // Escoger 2 categorías para asociar
    //         $newData['categorias'] = [$cat1->id, $cat3->id];

    //         $test = Livewire::test(CreateNegocio::class);

    //         $this->assertDatabaseCount(Negocio::class, 0);

    //         $statusValue = $this->faker->randomElement([
    //             EstadoSuscripcion::INACTIVE->value,
    //             EstadoSuscripcion::ACTIVE->value,
    //         ]);

    //         // Preparar la fecha esperada
    //         // si es "active" => endOfYear, si es "inactive" => now()
    //         if ($statusValue === EstadoSuscripcion::ACTIVE->value) {
    //             // endOfYear sin microsegundos
    //             $expectedEndsAt = now()->endOfYear()->startOfDay();
    //         } else {
    //             // forzamos sin microsegundos
    //             $expectedEndsAt = now()->copy()->startOfSecond();
    //         }

    //         // Rellenar campos simples
    //         $test->fillForm([
    //             'nombre' => $newData['nombre'],
    //             'descripcion' => $newData['descripcion'],
    //             'direccion' => $newData['direccion'],
    //             'ubicacion' => $newData['ubicacion'],
    //             'horario' => $newData['horario'],
    //             'enlaces_propios' => $newData['enlaces_propios'],
    //             'contacto' => $newData['contacto'],
    //             'categorias' => $newData['categorias'],
    //             'zona_id' => $newData['zona_id'],
    //             'user_id' => $newData['user_id'],
    //             'suscripcion' => $statusValue,
    //         ]);

    //         // Rellenar Repeater precios manualmente
    //         $test->set('data.precios', [
    //             [
    //                 'categoria' => 'Entradas',
    //                 'productos' => [
    //                     'Entrada General' => '10.00',
    //                     'Entrada VIP' => '20.00',
    //                 ],
    //             ],
    //             [
    //                 'categoria' => 'Bebidas',
    //                 'productos' => [
    //                     'Refresco' => '2.50',
    //                     'Agua' => '1.50',
    //                 ],
    //             ],
    //         ]);

    //         $test->set('data.enlaces_sociales', $newData['enlaces_sociales']);

    //         $test->call('create');

    //         if ($test->errors()->isNotEmpty()) {
    //             // dd($test->errors());
    //         }

    //         $test->assertHasNoFormErrors();


    //         $this->assertDatabaseCount(Negocio::class, 1);

    //         $newData['precios'] = [
    //             [
    //                 'categoria' => 'Entradas',
    //                 'productos' => [
    //                     'Entrada General' => '10.00',
    //                     'Entrada VIP' => '20.00',
    //                 ],
    //             ],
    //             [
    //                 'categoria' => 'Bebidas',
    //                 'productos' => [
    //                     'Refresco' => '2.50',
    //                     'Agua' => '1.50',
    //                 ],
    //             ],
    //         ];


    //         // Verificar que el negocio se creó
    //         $this->assertDatabaseHas(Negocio::class, [
    //             'nombre' => $newData['nombre'],
    //             'descripcion' => $newData['descripcion'],
    //             'direccion' => $newData['direccion'],
    //             'ubicacion' => json_encode($newData['ubicacion']),
    //             'horario' => json_encode($newData['horario']),
    //             'enlaces_sociales' => json_encode($newData['enlaces_sociales']),
    //             'enlaces_propios' => json_encode($newData['enlaces_propios']),
    //             // 'precios' => json_encode($newData['precios']),
    //             'contacto' => $newData['contacto'],
    //             'zona_id' => $newData['zona_id'],
    //             'user_id' => $newData['user_id'],
    //         ]);

    //         $negocioCreado = Negocio::latest()->first();
    //         $this->assertNotNull($negocioCreado);

    //         // Verificar la tabla pivote
    //         foreach ($newData['categorias'] as $catId) {
    //             $this->assertDatabaseHas('categoria_negocio', [
    //                 'negocio_id' => $negocioCreado->id,
    //                 'categoria_id' => $catId,
    //             ]);
    //         }

    //         // Ahora verificar la suscripción
    //         // 1) Recuperamos la suscripción creada
    //         $negocioCreado = Negocio::latest()->first();
    //         $suscripcion = $negocioCreado->suscripcion;

    //         $this->assertNotNull($suscripcion, 'La suscripción no se creó');
    //         $this->assertEquals($statusValue, $suscripcion->status->value);

    //         // 2) Verificar ends_at
    //         // Comparamos con Carbon sin microsegundos
    //         if ($statusValue === EstadoSuscripcion::ACTIVE->value) {
    //             // Esperamos endOfYear
    //             $this->assertTrue(
    //                 $suscripcion->ends_at->isSameDay($expectedEndsAt),
    //                 "ends_at no coincide con endOfYear. ends_at=" . $suscripcion->ends_at
    //             );
    //         } else {
    //             // Esperamos now()
    //             $this->assertTrue(
    //                 $suscripcion->ends_at->isSameDay($expectedEndsAt),
    //                 "ends_at no coincide con now(). ends_at=" . $suscripcion->ends_at
    //             );
    //         }

    //         // borrar todos los negocios
    //         Negocio::truncate();
    //     }

    //     // 🔴 Test negativos
    //     // Aquí probamos los errores en el Repeater de precios

    //     $test_cases = [
    //         // caso 1: campos vacíos
    //         [
    //             'payload' => [],
    //             'expectedInvalid' => ['nombre', 'direccion', 'ubicacion', 'contacto', 'zona_id', 'user_id', 'suscripcion']
    //         ],

    //         // caso 2: campos vacíos en el Repeater de enlaces
    //         [
    //             'payload' => [
    //                 'enlaces_sociales' => [
    //                     [
    //                         'plataforma' => '',
    //                         'url' => '',
    //                     ],
    //                 ],
    //             ],
    //             'expectedInvalid' => ['enlaces_sociales.0.plataforma', 'enlaces_sociales.0.url']
    //         ],
    //         // caso 3: campos vacíos en el Repeater de precios
    //         // [
    //         //     'payload' => [
    //         //         'precios' => [
    //         //             [
    //         //                 'categoria' => '',
    //         //                 'productos' => [
    //         //                     ['' => ''],
    //         //                 ],
    //         //             ],
    //         //         ],
    //         //     ],
    //         //     'expectedInvalid' => ['precios.0.categoria', /* TODO: Comprobar que no puede ser vacia ni la clave ni el valor */]
    //         // ],
    //         // caso 4: campos vacíos en el Repeater de enlaces propios
    //         // [
    //         //     'payload' => [
    //         //         'enlaces_propios' => [
    //         //             '' => '',
    //         //         ],
    //         //     ],
    //         //     'expectedInvalid' => ['enlaces_propios.0']
    //         // ],
    //         // caso 5: Valor de suscripcion inválido
    //         [
    //             'payload' => [
    //                 'suscripcion' => 9999,
    //             ],
    //             'expectedInvalid' => ['suscripcion']
    //         ],
    //         [
    //             'payload' => [
    //                 'categorias' => [],
    //             ],
    //             'expectedInvalid' => ['categorias'], // si tu resource requiere al menos 1
    //         ],
    //         // [
    //         //     'payload' => [
    //         //         'categorias' => [999999], // ID inexistente
    //         //     ],
    //         //     'expectedInvalid' => ['categorias.0'],
    //         // ],

    //     ];

    //     foreach ($test_roles as $rol) {
    //         $user = $this->usuarios[$rol]['usuario'];
    //         $this->actingAs($user);

    //         $test = Livewire::test(CreateNegocio::class);

    //         foreach ($test_cases as $case) {
    //             $test->fillForm($case['payload'])
    //                 ->call('create')
    //                 ->assertHasFormErrors($case['expectedInvalid']);

    //             // verificar que no se han creado registros en la base de datos
    //             $this->assertDatabaseCount(Negocio::class, 0);
    //             $this->assertDatabaseCount(Suscripcion::class, 0);
    //         }
    //     }
    // }

    public function test_el_sistema_crea_un_nuevo_negocio_con_datos_validos(): void
    {
        // 🟢 Test positivos
        $test_roles = $this->_getRolesPositivos(['negocio.create']);

        // Crear 3 categorías
        $cat1 = Categoria::factory()->create();
        $cat2 = Categoria::factory()->create();
        $cat3 = Categoria::factory()->create();

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];
            $this->actingAs($user);

            $newData = Negocio::factory()->make()->toArray();
            $newData['categorias'] = [$cat1->id, $cat3->id];

            // Simular la carga de archivos de imagen
            $maxImagenes = 5;
            $files = [];
            for ($j = 0; $j < $maxImagenes; $j++) {
                $files[] = UploadedFile::fake()->image('imagen' . $j . '.jpg', 100, 100);
            }

            // Simular la carga de archivos de logo
            $maxLogos = 1;
            $logoFiles = [];
            for ($j = 0; $j < $maxLogos; $j++) {
                $logoFiles[] = UploadedFile::fake()->image('logo' . $j . '.jpg', 100, 100);
            }

            $test = Livewire::test(CreateNegocio::class);

            $this->assertDatabaseCount(Negocio::class, 0);

            $statusValue = $this->faker->randomElement([
                EstadoSuscripcion::INACTIVE->value,
                EstadoSuscripcion::ACTIVE->value,
            ]);

            $expectedEndsAt = ($statusValue === EstadoSuscripcion::ACTIVE->value)
                ? now()->endOfYear()->startOfDay()
                : now()->copy()->startOfSecond();

            $test->fillForm([
                'nombre' => $newData['nombre'],
                'descripcion' => $newData['descripcion'],
                'direccion' => $newData['direccion'],
                'ubicacion' => $newData['ubicacion'],
                'horario' => $newData['horario'],
                'enlaces_propios' => $newData['enlaces_propios'],
                'contacto' => $newData['contacto'],
                'contactos_secundarios' => $newData['contactos_secundarios'],
                'categorias' => $newData['categorias'],
                'zona_id' => $newData['zona_id'],
                'user_id' => $newData['user_id'],
                'suscripcion' => $statusValue,
            ]);

            $test->set('data.imagenes', $files);
            $test->set('data.logos', $logoFiles);

            $test->set('data.precios', [
                [
                    'categoria' => 'Entradas',
                    'productos' => [
                        'Entrada General' => '10.00',
                        'Entrada VIP' => '20.00',
                    ],
                ],
                [
                    'categoria' => 'Bebidas',
                    'productos' => [
                        'Refresco' => '2.50',
                        'Agua' => '1.50',
                    ],
                ],
            ]);

            $test->set('data.enlaces_sociales', $newData['enlaces_sociales']);
            $test->set('data.enlaces_propios', $newData['enlaces_propios']);
            $test->set('data.contactos_secundarios', $newData['contactos_secundarios']);

            $test->call('create');

            $test->assertHasNoFormErrors();
            $this->assertDatabaseCount(Negocio::class, 1);

            $this->assertDatabaseHas(Negocio::class, [
                'nombre' => $newData['nombre'],
                'descripcion' => $newData['descripcion'],
                'direccion' => $newData['direccion'],
                'ubicacion' => json_encode($newData['ubicacion']),
                'horario' => json_encode($newData['horario']),
                'enlaces_sociales' => json_encode($newData['enlaces_sociales']),
                'enlaces_propios' => json_encode($newData['enlaces_propios']),
                'contacto' => $newData['contacto'],
                'contactos_secundarios' => json_encode($newData['contactos_secundarios']),
                'zona_id' => $newData['zona_id'],
                'user_id' => $newData['user_id'],
            ]);

            $negocioCreado = Negocio::latest()->first();
            $this->assertNotNull($negocioCreado);

            foreach ($newData['categorias'] as $catId) {
                $this->assertDatabaseHas('categoria_negocio', [
                    'negocio_id' => $negocioCreado->id,
                    'categoria_id' => $catId,
                ]);
            }

            // Comprobar imagenes y logos
            $this->assertTrue($negocioCreado->hasMedia('imagenes'));
            $this->assertEquals($maxImagenes, $negocioCreado->getMedia('imagenes')->count());
            $this->assertTrue($negocioCreado->hasMedia('logos'));
            $this->assertEquals($maxLogos, $negocioCreado->getMedia('logos')->count());


            $suscripcion = $negocioCreado->suscripcion;
            $this->assertNotNull($suscripcion);
            $this->assertEquals($statusValue, $suscripcion->status->value);

            $this->assertTrue(
                $suscripcion->ends_at->isSameDay($expectedEndsAt),
                "ends_at no coincide. ends_at=" . $suscripcion->ends_at
            );

            Negocio::truncate();
        }
    }

    public function test_el_sistema_no_crea_un_negocio_con_datos_invalidos(): void
    {
        // 🔴 Test negativos
        $test_roles = $this->_getRolesPositivos(['negocio.create']);

        $test_cases = [
            [
                'payload' => [],
                'expectedInvalid' => ['nombre', 'direccion', 'ubicacion', 'contacto', 'zona_id', 'user_id', 'suscripcion']
            ],
            [
                'payload' => [
                    'enlaces_sociales' => [
                        ['plataforma' => '', 'url' => '']
                    ]
                ],
                'expectedInvalid' => ['enlaces_sociales.0.plataforma', 'enlaces_sociales.0.url']
            ],
            [
                'payload' => [
                    'suscripcion' => 9999
                ],
                'expectedInvalid' => ['suscripcion']
            ],
            [
                'payload' => [
                    'categorias' => []
                ],
                'expectedInvalid' => ['categorias']
            ],
        ];

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];
            $this->actingAs($user);

            $test = Livewire::test(CreateNegocio::class);

            foreach ($test_cases as $case) {
                $test->fillForm($case['payload'])
                    ->call('create')
                    ->assertHasFormErrors($case['expectedInvalid']);

                $this->assertDatabaseCount(Negocio::class, 0);
                $this->assertDatabaseCount(Suscripcion::class, 0);
            }
        }
    }

    public function test_el_sistema_edita_correctamente_un_negocio_con_datos_validos(): void
    {
        $cat1 = Categoria::factory()->create();
        $cat2 = Categoria::factory()->create();
        $cat3 = Categoria::factory()->create();


        // 🟢 Test positivos
        $negocio = Negocio::factory()
            ->conSuscripcion()
            ->create([
                'enlaces_propios' => [],
            ]);

        $test_roles = $this->_getRolesPositivos(['negocio.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $negocio->refresh();
            $newData = Negocio::factory()->make()->toArray();
            // Escoger 2 categorías para asociar
            $newData['categorias'] = [$cat1->id, $cat3->id];

            // Simular la carga de archivos de imagen
            $numImagenes = 1; // Parece que solo funciona con 1 porque al hacer set('data.imagenes', $imagenes) no se borran las imagenes anteriores
            $imagenes = [];
            for ($j = 0; $j < $numImagenes; $j++) {
                $imagenes[] = UploadedFile::fake()->image('imagen' . $j . '.jpg', 100, 100);
            }

            // Verificar que tiene un icono inicialmente
            $this->assertTrue($negocio->hasMedia('imagenes'));
            $initialMediaCount = $negocio->getMedia('imagenes')->count();

            // Simular la carga de archivos de logo
            $numLogos = 1;
            $logos = [];
            for ($j = 0; $j < $numLogos; $j++) {
                $logos[] = UploadedFile::fake()->image('logo' . $j . '.jpg', 100, 100);
            }

            $this->assertTrue($negocio->hasMedia('logos'));
            $initialLogoCount = $negocio->getMedia('logos')->count();


            // Obtener la suscripción actual
            $suscripcion = $negocio->suscripcion;
            $this->assertNotNull($suscripcion, 'El negocio no tiene suscripción asociada');

            // Generar un nuevo estado aleatorio de suscripción
            $statusValue = $this->faker->randomElement([
                EstadoSuscripcion::INACTIVE->value,
                EstadoSuscripcion::ACTIVE->value,
            ]);

            // Determinar la fecha esperada de finalización
            $expectedEndsAt = $statusValue === EstadoSuscripcion::ACTIVE->value
                ? now()->endOfYear()->startOfDay()
                : now()->copy()->startOfSecond();

            // Inicia el test de Livewire en la página de edición
            $test = Livewire::test(EditNegocio::class, ['record' => $negocio->id]);

            // Verificamos que el formulario se inicializa con los datos actuales
            $test->assertFormSet([
                'nombre'       => $negocio->nombre,
                'descripcion'  => $negocio->descripcion,
                'direccion'    => $negocio->direccion,
                'ubicacion'    => $negocio->ubicacion,
                'horario'      => $negocio->horario,
                'enlaces_propios' => $negocio->enlaces_propios,
                'contacto'     => $negocio->contacto,
                'categorias'   => $negocio->categorias->pluck('id')->toArray(),
                'zona_id'      => $negocio->zona_id,
                'user_id'      => $negocio->user_id,
                'suscripcion'  => $suscripcion->status->value,
            ]);

            // Rellenamos los campos básicos
            $test->fillForm([
                'nombre'       => $newData['nombre'],
                'descripcion'  => $newData['descripcion'],
                'direccion'    => $newData['direccion'],
                'ubicacion'    => $newData['ubicacion'],
                'horario'      => $newData['horario'],
                'enlaces_propios' => $newData['enlaces_propios'],
                'contacto'     => $newData['contacto'],
                'categorias'   => $newData['categorias'],
                'zona_id'      => $newData['zona_id'],
                'user_id'      => $newData['user_id'],
                'suscripcion'  => $statusValue,
            ]);

            $test->set('data.imagenes', $imagenes);
            $test->set('data.logos', $logos);

            // Rellenamos el Repeater 'precios' de forma manual
            $test->set('data.precios', [
                [
                    'categoria' => 'Entradas',
                    'productos' => [
                        'Entrada General' => '10.00',
                        'Entrada VIP' => '20.00',
                    ],
                ],
                [
                    'categoria' => 'Bebidas',
                    'productos' => [
                        'Refresco' => '2.50',
                        'Agua' => '1.50',
                    ],
                ],
            ]);

            $test->set('data.enlaces_sociales', $newData['enlaces_sociales']);

            // Ejecutamos la acción de guardado y verificamos que no haya errores
            $test->call('save')->assertHasNoFormErrors();

            // Verificamos que la base de datos conserva 1 registro de Negocio
            $this->assertDatabaseCount(Negocio::class, 1);

            $newData['precios'] = [
                [
                    'categoria' => 'Entradas',
                    'productos' => [
                        'Entrada General' => '10.00',
                        'Entrada VIP' => '20.00',
                    ],
                ],
                [
                    'categoria' => 'Bebidas',
                    'productos' => [
                        'Refresco' => '2.50',
                        'Agua' => '1.50',
                    ],
                ],
            ];


            // Verificamos que los campos se hayan actualizado
            $this->assertDatabaseHas(Negocio::class, [
                'nombre'       => $newData['nombre'],
                'descripcion'  => $newData['descripcion'],
                'direccion'    => $newData['direccion'],
                'horario'      => json_encode($newData['horario']),
                // 'precios'      => json_encode($newData['precios']),
                'enlaces_sociales'      => json_encode($newData['enlaces_sociales']),
                'enlaces_propios'      => json_encode(array_merge($negocio->enlaces_propios, $newData['enlaces_propios'])),
                'contacto'     => $newData['contacto'],
                'zona_id'      => $newData['zona_id'],
                'user_id'      => $newData['user_id'],
            ]);

            // Verificar la tabla pivote
            foreach ($newData['categorias'] as $catId) {
                $this->assertDatabaseHas('categoria_negocio', [
                    'negocio_id' => $negocio->id,
                    'categoria_id' => $catId,
                ]);
            }

            // Verificar imagenes y logos
            $this->assertTrue($negocio->hasMedia('imagenes'));
            $this->assertEquals($numImagenes, $negocio->getMedia('imagenes')->count());
            $this->assertTrue($negocio->hasMedia('logos'));
            $this->assertEquals($numLogos, $negocio->getMedia('logos')->count());


            // 📌 **Verificar la suscripción después de la edición**
            $negocio->refresh();
            $suscripcion = $negocio->suscripcion;

            $this->assertNotNull($suscripcion, 'La suscripción no se mantuvo después de la edición');
            $this->assertEquals($statusValue, $suscripcion->status->value);

            // Verificar que ends_at se haya actualizado correctamente
            $this->assertTrue(
                $suscripcion->ends_at->isSameDay($expectedEndsAt),
                "ends_at no coincide con la fecha esperada. ends_at=" . $suscripcion->ends_at
            );
        }

        // 🔴 Test negativos
        $test_cases = [
            // caso 1: campos vacíos
            [
                'payload' => [],
                'expectedInvalid' => ['nombre', 'direccion', 'ubicacion', 'contacto', 'zona_id', 'user_id']
            ],

            // caso 2: campos vacíos en el Repeater de enlaces
            [
                'payload' => [
                    'enlaces_sociales' => [
                        [
                            'plataforma' => '',
                            'url' => '',
                        ],
                    ],
                ],
                'expectedInvalid' => ['enlaces_sociales.0.plataforma', 'enlaces_sociales.0.url']
            ],
            // caso 3: campos vacíos en el Repeater de precios
            // [
            //     'payload' => [
            //         'precios' => [
            //             [
            //                 'categoria' => '',
            //                 'productos' => [
            //                     ['' => ''],
            //                 ],
            //             ],
            //         ],
            //     ],
            //     'expectedInvalid' => ['precios.0.categoria', /* TODO: Comprobar que no puede ser vacia ni la clave ni el valor */]
            // ],
            // caso 4: campos vacíos en el Repeater de enlaces propios
            // [
            //     'payload' => [
            //         'enlaces_propios' => [
            //             '' => 'invalid',
            //         ],
            //     ],
            //     'expectedInvalid' => ['enlaces_propios.0']
            // ],
            // caso 5: Valor de suscripcion inválido
            [
                'payload' => [
                    'suscripcion' => 9999,
                ],
                'expectedInvalid' => ['suscripcion']
            ],
            [
                'payload' => [
                    'categorias' => [],
                ],
                'expectedInvalid' => ['categorias'], // si tu resource requiere al menos 1
            ],
        ];

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];
            $this->actingAs($user);

            $test = Livewire::test(CreateNegocio::class);

            foreach ($test_cases as $case) {
                $test->fillForm($case['payload'])
                    ->call('create')
                    ->assertHasFormErrors($case['expectedInvalid']);
            }
        }
    }


    public function test_el_sistema_elimina_un_negocio_correctamente(): void
    {

        // 🟢 Test positivos

        $test_roles = $this->_getRolesPositivos(['negocio.delete']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $negocio = Negocio::factory()->create();

            $test = Livewire::test(ListNegocios::class);

            $test
                ->assertTableActionVisible(DeleteAction::class, $negocio->id)
                ->callTableAction(DeleteAction::class, $negocio->id);

            $this->assertDatabaseCount(Negocio::class, 0);

            $this->assertDatabaseCount(Suscripcion::class, 0);

            $this->assertFalse($negocio->hasMedia('imagenes'));
            $this->assertFalse($negocio->hasMedia('logos'));
        }

        // 🔴 Test negativos

        $test_roles = [];

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            // $test = Livewire::test(ListAplicaciones::class);
        }
    }

    #[Group('test')]
    public function _test_el_sistema_____(): void
    {

        // 🟢 Test positivos

        // $categoria = Zona::factory()->create();

        $test_roles = [];

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            // $test = Livewire::test(ListAplicaciones::class);
        }

        // 🔴 Test negativos

        $test_roles = [];

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            // $test = Livewire::test(ListAplicaciones::class);
        }
    }
}
