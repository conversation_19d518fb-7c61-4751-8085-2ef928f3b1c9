import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:mia/app.dart';
import 'package:mia/providers/theme_provider.dart';
import 'package:mia/providers/settings_provider.dart';
import 'package:mia/providers/favorites_provider.dart';
import 'package:mia/providers/permission_provider.dart';
import 'package:mia/providers/location_provider.dart';

// Creamos un RouteObserver global para escuchar cambios en las rutas
// Se usa como parámetro navigatorObservers: en el MaterialApp
// final RouteObserver<ModalRoute<void>> routeObserver =
//     RouteObserver<ModalRoute<void>>();

void main() async {
  // Asegura que WidgetsFlutterBinding esté inicializado
  WidgetsFlutterBinding.ensureInitialized();

  // Carga variables de entorno
  await dotenv.load();

  // Configura locale
  Intl.defaultLocale = 'es';

  // Forzar orientación vertical
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Inicializa providers directamente
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => SettingsProvider()),
        ChangeNotifierProvider(create: (_) => FavoritesProvider()),
        ChangeNotifierProvider(create: (_) => PermissionProvider()),
        ChangeNotifierProvider(create: (_) => LocationProvider()),
      ],
      child: const MyApp(),
    ),
  );
}
