<?php

namespace App\Http\Requests;

use App\Models\Categoria;
use Illuminate\Validation\Rule;
use App\Enums\EstadoSuscripcion;
use App\Validators\BusinessHoursValidator;
use Illuminate\Foundation\Http\FormRequest;

class StoreNegocioRequest extends FormRequest
{
    public function authorize(): bool
    {
        // Verificamos si el usuario tiene permiso para crear negocios
        return $this->user()->can('create', \App\Models\Negocio::class);
    }

    public function rules(): array
    {
        return [
            'nombre' => 'required|string|max:100',
            'descripcion' => 'nullable|string',
            'direccion' => 'required|string|max:255',
            'ubicacion' => 'required|array', // Validación para objeto JSON
            'ubicacion.latitud' => 'required|numeric|between:-90,90',
            'ubicacion.longitud' => 'required|numeric|between:-180,180',
            'horario' => [
                'nullable',
                'array',
                // function ($attribute, $value, $fail) {
                //     $errors = BusinessHoursValidator::validate($value);

                //     foreach ($errors as $error) {
                //         $fail($error);
                //     }
                // }
            ],
            'contacto' => 'required|string|max:255',
            'zona_id' => 'nullable|exists:zonas,id',
            'user_id' => 'required|exists:users,id',

            // Enlaces
            'enlaces_sociales' => 'nullable|array',
            'enlaces_sociales.*.plataforma' => 'required|string',
            'enlaces_sociales.*.url' => 'required|url',

            'enlaces_propios' => 'nullable|array',
            'enlaces_propios.*' => [
                'required',
                function ($attribute, $value, $fail) {
                    // $attribute será algo como "precios.0.productos.Entrada General"
                    // Separamos por puntos y obtenemos la última parte
                    $parts = explode('.', $attribute);
                    $titulo = end($parts); // "Entrada General"

                    // Verificamos que la clave (titulo del enlace) no esté vacía
                    if (trim($titulo) === '') {
                        $fail('El titulo del enlace no puede estar vacío.');
                    }

                    // Verificamos que el valor sea url
                    if (!filter_var($value, FILTER_VALIDATE_URL)) {
                        $fail('La URL del enlace debe ser una URL válida.');
                    }
                },
            ],

            // Precios
            'precios' => 'nullable|array',
            'precios.*.categoria' => 'required|string',
            'precios.*.productos' => 'required|array',
            'precios.*.productos.*' => [
                'required', // Para asegurar que haya un valor numérico
                function ($attribute, $value, $fail) {
                    // $attribute será algo como "precios.0.productos.Entrada General"
                    // Separamos por puntos y obtenemos la última parte
                    $parts = explode('.', $attribute);
                    $productName = end($parts); // "Entrada General"

                    // Verificamos que la clave (nombre del producto) no esté vacía
                    if (trim($productName) === '') {
                        $fail('El nombre del producto no puede estar vacío.');
                    }

                    // Verificamos que el valor sea numérico
                    if (!is_numeric($value)) {
                        $fail('El precio del producto debe ser un valor numérico.');
                    }
                },
            ],
            // suscripcion
            'suscripcion' => [
                'nullable',
                function ($attribute, $value, $fail) {
                    // Verificamos que el valor este en los EstadoSuscripcion:cases()
                    if (!in_array($value, array_map(fn($case) => $case->value, EstadoSuscripcion::cases()))) {
                        $fail('El estado de la suscripción no es válido');
                    }
                }
            ],
            'categorias' => [
                'required',
                'array',
                'min:1',
                Rule::exists('categorias', 'id'),
                // function ($attribute, $value, $fail) {
                //     $existingCategories = Categoria::whereIn('id', $value)->count();
                //     if ($existingCategories !== count($value)) {
                //         $fail('Una o más categorías no existen.');
                //     }
                // }
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'nombre.required' => 'El nombre del negocio es obligatorio.',
            'nombre.max' => 'El nombre del negocio es demasiado largo.',
            'direccion.max' => 'La dirección del negocio es demasiado larga.',
            'ubicacion.required' => 'La ubicación es obligatoria y debe incluir latitud y longitud.',
            'ubicacion.latitud.between' => 'La latitud debe estar entre -90 y 90 grados.',
            'ubicacion.longitud.between' => 'La longitud debe estar entre -180 y 180 grados.',
            'contacto.required' => 'El contacto del negocio es obligatorio.',
            'categoria_id.exists' => 'La categoría seleccionada no es válida.',
            'zona_id.exists' => 'La zona seleccionada no es válida.',
            'user_id.exists' => 'El cliente seleccionado no es válido.',
            'enlaces_sociales.*.plataforma.required' => 'La plataforma del enlace es obligatoria.',
            'enlaces_sociales.*.url.required' => 'La URL del enlace es obligatoria.',
            'enlaces_sociales.*.url.url' => 'La URL del enlace debe ser una URL válida.',
            'precios.*.categoria.required' => 'La categoría del precio es obligatoria.',
            'precios.*.productos.required' => 'Debe existir al menos un producto.',
        ];
    }
}
