# Modelos del Backend

## Índice
- [User](#user)
- [Localidad](#localidad)
- [Zona](#zona)
- [<PERSON><PERSON><PERSON>](#negocio)
- [Categoria](#categoria)
- [Suscripcion](#suscripcion)
- [PagoSuscripcion](#pagosuscripcion)
- [Evento](#evento)

---

## User

**Archivo**: `app/Models/User.php`

### Descripción
Representa a los usuarios del sistema (administradores, propietarios de negocios, etc.).

### Atributos

| Campo | Tipo | Descripción |
|-------|------|-------------|
| id | bigint | ID único |
| name | string | Nombre completo |
| email | string | Email (único) |
| password | string | Contraseña hasheada |
| email_verified_at | timestamp | Fecha de verificación |
| profile_photo_path | string | Ruta de foto de perfil |

### Relaciones

```php
// Un usuario puede tener muchos negocios
public function negocios(): HasMany
{
    return $this->hasMany(Negocio::class);
}

// Roles y permisos (Spatie)
use HasRoles; // Trait de Spatie Permission
```

### Traits Utilizados
- `HasApiTokens` (Sanctum)
- `HasRoles` (Spatie Permission)
- `HasFactory`
- `HasProfilePhoto` (Jetstream)
- `Notifiable`
- `TwoFactorAuthenticatable` (Jetstream)

### Métodos Importantes

```php
// Verifica si puede acceder al panel Filament
public function canAccessPanel(Panel $panel): bool
{
    return $this->can('system.access-panel');
}
```

---

## Localidad

**Archivo**: `app/Models/Localidad.php`

### Descripción
Representa municipios o ciudades donde se ubican las zonas.

### Atributos

| Campo | Tipo | Descripción |
|-------|------|-------------|
| id | bigint | ID único |
| nombre | string | Nombre de la localidad |
| ubicacion | json | Coordenadas centrales {lat, lng} |
| limites | json | Polígono de límites geográficos |

### Casts

```php
protected $casts = [
    'ubicacion' => 'array',
    'limites' => 'array',
];
```

### Relaciones

```php
// Una localidad tiene muchas zonas
public function zonas(): HasMany
{
    return $this->hasMany(Zona::class);
}

// Una localidad tiene muchos negocios a través de zonas
public function negocios(): HasManyThrough
{
    return $this->hasManyThrough(Negocio::class, Zona::class);
}
```

---

## Zona

**Archivo**: `app/Models/Zona.php`

### Descripción
Representa áreas geográficas dentro de una localidad (ej: "Playa Norte", "Centro").

### Atributos

| Campo | Tipo | Descripción |
|-------|------|-------------|
| id | bigint | ID único |
| nombre | string | Nombre de la zona |
| descripcion | text | Descripción opcional |
| coordenadas | json | Polígono GeoJSON |
| localidad_id | bigint | FK a localidades |

### Casts

```php
protected $casts = [
    'coordenadas' => 'array',
];
```

### Relaciones

```php
// Una zona pertenece a una localidad
public function localidad(): BelongsTo
{
    return $this->belongsTo(Localidad::class);
}

// Una zona tiene muchos negocios
public function negocios(): HasMany
{
    return $this->hasMany(Negocio::class);
}
```

---

## Negocio

**Archivo**: `app/Models/Negocio.php`

### Descripción
Representa establecimientos comerciales (restaurantes, tiendas, servicios, etc.).

### Atributos

| Campo | Tipo | Descripción |
|-------|------|-------------|
| id | bigint | ID único |
| nombre | string | Nombre del negocio |
| descripcion | text | Descripción detallada |
| direccion | string | Dirección física |
| ubicacion | json | {latitud, longitud} |
| horario | json | Horarios por día de semana |
| precios | json | Rangos de precios |
| enlaces_sociales | json | {facebook, instagram, ...} |
| enlaces_propios | json | {titulo: url} |
| contactos_secundarios | json | Contactos adicionales |
| contacto | string | Teléfono principal |
| zona_id | bigint | FK a zonas |
| user_id | bigint | FK a users (propietario) |
| order | integer | Orden de visualización |

### Casts

```php
protected $casts = [
    'ubicacion' => 'array',
    'horario' => 'array',
    'precios' => 'array',
    'enlaces_sociales' => 'array',
    'enlaces_propios' => 'array',
    'contactos_secundarios' => 'array',
];
```

### Relaciones

```php
// Pertenece a una zona
public function zona(): BelongsTo
{
    return $this->belongsTo(Zona::class);
}

// Pertenece a un usuario (propietario)
public function user(): BelongsTo
{
    return $this->belongsTo(User::class);
}

// Tiene muchas categorías (N:M)
public function categorias(): BelongsToMany
{
    return $this->belongsToMany(
        Categoria::class, 
        'categoria_negocio', 
        'negocio_id', 
        'categoria_id'
    );
}

// Tiene una suscripción
public function suscripcion(): HasOne
{
    return $this->hasOne(Suscripcion::class);
}

// Tiene muchos eventos
public function eventos(): HasMany
{
    return $this->hasMany(Evento::class);
}
```

### Media Collections (Spatie)

```php
// Colecciones de medios
public function registerMediaCollections(): void
{
    $this->addMediaCollection('imagenes')->useDisk('negocios');
    $this->addMediaCollection('logos')->useDisk('negocios');
}

// Conversiones de imágenes
public function registerMediaConversions(?Media $media = null): void
{
    $this->addMediaConversion('thumb')
        ->fit(Fit::Contain, 200, 200)
        ->nonQueued();
}
```

---

## Categoria

**Archivo**: `app/Models/Categoria.php`

### Descripción
Categorías jerárquicas para clasificar negocios (ej: Restaurantes > Comida Rápida).

### Atributos

| Campo | Tipo | Descripción |
|-------|------|-------------|
| id | bigint | ID único |
| nombre | string | Nombre de la categoría |
| descripcion | text | Descripción opcional |
| parent_id | bigint | FK a categorias (padre) |
| order | integer | Orden de visualización |
| visible | boolean | Si se muestra en la app |
| permitir_eventos | boolean | Si permite eventos |
| permitir_promociones | boolean | Si permite promociones |

### Casts

```php
protected $casts = [
    'parent_id' => 'int',
    'visible' => 'boolean',
    'permitir_eventos' => 'boolean',
    'permitir_promociones' => 'boolean',
];
```

### Relaciones

```php
// Subcategorías
public function subcategorias(): HasMany
{
    return $this->hasMany(Categoria::class, 'parent_id');
}

// Categoría padre
public function padre(): BelongsTo
{
    return $this->belongsTo(Categoria::class, 'parent_id');
}

// Negocios asociados (N:M)
public function negocios(): BelongsToMany
{
    return $this->belongsToMany(
        Negocio::class, 
        'categoria_negocio', 
        'categoria_id', 
        'negocio_id'
    );
}
```

### Métodos Útiles

```php
// Obtiene todas las subcategorías recursivamente
public function getDescendientes(): Collection
{
    $descendientes = new Collection();
    
    foreach ($this->subcategorias as $subcategoria) {
        $descendientes->push($subcategoria);
        $descendientes = $descendientes->merge(
            $subcategoria->getDescendientes()
        );
    }
    
    return $descendientes;
}
```

### Media Collections

```php
public function registerMediaCollections(): void
{
    $this->addMediaCollection('iconos')->useDisk('categorias');
}
```

---

## Suscripcion

**Archivo**: `app/Models/Suscripcion.php`

### Descripción
Representa el plan de suscripción de un negocio.

### Atributos

| Campo | Tipo | Descripción |
|-------|------|-------------|
| id | bigint | ID único |
| negocio_id | bigint | FK a negocios |
| plan | enum | Tipo de plan (free, basic) |
| status | enum | Estado (0=inactivo, 1=activo) |
| precio | decimal | Precio del plan |
| started_at | datetime | Fecha de inicio |
| ends_at | datetime | Fecha de fin |

### Casts

```php
protected $casts = [
    'status' => EstadoSuscripcion::class,
    'plan' => TipoSuscripcion::class,
    'started_at' => 'datetime',
    'ends_at' => 'datetime',
];
```

### Relaciones

```php
public function negocio(): BelongsTo
{
    return $this->belongsTo(Negocio::class);
}

public function pagos(): HasMany
{
    return $this->hasMany(PagoSuscripcion::class);
}
```

### Scopes

```php
// Suscripciones facturables (no free)
public function scopeFacturables($query)
{
    return $query->where('plan', '!=', TipoSuscripcion::FREE->value);
}

// Suscripciones activas
public function scopeActivas($query)
{
    return $query->where('status', EstadoSuscripcion::ACTIVE->value);
}

// Sin pagos en temporada actual
public function scopeSinPagosEnTemporadaActual($query)
{
    // Lógica de temporada (abril a marzo)
}
```

---

## PagoSuscripcion

**Archivo**: `app/Models/PagoSuscripcion.php`

### Descripción
Registra los pagos realizados por suscripciones.

### Atributos

| Campo | Tipo | Descripción |
|-------|------|-------------|
| id | bigint | ID único |
| suscripcion_id | bigint | FK a suscripciones |
| metodo_pago | enum | Método (efectivo, bizum, transferencia) |
| importe | decimal | Cantidad pagada |
| transaccion_id | string | ID de transacción |
| estado | enum | Estado (pendiente, completado, rechazado) |
| fecha_pago | date | Fecha del pago |

### Casts

```php
protected $casts = [
    'metodo_pago' => MetodoPago::class,
    'estado' => EstadoPago::class,
    'fecha_pago' => 'date',
    'importe' => 'decimal:2',
];
```

### Relaciones

```php
public function suscripcion(): BelongsTo
{
    return $this->belongsTo(Suscripcion::class);
}

public function negocio(): HasOneThrough
{
    return $this->hasOneThrough(
        Negocio::class,
        Suscripcion::class,
        'id',
        'id',
        'suscripcion_id',
        'negocio_id'
    );
}
```

### Scopes

```php
public function scopePendientes($query)
public function scopeCompletados($query)
public function scopeRechazados($query)
public function scopeEnTemporadaActual($query)
```

---

## Evento

**Archivo**: `app/Models/Evento.php`

### Descripción
Eventos o promociones de negocios.

### Atributos

| Campo | Tipo | Descripción |
|-------|------|-------------|
| id | bigint | ID único |
| nombre | string | Nombre del evento |
| descripcion | text | Descripción |
| url | string | URL externa (opcional) |
| fecha_inicio | datetime | Fecha de inicio |
| fecha_fin | datetime | Fecha de fin |
| negocio_id | bigint | FK a negocios |

### Relaciones

```php
public function negocio(): BelongsTo
{
    return $this->belongsTo(Negocio::class);
}

public function user(): HasOneThrough
{
    return $this->hasOneThrough(
        User::class,
        Negocio::class,
        'id',
        'id',
        'negocio_id',
        'user_id'
    );
}
```

### Media Collections

```php
public function registerMediaCollections(): void
{
    $this->addMediaCollection('imagenes_eventos')->useDisk('eventos');
}
```

