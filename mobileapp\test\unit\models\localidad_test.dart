import 'package:flutter_test/flutter_test.dart';
import 'package:mia/models/localidad.dart';
import '../../helpers/base_test_helper.dart';
import 'utils/model_test_utils.dart';

void main() async {
  TestWidgetsFlutterBinding.ensureInitialized();
  await BaseTest.initialize();

  group('Localidad Model Tests', () {
    ModelTestUtils.runCommonModelTests<Localidad>(
      validFixturePaths: [
        'localidad/valid_localidad_1.json',
      ],
      invalidFixturePaths: [
        'localidad/invalid_localidad_missing_1.json',
      ],
      fromJson: Localidad.fromJson,
      toJson: (localidad) => localidad.toJson(),
      getExpectedValues: (fixturePath) =>
          _getLocalidadExpectedValues(fixturePath),
      customModel: {
        'id': 99,
        'nombre': 'Localidad de Prueba',
        'ubicacion': {'latitud': 1.0, 'longitud': 1.0},
        // 'limites': {'latitud_min': 1.0, 'longitud_min': 1.0, 'latitud_max': 1.0, 'longitud_max': 1.0},
      },
      requiredFields: ['id', 'nombre'],
    );

    _runSpecificTests();
  });
}

void _runSpecificTests() {
  group('Funciones específicas de Localidad:', () {
    test('toString', () {
      final localidad = Localidad(
        id: 1,
        nombre: 'Matalascañas',
        ubicacion: {'latitud': 1.0, 'longitud': 1.0},
      );
      expect(localidad.toString(),
          'Localidad(id: 1, nombre: Matalascañas, ubicacion: {latitud: 1.0, longitud: 1.0})');
    });
  });
}

Map<String, dynamic> _getLocalidadExpectedValues(String fixturePath) {
  return {
    'valid_localidad_1.json': {
      'id': 1,
      'nombre': 'Matalascañas',
      'ubicacion': {'latitud': 36.9990019, 'longitud': -6.5478919}
    }
  }[fixturePath.split('/').last]!;
}
