{"version": "2.0.0", "tasks": [{"type": "shell", "label": "generate-env-local", "command": "bash", "args": ["scripts/generate-env.sh", "local", ".env.local"], "options": {"cwd": "${workspaceFolder}/mobileapp"}}, {"type": "shell", "label": "generate-env-staging", "command": "bash", "args": ["scripts/generate-env.sh", "staging", ".env.staging"], "options": {"cwd": "${workspaceFolder}/mobileapp"}}, {"type": "shell", "label": "generate-env-production", "command": "bash", "args": ["scripts/generate-env.sh", "production", ".env.production"], "options": {"cwd": "${workspaceFolder}/mobileapp"}}]}