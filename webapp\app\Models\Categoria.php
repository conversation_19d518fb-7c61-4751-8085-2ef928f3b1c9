<?php

namespace App\Models;

use App\Models\Negocio;
use Spatie\Image\Enums\Fit;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\MediaLibrary\HasMedia;

class Categoria extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;

    protected $fillable = [
        'nombre',
        'descripcion',
        'parent_id',
        'order',
        'visible',
        'permitir_eventos',
        'permitir_promociones'
    ];

    protected $casts = [
        'parent_id' => 'int',
        'visible' => 'boolean',
        'permitir_eventos'     => 'boolean',
        'permitir_promociones' => 'boolean'
    ];

    public function subcategorias()
    {
        return $this->hasMany(Categoria::class, 'parent_id');
    }

    public function padre()
    {
        return $this->belongsTo(Categoria::class, 'parent_id');
    }

    public function _negocios()
    {
        return $this->hasMany(Negocio::class);
    }

    public function negocios(): BelongsToMany
    {
        return $this->belongsToMany(Negocio::class, 'categoria_negocio', 'categoria_id', 'negocio_id');
    }

    public function getChildrenAttribute()
    {
        return $this->subcategorias;
    }

    public function hasAntecesor(Categoria $categoria_antecesora, $categorias = null): bool
    {
        // Si no se pasó la colección, la obtenemos de la base de datos y la indexamos por id
        if (is_null($categorias)) {
            $categorias = Categoria::all()->keyBy('id');
        }

        // Si no hay padre, ya no hay antecesores
        if (is_null($this->parent_id)) {
            return false;
        }

        // Buscar el padre en la colección
        if (!isset($categorias[$this->parent_id])) {
            return false;
        }

        $categoria_padre = $categorias[$this->parent_id];

        // Si el padre es el que buscamos, devolvemos true
        if ($categoria_padre->id === $categoria_antecesora->id) {
            return true;
        }

        // Llamada recursiva pasando la misma colección
        return $categoria_padre->esAntecesor($categoria_antecesora, $categorias);
    }

    // Función recursiva para obtener todas las subcategorías
    public function getDescendientes(): Collection
    {
        $descendientes = new Collection();

        foreach ($this->subcategorias as $subcategoria) {
            $descendientes->push($subcategoria);
            $descendientes = $descendientes->merge($subcategoria->getDescendientes());
        }

        return $descendientes;
    }


    // Media Library
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('iconos')->useDisk('categorias');
    }

    public function registerMediaConversions(?Media $media = null): void
    {
        $this
            ->addMediaConversion('thumb')
            ->fit(Fit::Contain, 200, 200)
            ->nonQueued();
    }
}
