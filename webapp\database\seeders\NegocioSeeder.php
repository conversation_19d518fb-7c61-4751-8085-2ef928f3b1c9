<?php

namespace Database\Seeders;

use App\Models\Categoria;
use App\Models\User;
use App\Models\Negocio;
use Illuminate\Database\Seeder;

class NegocioSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categorias = Categoria::all();

        $this->command->getOutput()->progressStart($categorias->count() * 3);

        $categorias->each(function ($categoria) {
            Negocio::factory(3)
                ->conSuscripcion()
                ->conCategorias([$categoria->id])
                ->conEventos(rand(1, 3))
                ->afterCreating(function () {
                    $this->command->getOutput()->progressAdvance();
                })
                ->create()
            ;
        });

        $this->command->getOutput()->progressFinish();
    }
}
