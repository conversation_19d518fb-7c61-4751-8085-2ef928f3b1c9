// Mantener este comentario con el nombre de archivo
// lib/pages/splash.dart

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:mia/config/colors.dart';
import 'package:mia/config/styles.dart';
import 'package:mia/pages/categorias.dart';
import 'package:mia/services/core_service.dart';
import 'package:mia/services/global_data_service.dart';
import 'package:mia/widgets/app_scaffold.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  @override
  void initState() {
    super.initState();
    _loadDataAndNavigate();
  }

  Future<void> _loadDataAndNavigate() async {
    // bool dataLoaded = false; // Flag para indicar si los datos se cargaron

    try {
      await GlobalDataService().loadData();
      // dataLoaded = true; // Datos cargados con éxito
    } catch (e) {
      String error = "Error al cargar datos: $e";
      debugPrint(error);
      CoreService.muestraError(error); // Muestra el error
    }

    // Espera al menos 3 segundos
    // await Future.delayed(const Duration(seconds: 3));

    if (mounted) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            // builder: (context) => HomePage(
            //   title: 'Bienvenido',
            // ),
            builder: (context) => const CategoriasPage()),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      isSplashScreen: true,
      child: Stack(
        children: [
          Center(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                const SizedBox(height: 100),
                Image.asset('assets/mia.png'),
                Text(
                  'Matalascañas \n Info Agenda',
                  textAlign: TextAlign.center,
                  style: AppStyles.getStyle(context, 'h3',
                      fontWeight: 'bold',
                      color: AppColors.current.secondaryColor),
                ),
                const SizedBox(height: 40),
                const CircularProgressIndicator(),
              ],
            ),
          ),
          Positioned(
            bottom: 0.0, // Espaciado desde el borde inferior
            left: 0,
            right: 0,
            child: Center(
              child: Image.asset('assets/piedra_solo_color.png'),
            ),
          ),
        ],
      ),
    );
  }
}
