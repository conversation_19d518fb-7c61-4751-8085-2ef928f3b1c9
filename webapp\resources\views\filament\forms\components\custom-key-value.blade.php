{{-- resources/views/filament/forms/components/custom-key-value.blade.php --}}
<x-dynamic-component :component="$getFieldWrapperView()" :field="$field">
    @php
        // Convertimos el estado a un formato adecuado para Alpine.js
        $state = $getState() ?? [
            'lunes'     => [],
            'martes'    => [],
            'miércoles' => [],
            'jueves'    => [],
            'viernes'   => [],
            'sábado'    => [],
            'domingo'   => [],
        ];

        // Transformamos cada tramo horario "09:00-18:00" a ['09:00', '18:00']
        foreach ($state as $day => $tramos) {
            // Asegurar que $tramos sea siempre un array
            $tramos = is_array($tramos) ? $tramos : [];
            $state[$day] = array_map(fn($tramo) => explode('-', $tramo), $tramos);
        }
    @endphp

    <table class="w-full border-collapse">
        <colgroup>
            <col style="width: 1%;">
            <col>
        </colgroup>
        <thead>
            <tr>
                <th class="border px-4 py-2 text-left bg-gray-200 dark:bg-gray-700 dark:text-gray-200">Día</th>
                <th class="border px-4 py-2 text-left bg-gray-200 dark:bg-gray-700 dark:text-gray-200">Tramos horarios</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($state as $day => $tramos)
                <tr class="bg-white dark:bg-gray-800" 
                    x-data="{
                        tramos: @js($tramos), 
                        addTramo() { 
                          if (this.tramos.length < 4) {
                            this.tramos.push(['', '']); 
                            this.updateState(); 
                          }
                        }, 
                        removeTramo(index) { 
                          this.tramos.splice(index, 1); 
                          this.updateState(); 
                        }, 
                        updateState() { 
                          @this.set('{{ $getStatePath() }}.{{ $day }}', this.tramos
                              .map(t => t[0] && t[1] ? `${t[0]}-${t[1]}` : '')
                              .filter(Boolean)
                          ); 
                        }
                    }">
                    <td class="border px-4 py-2 text-gray-800 dark:text-gray-100">
                        {{ ucfirst($day) }}
                    </td>
                    <td class="border px-4 py-2">
                        <div class="flex flex-wrap gap-2">
                            <template x-for="(tramo, index) in tramos" :key="index">
                                <div class="flex items-center gap-2 border rounded p-2 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100">
                                    <input type="time" x-model="tramo[0]" @change="updateState()" class="w-24 border rounded p-2 bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-100">
                                    <span>-</span>
                                    <input type="time" x-model="tramo[1]" @change="updateState()" class="w-24 border rounded p-2 bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-100">
                                    <button type="button" @click="removeTramo(index)" class="text-red-500 hover:text-red-700">
                                        <x-heroicon-s-trash class="fi-icon-btn relative flex items-center justify-center rounded-lg outline-none transition duration-75 focus-visible:ring-2 -m-1.5 h-5 w-5 fi-color-custom text-custom-500 hover:text-custom-600 focus-visible:ring-custom-600 dark:text-custom-400 dark:hover:text-custom-300 dark:focus-visible:ring-custom-500 fi-color-danger fi-ac-action fi-ac-icon-btn-action" style="--c-300:var(--danger-300);--c-400:var(--danger-400);--c-500:var(--danger-500);--c-600:var(--danger-600);"/>
                                    </button>
                                </div>
                            </template>

                            <button type="button" x-show="tramos.length < 4" @click="addTramo()"
                                title="Añadir tramo horario"
                                class="flex items-center justify-center gap-1 p-2 rounded text-white bg-primary-600 hover:bg-primary-700">
                                <span>+</span>
                                <x-heroicon-s-clock class="h-5 w-5" />
                            </button>
                        </div>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
</x-dynamic-component>
