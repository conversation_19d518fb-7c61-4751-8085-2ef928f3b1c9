<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Filament\Resources\UserResource\RelationManagers;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    protected static bool $hasTitleCaseModelLabel = true;

    protected static ?string $modelLabel = "usuario";

    protected static ?string $pluralModelLabel = 'usuarios';

    protected static ?string $navigationLabel = 'Usuarios';

    protected static ?string $navigationGroup = 'Administración';

    public static function form(Form $form): Form
    {

        /** @var User */
        $authUser = Auth::user();

        return $form
            ->schema([
                TextInput::make('name')
                    ->label('Nombre')
                    ->required()
                    ->maxLength(255),

                TextInput::make('email')
                    ->label('Email')
                    ->required()
                    ->email()
                    ->maxLength(255),

                TextInput::make('password')
                    ->label('Contraseña')
                    ->required(fn($context) => $context == 'create')
                    ->minLength(8)
                    ->password()
                    ->revealable()
                    ->dehydrateStateUsing(fn($state) => bcrypt($state)),

                Select::make('roles')
                    ->label('Roles')
                    ->multiple()
                    ->relationship('roles', 'name')
                    ->preload()
                    ->required()
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('roles.name')
                    ->label('Roles')
                    ->badge()
                    ->color(fn($state) => match ($state) {
                        'admin' => 'success',
                        'owner' => 'success',
                        'cliente' => 'primary',
                        default => 'gray',
                    })
                    ->separator(', '),
                TextColumn::make('name')->sortable()->searchable(),
                TextColumn::make('email')->sortable()->searchable(),
            ])
            ->filters([
                SelectFilter::make('role')
                    ->label('Tipo de Usuario')
                    ->options([
                        'admin' => 'Admins',
                        'owner' => 'Propietarios',
                        'cliente' => 'Clientes',
                        'usuario' => 'Usuarios',
                    ])
                    // ->multiple()
                    ->query(function (Builder $query, $data) {

                        $data = $data['value'];
                        if ($data == null) {
                            return;
                        }

                        if (!is_array($data)) {
                            $data = [$data];
                        }

                        if ($data != []) {
                            $query->whereHas('roles', function (Builder $query) use ($data) {
                                return $query->whereIn('name', $data);
                            });
                        }

                        return $query;
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->with('roles');
    }

    public static function shouldRegisterNavigation(): bool
    {
        /** @var User */
        $user = Auth::user();
        return $user->can('user.admin-users');
    }
}
