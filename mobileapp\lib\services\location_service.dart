// lib/services/location_service.dart

import 'package:geolocator/geolocator.dart';

/// Servicio para obtener la ubicación del dispositivo.
class LocationService {
  /// Obtiene la posición actual del dispositivo.
  /// Lanza una excepción si el permiso no está concedido o el GPS está deshabilitado.
  Future<Position> getCurrentPosition({
    LocationAccuracy accuracy = LocationAccuracy.best,
    Duration? timeLimit,
  }) async {
    // Verifica que los servicios de ubicación estén habilitados
    final serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      throw LocationServiceDisabledException();
    }

    // Asume que el permiso ya fue solicitado por PermissionProvider.
    // Si quieres, podrías volver a pedirlo aquí:
    var permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        throw PermissionDeniedException('Permiso denegado');
      }
    }
    if (permission == LocationPermission.deniedForever) {
      throw PermissionDeniedException('Permiso denegado permanentemente');
    }

    // Obtiene la posición actual
    return await Geolocator.getCurrentPosition(
      desiredAccuracy: accuracy,
      timeLimit: timeLimit,
    );
  }

  /// Stream de actualizaciones de posición del dispositivo.
  /// Ideal para seguir la posición en tiempo real.
  Stream<Position> getPositionStream({
    LocationAccuracy accuracy = LocationAccuracy.best,
    int distanceFilter = 10,
    Duration? intervalDuration,
  }) {
    return Geolocator.getPositionStream(
      locationSettings: LocationSettings(
        accuracy: accuracy,
        distanceFilter: distanceFilter,
        timeLimit: intervalDuration,
      ),
    );
  }
}

/// Excepción lanzada cuando el GPS/servicio de ubicación está desactivado.
class LocationServiceDisabledException implements Exception {
  @override
  String toString() =>
      'Location services are disabled. Please enable them in device settings.';
}
