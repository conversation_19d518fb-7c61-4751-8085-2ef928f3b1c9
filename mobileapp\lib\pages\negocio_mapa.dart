// lib/pages/negocio_mapa.dart

import 'package:flutter/material.dart';
import 'package:flutter_map_animations/flutter_map_animations.dart';
import 'package:mia/config/colors.dart';
import 'package:mia/models/negocio.dart';
import 'package:mia/providers/location_provider.dart';
// import 'package:mia/providers/location_provider.dart.disabled';
import 'package:mia/services/core_service.dart';
import 'package:mia/widgets/app_scaffold.dart';
import 'package:mia/widgets/mapas/locations_map.dart';
import 'package:provider/provider.dart';
import 'package:mia/providers/permission_provider.dart';

class NegocioMapaPage extends StatefulWidget {
  final Negocio negocio;

  const NegocioMapaPage({super.key, required this.negocio});

  @override
  State<NegocioMapaPage> createState() => _NegocioMapaPageState();
}

class _NegocioMapaPageState extends State<NegocioMapaPage>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  // Controlador para el mapa
  late final _animatedMapController = AnimatedMapController(vsync: this);
  late LocationProvider _locationProvider;
  bool _locationRequested = false;

  @override
  void initState() {
    super.initState();
    // 1) Captura el provider UNA sola vez (listen: false)
    _locationProvider = context.read<LocationProvider>();

    // Registrar el observer para detectar cambios en el ciclo de vida de la app
    WidgetsBinding.instance.addObserver(this);

    // Pide permiso tras el primer frame, sin usar context en un async gap
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Ya en el mismo frame, el context está garantizado como montado
      context.read<PermissionProvider>().requestPermission();
    });
  }

  @override
  void dispose() {
    // Eliminar el observer cuando se destruye el widget
    WidgetsBinding.instance.removeObserver(this);
    _animatedMapController.dispose();
    _locationProvider.stopTracking();
    super.dispose();
  }

  // Implementación del método didChangeAppLifecycleState para detectar cuando
  // la aplicación vuelve al primer plano después de estar en segundo plano
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // El usuario ha vuelto a la aplicación (posiblemente desde los ajustes)
      // Verificamos el estado del permiso nuevamente
      context.read<PermissionProvider>().checkPermission();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    final permStatus = context.watch<PermissionProvider>().status;

    if (permStatus == LocationPermissionStatus.granted && !_locationRequested) {
      _locationRequested = true;
      // Esperamos al siguiente frame para llamar a fetchLocation
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _locationProvider.startTracking();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final permStatus = context.watch<PermissionProvider>().status;
    final loc = context.watch<LocationProvider>().userLocation;

    return AppScaffold(
      appBar: AppBar(
        title: Text(widget.negocio.nombre),
        backgroundColor: AppColors.current.surfaceColor,
        foregroundColor: AppColors.current.textColor,
        actions: [
          TextButton.icon(
            onPressed: () {
              CoreService.launchMap(widget.negocio);
            },
            label: const Text('¿CÓMO LLEGAR?'),
            icon: const Icon(Icons.directions_outlined),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        shape:
            ShapeBorder.lerp(const CircleBorder(), const CircleBorder(), 0.5),
        onPressed: () {
          Navigator.pop(context);
        },
        child: const Icon(Icons.arrow_back),
      ),
      child: Stack(
        children: [
          // El mapa siempre se muestra independientemente del estado del permiso
          LocationsMap(
            negocios: [widget.negocio],
            height: _calcularAltura(),
            mapController: _animatedMapController,
            showActions: true,
            unboundedMap: true,
            minZoom: 1,
            maxZoom: 17,
            // Solo mostrar ubicación del usuario si el permiso está concedido
            showUserLocation: permStatus == LocationPermissionStatus.granted,
            userLocation: loc,
          ),

          // Overlay con indicador de carga si el estado es desconocido
          if (permStatus == LocationPermissionStatus.unknown)
            Container(
              color: Colors.black45,
              child: const Center(child: CircularProgressIndicator()),
            ),

          // Botón flotante para solicitar permiso (denied) o ir a ajustes (permanentlyDenied)
          if (permStatus == LocationPermissionStatus.denied ||
              permStatus == LocationPermissionStatus.permanentlyDenied)
            Positioned(
              bottom: 16,
              left: 0,
              right: 0,
              child: Center(
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                    backgroundColor: Theme.of(context).primaryColor,
                  ),
                  onPressed: () {
                    if (permStatus == LocationPermissionStatus.denied) {
                      context.read<PermissionProvider>().requestPermission();
                    } else {
                      // Abrir ajustes si el permiso está permanentemente denegado
                      CoreService.openAppSystemSettings(context);
                    }
                  },
                  child: Text(
                    permStatus == LocationPermissionStatus.denied
                        ? 'Conceder permiso de ubicación'
                        : 'Ir a ajustes para activar la ubicación',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  double _calcularAltura() {
    final double screenHeight = MediaQuery.of(context).size.height;
    // final double appBarHeight = kToolbarHeight;
    final double appBarHeight = 0.0;
    final double padding = 10.0;
    return screenHeight - appBarHeight - padding;
  }
}
