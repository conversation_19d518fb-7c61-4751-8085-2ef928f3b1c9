<?php

namespace App\Filament\Widgets;

use App\Models\Negocio;
use Filament\Widgets\Widget;
use Illuminate\Support\Collection;
use Filament\Widgets\Concerns\InteractsWithPageFilters;

class _NegociosMap extends Widget
{
    use InteractsWithPageFilters;

    protected static string $view = 'filament.widgets.negocios-map';

    protected array|string|int $columnSpan = 'full';

    public ?Collection $negocios = null;

    // Si quieres pasar negocios filtrados u otros,
    // puedes sobreescribir 'mount()' o 'getViewData()'
    // para inyectar la propiedad $negocios a la vista.
    public function mount(?Collection $negocios = null): void
    {
        // Ejemplo: tomamos todos los negocios o algo filtrado
        // Por ahora, simulamos:
        $this->negocios = $negocios ?? Negocio::all();

        // Filtramos para que solo nos queden los datos que necesitamos
        $this->negocios = $this->negocios->map(function ($n) {
            return [
                'id' => $n->id,
                'nombre' => $n->nombre,
                'ubicacion' => $n->ubicacion, // ['latitud' => '...', 'longitud' => '...']
            ];
        });
    }

    protected function getViewData(): array
    {
        return [
            'negocios' => $this->negocios,
        ];
    }
}
