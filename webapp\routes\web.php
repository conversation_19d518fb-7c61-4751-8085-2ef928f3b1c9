<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Middleware\CheckSystemAdminPermission;

Route::get('/', [HomeController::class, 'index']);

Route::get('/negocios/{negocio}', [HomeController::class, 'show']);

Route::view('/privacy-policy', 'privacy-policy');

Route::view('/flyer', 'flyer');


Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
])->group(function () {
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');
});

Route::middleware(CheckSystemAdminPermission::class)->group(function () {
    Route::get('decompose', '\Lubusin\Decomposer\Controllers\DecomposerController@index');
});
