<?php

namespace App\Models;

use App\Enums\EstadoPago;
use App\Enums\MetodoPago;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PagoSuscripcion extends Model
{
    /** @use HasFactory<\Database\Factories\PagoSuscripcionFactory> */
    use HasFactory;

    protected $table = 'pago_suscripcions';

    protected $fillable = [
        'suscripcion_id',
        'metodo_pago',
        'importe',
        'transaccion_id',
        'estado',
        'fecha_pago',
    ];

    protected $casts = [
        'metodo_pago' => MetodoPago::class,
        'estado' => EstadoPago::class,
        'fecha_pago' => 'date',
        'importe' => 'decimal:2',
    ];

    public function suscripcion()
    {
        return $this->belongsTo(Suscripcion::class);
    }

    public function negocio()
    {
        return $this->hasOneThrough(
            Negocio::class,
            Suscripcion::class,
            'id', // Foreign key on suscripciones table
            'id', // Foreign key on negocios table
            'suscripcion_id', // Local key on pago_suscripcions table
            'negocio_id' // Local key on suscripciones table
        );
    }

    // scopes
    public function scopePendientes($query)
    {
        return $query->where('estado', EstadoPago::PENDIENTE->value);
    }

    public function scopeCompletados($query)
    {
        return $query->where('estado', EstadoPago::COMPLETADO->value);
    }

    public function scopePendientesYCompletados($query)
    {
        return $query->whereIn('estado', [
            EstadoPago::PENDIENTE->value,
            EstadoPago::COMPLETADO->value,
        ]);
    }

    public function scopeRechazados($query)
    {
        return $query->where('estado', EstadoPago::RECHAZADO->value);
    }

    public function scopeEnTemporadaActual($query)
    {
        $hoy = now();
        $inicioTemporada = $hoy->month >= 4 ? $hoy->year : $hoy->year - 1;
        $finTemporada = $inicioTemporada + 1;

        return $query->whereBetween('fecha_pago', [
            \Carbon\Carbon::createFromDate($inicioTemporada, 4, 1)->startOfDay(),
            \Carbon\Carbon::createFromDate($finTemporada, 3, 31)->endOfDay()
        ]);
    }
}
