<?php

namespace App\Http\Requests;

use App\Enums\EstadoPago;
use App\Enums\MetodoPago;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdatePagoSuscripcionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User */
        $user = Auth::user();

        if ($user->can('system.admin-dashboard')) {
            return true;
        }

        return false;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'suscripcion_id' => 'sometimes|exists:suscripciones,id',
            'metodo_pago' => ['sometimes', Rule::enum(MetodoPago::class)],
            'importe' => 'sometimes|numeric|min:0',
            'fecha_pago' => 'sometimes|date',
            'transaccion_id' => 'sometimes|string|max:255',
            'estado' => ['sometimes', Rule::enum(EstadoPago::class)],
        ];
    }
}
