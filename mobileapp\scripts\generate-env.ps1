# Si hace falta configurar la politica de ejecuccion de scripts
# Set-ExecutionPolicy RemoteSigned -Scope CurrentUser

# Parámetros con valores por defecto
param(
    [string]$Flavor = "local",
    [string]$EnvFile = ".env.example"
)

# Mensaje de depuración
Write-Host "Generando entorno para FLAVOR: $Flavor"
Write-Host "Usando archivo de configuración: $EnvFile"

# Eliminar .env existente
if (Test-Path .env) {
    Remove-Item .env
}

# Copiar archivo de entorno específico
try {
    Copy-Item $EnvFile .env -ErrorAction Stop
}
catch {
    Write-Host "Error copiando $EnvFile : $_"
    # Usar archivo de ejemplo como respaldo
    Copy-Item .env.example .env
}

# Añadir FLAVOR al archivo
# Add-Content -Path .env -Value "`nFLAVOR=$Flavor"

# Mostrar contenido generado
# Get-Content .env