// lib/widgets/app_scaffold.dart

import 'package:flutter/material.dart';
import 'package:mia/config/colors.dart';

class AppScaffold extends StatelessWidget {
  final Widget child;
  final AppBar? appBar;
  final Widget? bottomNavigationBar;
  final FloatingActionButton? floatingActionButton;

  final Widget? drawer;
  final void Function(bool)? onDrawerChanged;
  final Widget? endDrawer;
  final void Function(bool)? onEndDrawerChanged;

  final bool isSplashScreen;

  const AppScaffold({
    super.key,
    required this.child,
    this.appBar,
    this.bottomNavigationBar,
    this.drawer,
    this.onDrawerChanged,
    this.endDrawer,
    this.onEndDrawerChanged,
    this.floatingActionButton,
    this.isSplashScreen = false,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar,
      bottomNavigationBar: bottomNavigationBar,
      drawer: drawer,
      onDrawerChanged: onDrawerChanged,
      endDrawer: endDrawer,
      onEndDrawerChanged: onEndDrawerChanged,
      body: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              gradient:
                  AppColors.current.mainGradient, // Usa tu gradiente definido
            ),
          ),
          SafeArea(
            child: Align(
              alignment: isSplashScreen ? Alignment.center : Alignment.topLeft,
              child: Padding(
                padding: const EdgeInsets.all(0.0),
                child: child,
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: floatingActionButton,
    );
  }
}
