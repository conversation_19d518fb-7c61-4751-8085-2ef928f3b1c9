<?php

namespace Tests\Feature\Web;

use Tests\BaseTest;
use App\Models\User;
use Livewire\Livewire;
use App\Models\Categoria;
use Illuminate\Http\UploadedFile;
use Spatie\Permission\Models\Role;
use PHPUnit\Framework\Attributes\Group;
use Filament\Tables\Actions\DeleteAction;
use Database\Seeders\RolesPermissionSeeder;
use Illuminate\Foundation\Testing\WithFaker;
use App\Filament\Resources\CategoriaResource;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Filament\Resources\CategoriaResource\Pages\EditCategoria;
use App\Filament\Resources\CategoriaResource\Pages\ListCategorias;
use App\Filament\Resources\CategoriaResource\Pages\CreateCategoria;

#[Group('categoria')]
#[Group('webapp')]
#[Group('categoria_webapp')]
class CategoriaTest extends BaseTest
{
    use RefreshDatabase;

    private array $usuarios;

    // Configuración inicial para cada prueba
    protected function setUp(): void
    {
        parent::setUp();

        $this->seed(RolesPermissionSeeder::class);

        foreach (Role::all() as $rol) {
            $user = User::factory()->create();
            $user->assignRole($rol->name);

            $this->usuarios[$rol->name] = [
                'usuario' => $user,
            ];
        }
    }

    public function test_el_sistema_muestra_las_paginas_de_categoria_correctamente_segun_el_rol_del_usuario(): void
    {

        // 🟢 Test positivos

        $categoria = Categoria::factory()->create();

        $test_roles = $this->_getRolesPositivos(['categoria.list', 'categoria.create', 'categoria.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $this->get(CategoriaResource::getUrl('index'))->assertSuccessful();
            $this->get(CategoriaResource::getUrl('create'))->assertSuccessful();
            $this->get(CategoriaResource::getUrl('edit', ['record' => $categoria->id]))->assertSuccessful();
        }

        // 🔴 Test negativos

        $test_roles = $this->_getRolesNegativos(['categoria.list', 'categoria.create', 'categoria.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $this->get(CategoriaResource::getUrl('index'))->assertForbidden();
            $this->get(CategoriaResource::getUrl('create'))->assertForbidden();
            $this->get(CategoriaResource::getUrl('edit', ['record' => $categoria->id]))->assertForbidden();
        }
    }

    public function test_el_sistema_crea_una_nueva_categoria_solo_con_datos_validos(): void
    {
        // 🟢 Test positivos        

        $test_roles = $this->_getRolesPositivos(['categoria.create']);

        $i = 0;
        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $newData = Categoria::factory()->make()->toArray();

            // Simular la carga de un archivo
            $file = UploadedFile::fake()->image('icono.jpg', 100, 100);

            $test = Livewire::test(CreateCategoria::class);

            $this->assertDatabaseCount(Categoria::class, $i++);

            $test
                ->fillForm([
                    'nombre' => $newData['nombre'],
                    'descripcion' => $newData['descripcion'],
                    'parent_id' => $newData['parent_id'],
                    'visible' => $newData['visible'],

                ])
                ->set('data.icono', $file)
                ->call('create')
                ->assertHasNoFormErrors();

            $this->assertDatabaseHas(Categoria::class, [
                'nombre' => $newData['nombre'],
                'descripcion' => $newData['descripcion'],
                'parent_id' => $newData['parent_id'],
                'visible' => $newData['visible'],
            ]);

            $categoria = Categoria::latest()->first();
            $this->assertNotNull($categoria);
            $this->assertTrue($categoria->hasMedia('iconos'));
            $this->assertEquals(1, $categoria->getMedia('iconos')->count());
            $this->assertDatabaseCount(Categoria::class, $i);
        }

        // 🔴 Test negativos

        $test_roles = $this->_getRolesPositivos(['categoria.create']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $test_cases = [
                // caso 1: campos vacíos
                [
                    'payload' => [],
                    'expectedInvalid' => ['nombre']
                ],
            ];

            $test = Livewire::test(CreateCategoria::class);

            foreach ($test_cases as $case) {
                $test->fillForm($case['payload'])
                    ->call('create')
                    ->assertHasFormErrors($case['expectedInvalid']);
            }
        }
    }

    public function test_el_sistema_edita_una_categoria_solo_con_datos_validos(): void
    {
        // 🟢 Test positivos

        // $categoria = Categoria::factory()->conIcono()->create();
        // $subcategoria = Categoria::factory()->create(['parent_id' => $categoria->id]);

        $test_roles = $this->_getRolesPositivos(['categoria.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $categoria = Categoria::factory()->conIcono()->create();

            $test = Livewire::test(EditCategoria::class, ['record' => $categoria->id]);

            // $categoria->refresh();
            $newData = Categoria::factory()->make()->toArray();
            // Simular la carga de un archivo
            $file = UploadedFile::fake()->image('icono.jpg', 100, 100);

            // Verificar que tiene un icono inicialmente
            $this->assertTrue($categoria->hasMedia('iconos'));
            $initialMediaCount = $categoria->getMedia('iconos')->count();


            $test
                ->assertFormSet([
                    'nombre' => $categoria->nombre,
                    'descripcion' => $categoria->descripcion,
                    'parent_id' => $categoria->parent_id,
                    'visible' => $categoria->visible,
                ])
                ->fillForm([
                    'nombre' => $newData['nombre'],
                    'descripcion' => $newData['descripcion'],
                    'parent_id' => $newData['parent_id'],
                    'visible' => $newData['visible'],
                ])
                ->set('data.icono', $file)
                ->call('save')
                ->assertHasNoFormErrors();

            $this->assertTrue($categoria->hasMedia('iconos'));
            $this->assertEquals($initialMediaCount, $categoria->getMedia('iconos')->count());

            $this->assertDatabaseHas(Categoria::class, [
                'nombre' => $newData['nombre'],
                'descripcion' => $newData['descripcion'],
                'parent_id' => $newData['parent_id'],
                'visible' => $newData['visible'],
            ]);
        }

        // 🔴 Test negativos

        $test_roles = $this->_getRolesPositivos(['categoria.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $categoria->refresh();
            // $subcategoria->refresh();

            $test_cases = [
                // caso 1: campos vacíos
                [
                    'payload' => [],
                    'expectedInvalid' => ['nombre']
                ],
            ];

            $test = Livewire::test(EditCategoria::class, ['record' => $categoria->id]);

            foreach ($test_cases as $case) {
                $test
                    ->assertFormSet([
                        'nombre' => $categoria->nombre,
                        'descripcion' => $categoria->descripcion,
                        'parent_id' => $categoria->parent_id,
                        'visible' => $categoria->visible,
                    ])
                    ->fillForm($case['payload'])
                    ->call('save')
                    ->assertHasFormErrors($case['expectedInvalid']);
            }

            $this->assertDatabaseHas(Categoria::class, [
                'nombre' => $categoria->nombre,
                'descripcion' => $categoria->descripcion,
                'parent_id' => $categoria->parent_id,
                'visible' => $categoria->visible,
            ]);
        }
    }

    public function test_el_sistema_elimina_correctamente_una_categoria(): void
    {

        // 🟢 Test positivos        

        $test_roles = $this->_getRolesPositivos(['categoria.delete']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            $categoria = Categoria::factory()->conIcono()->create();
            $this->assertDatabaseCount(Categoria::class, 1);
            $this->assertTrue($categoria->hasMedia('iconos'));
            $this->assertEquals(1, $categoria->getMedia('iconos')->count());

            $test = Livewire::test(ListCategorias::class);

            $test
                ->assertTableActionVisible(DeleteAction::class, $categoria)
                ->callTableAction(DeleteAction::class, $categoria);

            $this->assertModelMissing($categoria);
            $this->assertDatabaseCount(Categoria::class, 0);
            $this->assertDatabaseEmpty('media');
        }

        // 🔴 Test negativos

        $test_roles = [];

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);
        }
    }

    public function test_can_retrieve_all_descendants_recursively()
    {
        // Crear la jerarquía de categorías:
        // - Categoria A
        //   - Categoria B
        //     - Categoria C
        //   - Categoria D

        $categoriaA = Categoria::factory()->create(['nombre' => 'Categoria A']);
        $categoriaB = Categoria::factory()->create(['nombre' => 'Categoria B', 'parent_id' => $categoriaA->id]);
        $categoriaC = Categoria::factory()->create(['nombre' => 'Categoria C', 'parent_id' => $categoriaB->id]);
        $categoriaD = Categoria::factory()->create(['nombre' => 'Categoria D', 'parent_id' => $categoriaA->id]);

        // Recargar la categoría A con sus descendientes
        $descendientesA = $categoriaA->getDescendientes()->pluck('id');

        // dd($descendientesA);

        // Verificar que la categoría A tiene dos subcategorías directas
        $this->assertCount(3, $descendientesA);

        $this->assertTrue($descendientesA->contains($categoriaB->id));
        $this->assertTrue($descendientesA->contains($categoriaC->id));
        $this->assertTrue($descendientesA->contains($categoriaD->id));
    }

    public function _test_el_sistema_____(): void
    {

        // 🟢 Test positivos

        $categoria = Categoria::factory()->create();

        $test_roles = [];

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            // $test = Livewire::test(ListAplicaciones::class);
        }

        // 🔴 Test negativos

        $test_roles = [];

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];

            $this->actingAs($user);

            // $test = Livewire::test(ListAplicaciones::class);
        }
    }
}
