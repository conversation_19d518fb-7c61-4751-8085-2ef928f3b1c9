name: mia
description: "Matalascañas Info Agenda"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.2+30

environment:
   sdk: ^3.5.4

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
   flutter:
      sdk: flutter

   # The following adds the Cupertino Icons font to your application.
   # Use with the CupertinoIcons class for iOS style icons.
   cupertino_icons: ^1.0.8
   # google_fonts: ^6.2.1
   path_provider: ^2.1.5
   http: ^1.3.0
   shared_preferences: ^2.5.1
   fluttertoast: ^8.2.11
   flutter_map: ^7.0.2
   latlong2: ^0.9.1
   flutter_map_animations: ^0.8.0
   mockito: ^5.4.4
   build_runner: ^2.4.13
   flutter_dotenv: ^5.2.1
   flutter_svg: ^2.0.17
   url_launcher: ^6.3.1
   intl: ^0.19.0
   flutter_localizations:
      sdk: flutter
   cached_network_image: ^3.4.1
   flutter_svg_provider: ^1.0.7
   visibility_detector: ^0.4.0+2
   shimmer: ^3.0.0
   auto_size_text: ^3.0.0
   package_info_plus: ^8.3.0
   flutter_cache_manager: ^3.4.1
   provider: ^6.1.4
   permission_handler: ^12.0.0+1
   geolocator: ^14.0.0

dependency_overrides:
   geolocator_android: 4.6.1

dev_dependencies:
   flutter_test:
      sdk: flutter

   # The "flutter_lints" package below contains a set of recommended lints to
   # encourage good coding practices. The lint set provided by the package is
   # activated in the `analysis_options.yaml` file located at the root of your
   # package. See that file for information about deactivating specific lint
   # rules and activating additional ones.
   flutter_lints: ^5.0.0
   mocktail: ^1.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
   # The following line ensures that the Material Icons font is
   # included with your application, so that you can use the icons in
   # the material Icons class.
   uses-material-design: true

   # To add assets to your application, add an assets section, like this:
   assets:
      - assets/mia-splash.png
      - assets/la_piedra.svg
      - assets/piedra_solo_color.png
      - assets/mia.png
      - .env

   # An image asset can refer to one or more resolution-specific "variants", see
   # https://flutter.dev/to/resolution-aware-images

   # For details regarding adding assets from package dependencies, see
   # https://flutter.dev/to/asset-from-package

   # To add custom fonts to your application, add a fonts section here,
   # in this "flutter" section. Each entry in this list should have a
   # "family" key with the font family name, and a "fonts" key with a
   # list giving the asset and other descriptors for the font. For
   # example:
   fonts:
      - family: Nunito
        fonts:
           #   - asset: assets/fonts/Nunito-VariableFont_wght.ttf
           #   - asset: assets/fonts/Nunito-Italic-VariableFont_wght.ttf
           - asset: assets/fonts/Nunito/Nunito-Black.ttf
           - asset: assets/fonts/Nunito/Nunito-BlackItalic.ttf
           - asset: assets/fonts/Nunito/Nunito-Bold.ttf
           - asset: assets/fonts/Nunito/Nunito-BoldItalic.ttf
           - asset: assets/fonts/Nunito/Nunito-ExtraBold.ttf
           - asset: assets/fonts/Nunito/Nunito-ExtraBoldItalic.ttf
           - asset: assets/fonts/Nunito/Nunito-ExtraLight.ttf
           - asset: assets/fonts/Nunito/Nunito-ExtraLightItalic.ttf
           - asset: assets/fonts/Nunito/Nunito-Italic.ttf
           - asset: assets/fonts/Nunito/Nunito-Light.ttf
           - asset: assets/fonts/Nunito/Nunito-LightItalic.ttf
           - asset: assets/fonts/Nunito/Nunito-Medium.ttf
           - asset: assets/fonts/Nunito/Nunito-MediumItalic.ttf
           - asset: assets/fonts/Nunito/Nunito-Regular.ttf
           - asset: assets/fonts/Nunito/Nunito-SemiBold.ttf
           - asset: assets/fonts/Nunito/Nunito-SemiBoldItalic.ttf

   #         style: italic
   #   - family: Trajan Pro
   #     fonts:
   #       - asset: fonts/TrajanPro.ttf
   #       - asset: fonts/TrajanPro_Bold.ttf
   #         weight: 700
   #
   # For details regarding fonts from package dependencies,
   # see https://flutter.dev/to/font-from-package
