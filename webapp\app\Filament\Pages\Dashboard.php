<?php

namespace App\Filament\Pages;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use App\Filament\Widgets\GlobalSummary;
use App\Filament\Widgets\PaymentSummary;
use App\Filament\Widgets\NegociosListWidget;
use App\Filament\Widgets\SuscriptionsSummary;
use App\Filament\Widgets\SuscriptionsOverview;
use Filament\Pages\Dashboard as BaseDashboard;
use App\Filament\Widgets\NegociosMapWithFilters;

class Dashboard extends BaseDashboard
{

    protected static string $view = 'filament.pages.dashboard';

    protected static ?string $title = 'Panel de control';

    public $negocioIdParaEditar = null;

    protected $listeners = [
        'showEditNegocio' => 'showEditNegocio',
    ];





    public function getHeaderWidgets(): array
    {
        return [];
    }

    public function getWidgets(): array
    {
        /** @var User */
        $user = Auth::user();
        if (!$user->can('system.admin-dashboard')) {
            return [
                // SuscriptionsOverview::make(),
                // NegociosListWidget::make(),
            ];
        }

        return [
            // SuscriptionsOverview::make(),
            GlobalSummary::make(),
            SuscriptionsSummary::make(),
            PaymentSummary::make(),
            NegociosMapWithFilters::make(),
        ];
    }



    public function showEditNegocio(int $negocioId)
    {
        // abrir enlace sin modal
        $this->redirect(route('filament.admin.resources.negocios.edit', [
            'record' => $negocioId,
            'origin' => 'dashboard',
        ]), navigate: true);
    }

    public function cerrarModal()
    {
        $this->negocioIdParaEditar = null;
    }
}
