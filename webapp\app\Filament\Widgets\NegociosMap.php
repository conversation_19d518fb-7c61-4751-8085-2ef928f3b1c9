<?php

namespace App\Filament\Widgets;

use DateTime;
use Throwable;
use App\Models\Zona;
use App\Models\Negocio;
use Filament\Forms\Form;
use App\Models\Categoria;
use App\Models\Localidad;
use App\Services\CacheService;
use Illuminate\Support\Carbon;
use App\Enums\EstadoSuscripcion;
use Illuminate\Support\Collection;
use Illuminate\Contracts\View\View;
use Filament\Forms\Components\Select;
use Illuminate\Support\Facades\Cache;
use Filament\Forms\Components\Section;
use Webbingbrasil\FilamentMaps\Marker;
use Webbingbrasil\FilamentMaps\Actions;
use Webbingbrasil\FilamentMaps\Widgets\MapWidget;
use Webbingbrasil\FilamentMaps\Actions\ZoomAction;
use Filament\Widgets\Concerns\InteractsWithPageFilters;

class NegociosMap extends MapWidget
{
    // use InteractsWithPageFilters;

    protected static string $view = 'filament.widgets.map';

    protected int | string | array $columnSpan = 'full';

    public ?Collection $negocios = null;
    public array $filters = [];

    public string $height = "500px";
    protected bool $hasBorder = false;

    public bool $_flag = false;

    protected $listeners = [
        'refreshNegocios' => 'refreshNegociosMap',
        'cambiarSuscripcion' => 'cambiarSuscripcion',
        'showEditNegocio' => 'showEditNegocio',
    ];

    // Funciones de componenente Livewire

    public function mount(?Collection $negocios = null, array $filters = []): void
    {
        $this->negocios = $negocios;
        $this->filters = $filters; // Initialize filters

        $centro = $this->getMapCenter();
        parent::mount();

        $this->centerTo($centro, $this->getMapZoom());
    }

    protected function getViewData(): array
    {
        return [
            'negocios' => $this->negocios,
            'filters' => $this->filters,
        ];
    }

    public function refreshNegociosMap(array $params = [])
    {
        $this->negocios = collect($params['negocios'] ?? []);
        $this->filters = $params['filters'] ?? [];

        $this->mount($this->negocios, $this->filters);
    }

    // Funciones de MapWidget

    public function getMapCenter(): array
    {
        // 1) Comprobar si existe filtro localidad
        $localidad = $this->_getLocalidadEnFiltro();
        if ($localidad) {
            $center = [
                'lat' => $localidad?->ubicacion['latitud'],
                'lng' => $localidad?->ubicacion['longitud'],
            ];
        } else {
            $center = [
                'lat' => 36.9990019,
                'lng' => -6.5478919,
            ];
        }

        return $center;
    }

    // 2) Sobrescribir el zoom inicial
    public function getMapZoom(): int
    {
        return 14; // Un zoom apropiado
    }

    public function getMapOptions(): array
    {
        // if (array_key_exists('localidad', $this->filters) && $this->filters['localidad']) {
        //     $localidad = Localidad::find($this->filters['localidad']);
        //     $maxBounds = [
        //         [
        //             //suroeste
        //             $localidad?->limites['latitud_min'] ?? 37.0328554,
        //             $localidad?->limites['longitud_min'] ?? -6.5732570,
        //         ],
        //         [
        //             //noreste
        //             $localidad?->limites['latitud_max'] ?? 36.9739728,
        //             $localidad?->limites['longitud_max'] ?? -6.5152259,
        //         ],
        //     ];
        // } else {
        //     $maxBounds = [
        //         [37.0328554, -6.5732570], //suroeste
        //         [36.9739728, -6.5152259], //noreste
        //     ];
        // }

        $maxBounds = null;

        // Calcular el maxbounds tomando en cuenta los límites de las localidades, tomando las longitudes mínimas y máximas de todas las localidades y calculando el mínimo y máximo de ellas y añadiendo un margen de 0.1
        $localidades = Localidad::all();
        if ($localidades->count() > 0) {
            $minLat = $localidades->min('limites.latitud_min');
            $maxLat = $localidades->max('limites.latitud_max');
            $minLng = $localidades->min('limites.longitud_min');
            $maxLng = $localidades->max('limites.longitud_max');

            $maxBounds = [
                [$minLat - 0.1, $minLng - 0.1],
                [$maxLat + 0.1, $maxLng + 0.1],
            ];
        }


        return [
            'center' => $this->getMapCenter(),
            'zoom' => $this->getMapZoom(),
            'scrollWheelZoom' => true,
            'dragging' => true,
            'minZoom' => 12,
            // 'maxZoom' => 18,
            'maxBounds' => $maxBounds,
            'maxBoundsViscosity' => 1.0,
        ];
    }

    public function getMarkers(): array
    {
        $markers = [];

        // obtener todos los ids de $this->negocios
        $negocioIds = $this->negocios->pluck('id')->toArray();

        // Modificar esta consulta para incluir eager loading de suscripcion
        $negocios = Negocio::whereIn('id', $negocioIds)
            ->with('suscripcion')
            ->get();

        foreach ($negocios as $negocio) {
            // hacer null check de suscripcion
            if (!isset($negocio->suscripcion->status)) {
                continue;
            }

            $status = $negocio->suscripcion->status;
            $color = $status == EstadoSuscripcion::ACTIVE ? 'green' : 'red';

            // Verificamos que 'ends_at' sea una fecha válida
            try {
                $endsAt = $negocio->suscripcion->ends_at;
                $mensajeEstado = $status == EstadoSuscripcion::ACTIVE
                    ? 'Hasta ' . $endsAt->translatedFormat('d/m/Y') // Formato local
                    : 'Desde ' . $endsAt->translatedFormat('d/m/Y');
            } catch (\Exception $e) {
                // Si la fecha no es válida
                $mensajeEstado = '(Fecha desconocida)';
            }

            $accionSuscripcion = $status == EstadoSuscripcion::ACTIVE ? 'Anular Suscripción' : 'Activar Suscripción';
            $colorSuscripcion = $status == EstadoSuscripcion::ACTIVE ? '#dc3545' : '#28a745'; // Rojo si está activa, verde si está inactiva
            $labelSuscripcion = $status == EstadoSuscripcion::ACTIVE ? 'Activa' : 'Inactiva';

            $popup =
                '<b>' . $negocio['nombre'] . '</b><br/>
                    <span style="color: ' . $color . ';">
                        Suscripción ' . $labelSuscripcion . ' ' . $mensajeEstado . '
                    </span><br/>
                    <button onclick="Livewire.dispatch(\'cambiarSuscripcion\', [' . $negocio['id'] . '])" 
                        style=\'margin-top:5px;padding:5px 10px;background:' . $colorSuscripcion . ';color:white;border:none;border-radius:4px;cursor:pointer;\'>
                        ' . $accionSuscripcion . '
                    </button>
                    <button onclick="Livewire.dispatch(\'showEditNegocio\', [' . $negocio['id'] . '])" 
                        style=\'margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;\'>
                        Editar Negocio
                    </button>';


            $markers[] = Marker::make($negocio->id)
                ->lat($negocio->ubicacion['latitud'])
                ->lng($negocio->ubicacion['longitud'])
                ->color($color)
                ->popup($popup);
        }

        return $markers;
    }

    public function getActions(): array
    {
        return [
            Actions\ZoomAction::make()
                ->position('topright'),
        ];
    }

    public function cambiarSuscripcion(int $negocioId)
    {
        $negocio = Negocio::find($negocioId);

        if ($negocio) {
            // Se obtiene el estado anterior para el toggle
            $estadoAnterior = $negocio->suscripcion->status;
            // Se alterna el estado
            $negocio->suscripcion->status = $estadoAnterior == EstadoSuscripcion::ACTIVE
                ? EstadoSuscripcion::INACTIVE
                : EstadoSuscripcion::ACTIVE;

            // Ahora usamos el estado nuevo para asignar ends_at
            $negocio->suscripcion->ends_at = $negocio->suscripcion->status == EstadoSuscripcion::ACTIVE
                ? Carbon::now()->endOfYear() // Si se activa, la suscripción dura hasta el fin de año
                : now();                     // Si se desactiva, se asigna la fecha actual

            $negocio->suscripcion->save();
            $negocio->updated_at = now();
            $negocio->save();

            // Invalidamos la cache usando el servicio, actualizando el timestamp y olvidando la key
            (new CacheService())->invalidateCache(Negocio::class, 'negocios_all');
        }

        $this->refreshNegociosMap([
            'filters'  => $this->filters,
            'negocios' => $this->negocios,
        ]);
    }

    private function _getLocalidadEnFiltro(): ?Localidad
    {
        if (array_key_exists('localidad', $this->filters) && $this->filters['localidad']) {
            return Localidad::find($this->filters['localidad']);
        }
        return null;
    }

    private function _getBoundsFromLimites(array $limites): array
    {
        return [
            [
                $limites['latitud_min'],
                $limites['longitud_min'],
            ],
            [
                $limites['latitud_max'],
                $limites['longitud_max'],
            ],
        ];
    }
}
