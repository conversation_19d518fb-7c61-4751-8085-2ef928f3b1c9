<?php

namespace App\Enums;

enum EstadoPago: string
{
    case PENDIENTE = 'pendiente';
    case COMPLETADO = 'completado';
    case RECHAZADO = 'rechazado';

    public function message(): string
    {
        return match ($this) {
            self::PENDIENTE => 'El pago está pendiente de procesamiento',
            self::COMPLETADO => 'El pago ha sido completado exitosamente',
            self::RECHAZADO => 'El pago ha sido rechazado',
        };
    }

    public function label(): string
    {
        return match ($this) {
            self::PENDIENTE => 'Pendiente',
            self::COMPLETADO => 'Completado',
            self::RECHAZADO => 'Rechazado',
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::PENDIENTE => 'warning',
            self::COMPLETADO => 'success',
            self::RECHAZADO => 'danger',
        };
    }

    public static function getAsOptions(): array
    {
        $options = [];
        foreach (self::cases() as $case) {
            $options[$case->value] = $case->label();
        }

        return $options;
    }
}
