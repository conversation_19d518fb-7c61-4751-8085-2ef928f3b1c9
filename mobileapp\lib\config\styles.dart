import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:mia/providers/theme_provider.dart';

class AppStyles {
  static final Map<String, TextStyle> _styles = {
    'displayLarge': const TextStyle(fontSize: 69),
    'displaySmall': const TextStyle(fontSize: 57),
    'h1': const TextStyle(fontSize: 48),
    'h2': const TextStyle(fontSize: 40),
    'h3': const TextStyle(fontSize: 33),
    'h4': const TextStyle(fontSize: 28),
    'h5': const TextStyle(fontSize: 23),
    'h6': const TextStyle(fontSize: 19),
    'base': const TextStyle(fontSize: 16),
    's': const TextStyle(fontSize: 13),
    'xs': const TextStyle(fontSize: 11),
  };

  static final Map<String, FontWeight> _fontWeights = {
    'regular': FontWeight.w400,
    'medium': FontWeight.w500,
    'bold': FontWeight.w700,
    'extra': FontWeight.w800,
  };

  /// Método para obtener un estilo con opciones de color y acceso a ThemeProvider
  static TextStyle getStyle(
    BuildContext context,
    String name, {
    String? fontWeight = 'regular',
    Color? color,
  }) {
    final provider = Provider.of<ThemeProvider>(context, listen: false);
    final currentTextColor = color ?? provider.scheme.textColor;

    TextStyle? style = _styles[name];
    FontWeight? weight = _fontWeights[fontWeight];

    style ??= const TextStyle(fontSize: 16);
    weight ??= FontWeight.w400;

    return style.copyWith(
      color: currentTextColor,
      fontWeight: weight,
    );
  }

  /// Método para crear estilos rápidamente
  static TextStyle createStyle({
    required double fontSize,
    FontWeight fontWeight = FontWeight.normal,
    String? fontFamily,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      fontFamily: fontFamily,
    );
  }
}
