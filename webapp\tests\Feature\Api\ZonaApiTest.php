<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Zona;
use Spatie\Permission\Models\Role;
use PHPUnit\Framework\Attributes\Group;
use Database\Seeders\RolesPermissionSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\BaseTest;

#[Group('api')]
#[Group('zona')]
class ZonaApiTest extends BaseTest
{
    use RefreshDatabase;

    private User $user;
    private User $cliente;
    private User $admin;
    private User $super_admin;
    private string $token;
    private string $token_cliente;
    private string $token_admin;
    private string $token_super;

    private array $usuarios;

    private string $api_slug = '/api/v1/zonas';

    protected function setUp(): void
    {
        parent::setUp();

        $this->seed(RolesPermissionSeeder::class);

        foreach (Role::all() as $rol) {
            /** @var User */
            $user = User::factory()->create();
            $user->assignRole($rol->name);

            $this->usuarios[$rol->name] = [
                'usuario' => $user,
                'token' => $user->createToken('auth_toke_' . $rol->name)->plainTextToken,
            ];
        }
    }

    /**
     * Test that the list of zonas is publicly accessible.
     */
    public function test_el_listado_de_zonas_es_accesible_publicamente(): void
    {
        Zona::factory(3)->create();

        $response = $this->getJson("{$this->api_slug}");
        $response->assertStatus(200)
            ->assertJsonCount(3);

        // $roles = Role::all();

        // foreach ($roles as $rol) {
        //     /** @var User */
        //     $user = $this->usuarios[$rol->name]['usuario'];
        //     $this->actingAs($user);

        //     $response = $this->getJson("{$this->api_slug}");
        //     if ($user->can('zona.list')) {
        //         $response->assertStatus(200)
        //             ->assertJsonCount(3);
        //     } else {
        //         $response->assertStatus(403);
        //     }
        // }
    }


    /**
     * Test that a zona can be viewed publicly.
     */
    public function test_mostrar_una_zona_es_accesible_publicamente(): void
    {
        $zona = Zona::factory()->create();

        $response = $this->getJson("{$this->api_slug}/{$zona->id}");
        $response
            ->assertStatus(200)
            ->assertJson(['id' => $zona->id]);

        // $roles = Role::all();

        // foreach ($roles as $rol) {
        //     /** @var User */
        //     $user = $this->usuarios[$rol->name]['usuario'];
        //     $this->actingAs($user);

        //     $response = $this->getJson("{$this->api_slug}/{$zona->id}");
        //     if ($user->can('zona.read')) {
        //         $response
        //             ->assertStatus(200)
        //             ->assertJson(['id' => $zona->id]);
        //     } else {
        //         $response->assertStatus(404); // En el controlador findOrFail lanza la excepción si no tiene permiso, no llega a ejecutarse el authorize
        //     }
        // }
    }


    /**
     * Test that only users with admin roles can create zonas.
     */
    public function test_solo_admins_puede_crear_zonas(): void
    {
        // 🟢 Test positivos

        $test_roles = $this->_getRolesPositivos(['zona.create']);

        foreach ($test_roles as $rol) {
            $this->actingAs($this->usuarios[$rol]['usuario']);

            $data = Zona::factory()->make()->toArray();

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->postJson("{$this->api_slug}", $data);


            $response->assertStatus(201)
                ->assertJsonStructure([
                    'id',
                    'nombre',
                    'descripcion',
                    'coordenadas',
                    'created_at',
                    'updated_at'
                ])
                ->assertJson([
                    'nombre' => $data['nombre'],
                    'descripcion' => $data['descripcion'],
                    'coordenadas' => $data['coordenadas'],
                ]);

            $this->assertDatabaseHas('zonas', [
                'nombre' => $data['nombre'],
                'descripcion' => $data['descripcion'],
                'coordenadas' => json_encode($data['coordenadas']),
            ]);
        }

        // 🔴 Test negativos

        $test_roles = $this->_getRolesNegativos(['zona.create']);

        foreach ($test_roles as $rol) {
            // Cambia el usuario autenticado antes de la solicitud
            $this->actingAs($this->usuarios[$rol]['usuario']);

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->postJson("{$this->api_slug}", $data);

            $response->assertStatus(403); // No autorizado
        }
    }


    public function test_el_sistema_no_crea_nueva_zona_con_datos_inválidos(): void
    {
        // Casos de datos inválidos
        $testCases = [
            // Caso 1: Enviar datos vacíos
            [
                'payload' => [],
                'expectedInvalid' => ['nombre']
            ],

        ];

        // Obtener un rol con permiso de creación
        $rol = $this->_getRolesPositivos(['localidad.create']);

        if (is_array($rol)) {
            $rol = $rol[0] ?? null;
        }

        if (!$rol) {
            return;
        }

        $token = $this->usuarios[$rol]['token'];

        foreach ($testCases as $case) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->postJson("{$this->api_slug}", $case['payload']);
        }

        if (isset($case['expectedForbidden']) && $case['expectedForbidden']) {
            $response->assertForbidden(); // Verificar acceso denegado
        } else {
            $response->assertInvalid($case['expectedInvalid']);
        }
    }


    /**
     * Test that only users with admin roles can update zonas.
     */
    public function test_solo_usuarios_con_permiso_pueden_editar_zonas(): void
    {
        $zona = Zona::factory()->create();

        // 🟢 Test positivos

        $test_roles = $this->_getRolesPositivos(['zona.update']);

        foreach ($test_roles as $rol) {
            $this->actingAs($this->usuarios[$rol]['usuario']);

            $zona->refresh();
            $data = Zona::factory()->make()->toArray();

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->putJson("{$this->api_slug}/{$zona->id}", $data);

            $response->assertStatus(200)
                ->assertJsonStructure([
                    'id',
                    'nombre',
                    'descripcion',
                    'coordenadas',
                    'created_at',
                    'updated_at'
                ])
                ->assertJson([
                    'nombre' => $data['nombre'],
                    'descripcion' => $data['descripcion'],
                    'coordenadas' => $data['coordenadas'],
                ]);

            $this->assertDatabaseHas('zonas', [
                'nombre' => $data['nombre'],
                'descripcion' => $data['descripcion'],
                'coordenadas' => json_encode($data['coordenadas']),
            ]);
        }

        // 🔴 Test negativos

        $test_roles = $this->_getRolesNegativos(['localidad.update']);

        foreach ($test_roles as $rol) {
            // Cambia el usuario autenticado antes de la solicitud
            $this->actingAs($this->usuarios[$rol]['usuario']);

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->putJson("{$this->api_slug}/{$zona->id}", $data);

            $response->assertStatus(403); // No autorizado
        }
    }

    public function test_el_sistem_no_edita_una_zona_con_datos_invalidos(): void
    {
        $test_cases = [
            // Caso 1: Enviar datos vacíos
            [
                'payload' => [],
                'expectedInvalid' => ['nombre']
            ],
        ];

        $zona = Zona::factory()->create();

        $rol = $this->_getRolesPositivos(['zona.update']);
        if (is_array($rol)) {
            $rol = $rol[0] ?? null;
        }
        if (!$rol) {
            return;
        }

        $token = $this->usuarios[$rol]['token'];

        foreach ($test_cases as $case) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->putJson("{$this->api_slug}/{$zona->id}", $case['payload']);
        }

        if (isset($case['expectedForbidden']) && $case['expectedForbidden']) {
            $response->assertForbidden(); // Verificar acceso denegado
        } else {
            $response->assertInvalid($case['expectedInvalid']);
        }
    }


    /**
     * Test that only users with admin roles can delete zonas.
     */
    public function test_solo_los_usuarios_con_permiso_pueden_borrar_zonas(): void
    {
        // 🟢 Test positivos

        $test_roles = $this->_getRolesPositivos(['zona.delete']);

        foreach ($test_roles as $rol) {
            $this->actingAs($this->usuarios[$rol]['usuario']);
            $zona = Zona::factory()->create();

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->deleteJson("{$this->api_slug}/{$zona->id}");

            $response->assertNoContent();

            // Verificar que el negocio no existe en la base de datos
            $this->assertDatabaseMissing(Zona::class, [
                'id' => $zona->id,
            ]);
        }

        // 🔴 Test negativos
        $test_roles = $this->_getRolesNegativos(['zona.delete']);

        $zona = Zona::factory()->create();

        foreach ($test_roles as $rol) {
            // Cambia el usuario autenticado antes de la solicitud
            $this->actingAs($this->usuarios[$rol]['usuario']);

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->deleteJson("{$this->api_slug}/{$zona->id}");

            $response->assertStatus(403); // No autorizado

            // Verificar que el negocio existe en la base de datos
            $this->assertDatabaseHas(Zona::class, [
                'id' => $zona->id,
            ]);
        }
    }
}
