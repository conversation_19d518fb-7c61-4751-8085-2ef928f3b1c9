<?php

namespace App\Console\Commands;

use App\Models\Negocio;
use Illuminate\Console\Command;

class AsignarOrdenNegocios extends Command
{
    protected $signature = 'negocios:asignar-orden';
    protected $description = 'Asigna el orden a todos los negocios según el orden actual de aparición';

    public function handle()
    {
        $negocios = Negocio::with('categorias', 'suscripcion', 'eventos', 'zona')
            ->get();

        foreach ($negocios as $i => $negocio) {
            $negocio->order = $i;
            // $negocio->save();
            // $this->info("{$i} - {$negocio->nombre}");
        }

        // $this->info('Todos los negocios fueron ordenados correctamente.');
        $this->info('Comando desactivado');

        return 0;
    }
}
