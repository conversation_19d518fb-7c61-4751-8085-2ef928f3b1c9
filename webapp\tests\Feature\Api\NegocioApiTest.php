<?php

namespace Tests\Feature\Api;


use Tests\TestCase;
use App\Models\User;
use App\Models\Negocio;
use App\Models\Categoria;
use App\Models\Suscripcion;
use App\Enums\EstadoSuscripcion;
use Spatie\Permission\Models\Role;
use \PHPUnit\Framework\Attributes\Group;
use Database\Seeders\RolesPermissionSeeder;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\BaseTest;

#[Group('api')]
#[Group('negocio')]
class NegocioApiTest extends BaseTest
{
    use RefreshDatabase;
    use WithFaker;

    private array $usuarios;

    private string $api_slug = '/api/v1/negocios';

    // Configuración inicial para cada prueba
    protected function setUp(): void
    {
        parent::setUp();

        $this->seed(RolesPermissionSeeder::class);

        foreach (Role::all() as $rol) {
            $user = User::factory()->create();
            $user->assignRole($rol->name);

            $this->usuarios[$rol->name] = [
                'usuario' => $user,
                'token' => $user->createToken('auth_token_' . $rol->name)->plainTextToken,
            ];
        }
    }


    public function test_el_listado_de_negocios_es_accesible_publicamente()
    {
        Negocio::factory(2)
            ->conSuscripcion(['status' => EstadoSuscripcion::ACTIVE->value])
            ->create();
        Negocio::factory(1)
            ->conSuscripcion(['status' => EstadoSuscripcion::INACTIVE->value])
            ->create();
        $response = $this->getJson("{$this->api_slug}");
        $response->assertStatus(200)
            ->assertJsonCount(2)
            ->assertJsonStructure([
                '*' => [
                    'id',
                    'nombre',
                    'descripcion',
                    'direccion',
                    'ubicacion',
                    'horario',
                    'enlaces_sociales',
                    'enlaces_propios',
                    'contacto',
                    'precios',
                    'zona_id',
                    'user_id',
                    'categorias',
                    'suscripcion',
                ]
            ]);
    }

    public function test_mostrar_un_negocio_es_accesible_publicamente()
    {
        Categoria::factory(3)->create();
        $negocio = Negocio::factory()
            ->conSuscripcion()
            ->create();
        $response = $this->getJson("{$this->api_slug}/{$negocio->id}");
        $response->assertStatus(200)
            ->assertJson([
                'id' => $negocio->id,
                'nombre' => $negocio->nombre,
                'descripcion' => $negocio->descripcion,
                'direccion' => $negocio->direccion,
                'ubicacion' => $negocio->ubicacion,
                'horario' => $negocio->horario,
                'precios' => $negocio->precios,
                'enlaces_sociales' => $negocio->enlaces_sociales,
                'enlaces_propios' => $negocio->enlaces_propios,
                'contacto' => $negocio->contacto,
                'zona_id' => $negocio->zona_id,
                'user_id' => $negocio->user_id,
            ]);
    }

    public function test_solo_usuarios_con_permiso_pueden_crear_negocios(): void
    {
        $cat1 = Categoria::factory()->create();
        $cat2 = Categoria::factory()->create();
        $cat3 = Categoria::factory()->create();

        // 🟢 Test positivos

        $test_roles = $this->_getRolesPositivos(['negocio.create']);

        foreach ($test_roles as $rol) {
            Negocio::truncate();

            $user = $this->usuarios[$rol]['usuario'];
            $this->actingAs($user);

            $data = Negocio::factory()->make()->toArray();
            $statusValue = $this->faker->randomElement([
                EstadoSuscripcion::INACTIVE->value,
                EstadoSuscripcion::ACTIVE->value,
            ]);

            $data['suscripcion'] = $statusValue;

            // Preparar la fecha esperada
            // si es "active" => endOfYear, si es "inactive" => now()
            if ($statusValue === EstadoSuscripcion::ACTIVE->value) {
                // endOfYear sin microsegundos
                $expectedEndsAt = now()->endOfYear()->startOfDay();
            } else {
                // forzamos sin microsegundos
                $expectedEndsAt = now()->copy()->startOfSecond();
            }

            $data['categorias'] = [$cat1->id, $cat2->id, $cat3->id];

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->postJson("{$this->api_slug}", $data);


            $response->assertStatus(201)
                ->assertJsonStructure([
                    'id',
                    'nombre',
                    'descripcion',
                    'direccion',
                    'ubicacion',
                    'horario',
                    'precios',
                    'enlaces_sociales',
                    'enlaces_propios',
                    'contacto',
                    'zona_id',
                    'user_id',
                    'created_at',
                    'updated_at'
                ])
                ->assertJson([
                    'nombre' => $data['nombre'],
                    'descripcion' => $data['descripcion'],
                    'direccion' => $data['direccion'],
                    'ubicacion' => $data['ubicacion'],
                    'horario' => $data['horario'],
                    'precios' => $data['precios'],
                    'enlaces_sociales' => $data['enlaces_sociales'],
                    'enlaces_propios' => $data['enlaces_propios'],
                    'contacto' => $data['contacto'],
                    'zona_id' => $data['zona_id'],
                    'user_id' => $data['user_id'],
                ])
            ;

            $this->assertDatabaseHas('negocios', [
                'nombre' => $data['nombre'],
                'descripcion' => $data['descripcion'],
                'direccion' => $data['direccion'],
                'contacto' => $data['contacto'],
                'zona_id' => $data['zona_id'],
                'user_id' => $data['user_id'],
            ]);

            // Verificar las categorías en la tabla pivote
            $negocioCreado = Negocio::find($response->json('id'));
            foreach ($data['categorias'] as $catId) {
                $this->assertDatabaseHas('categoria_negocio', [
                    'negocio_id' => $negocioCreado->id,
                    'categoria_id' => $catId,
                ]);
            }

            // Verificar que existe un suscripcion en la base de datos
            $this->assertDatabaseHas('suscripciones', [
                'negocio_id' => $response->json('id'),
                'status' => $statusValue,
            ]);

            $suscripcion = Suscripcion::where('negocio_id', $response->json('id'))->first();

            $this->assertTrue(
                $suscripcion->ends_at->isSameDay($expectedEndsAt),
                "ends_at no coincide con la fecha esperada. ends_at=" . $suscripcion->ends_at
            );

            // Verificar campos JSON directamente desde el modelo
            $negocioCreado = Negocio::first();

            $this->assertEquals(
                $data['ubicacion'],
                $negocioCreado->ubicacion,
                'El campo ubicación no coincide'
            );

            $this->assertEquals(
                $data['horario'],
                $negocioCreado->horario,
                'El campo horario no coincide'
            );

            $this->assertEquals(
                $data['precios'],
                $negocioCreado->precios,
                'El campo precios no coincide'
            );

            $this->assertEquals(
                $data['enlaces_sociales'],
                $negocioCreado->enlaces_sociales,
                'El campo enlaces_sociales no coincide'
            );

            $this->assertEquals(
                $data['enlaces_propios'],
                $negocioCreado->enlaces_propios,
                'El campo enlaces_propios no coincide'
            );
        }

        // 🔴 Test negativos
        $test_roles = $this->_getRolesNegativos(['negocio.create']);

        foreach ($test_roles as $rol) {
            Negocio::truncate();

            $this->actingAs($this->usuarios[$rol]['usuario']);

            $data = Negocio::factory()->make()->toArray();

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->postJson("{$this->api_slug}", $data);

            $response->assertStatus(403);

            $this->assertDatabaseCount(Negocio::class, 0);
        }
    }


    public function test_el_sistema_no_crea_nuevo_negocio_con_datos_inválidos()
    {
        // Casos de datos inválidos
        $testCases = [
            // Caso 1: Enviar datos vacíos
            [
                'payload' => [],
                'expectedInvalid' => ['nombre', 'direccion', 'ubicacion', 'ubicacion.latitud', 'ubicacion.longitud', 'contacto', 'user_id']
            ],
            // Caso 2: Enviar un nombre demasiado largo
            [
                'payload' => [
                    'nombre' => str_repeat('A', 101), // 101 caracteres
                    'direccion' => 'test',
                    'ubicacion' => ['latitud' => 10, 'longitud' => 20],
                    'contacto' => '<EMAIL>',
                    'user_id' => 1
                ],
                'expectedInvalid' => ['nombre']
            ],
            // Caso 3: Coordenadas fuera de rango
            [
                'payload' => [
                    'nombre' => 'Negocio Test',
                    'direccion' => 'test',
                    'ubicacion' => ['latitud' => -95, 'longitud' => 200],
                    'contacto' => '<EMAIL>',
                    'user_id' => 1
                ],
                'expectedInvalid' => ['ubicacion.latitud', 'ubicacion.longitud']
            ],
            // Caso 4: Contacto faltante
            [
                'payload' => [
                    'nombre' => 'Negocio Test',
                    'direccion' => 'test',
                    'ubicacion' => ['latitud' => 10, 'longitud' => 20],
                    'user_id' => 1
                ],
                'expectedInvalid' => ['contacto']
            ],
            // Caso 5: Usuario no existente
            [
                'payload' => [
                    'nombre' => 'Negocio Test',
                    'direccion' => 'test',
                    'ubicacion' => ['latitud' => 10, 'longitud' => 20],
                    'contacto' => '<EMAIL>',
                    'user_id' => 99999 // ID inexistente
                ],
                'expectedInvalid' => ['user_id']
            ],
            // Caso 6: Usuario intenta crear un negocio para otro usuario sin permiso
            [
                'payload' => [
                    'nombre' => 'Negocio Test',
                    'direccion' => 'test',
                    'ubicacion' => ['latitud' => 10, 'longitud' => 20],
                    'contacto' => '<EMAIL>',
                    'user_id' => 2 // Usuario diferente
                ],
                'expectedInvalid' => ['user_id'],
            ],
            // Caso 7: Direccion demasiado larga
            [
                'payload' => [
                    'nombre' => 'test',
                    'direccion' => str_repeat('A', 256),
                    'ubicacion' => ['latitud' => 10, 'longitud' => 20],
                    'contacto' => '<EMAIL>',
                    'user_id' => 1
                ],
                'expectedInvalid' => ['direccion']
            ],
            // Caso 8: Precios inválidos
            [
                'payload' => [
                    'precios' => [
                        [
                            'categoria' => '',
                            'producto' => [
                                ['nombre' => '', 'precio' => 'invalid']
                            ]
                        ]
                    ]
                ],
                'expectedInvalid' => ['precios.0.categoria', 'precios.0.producto.0.nombre', 'precios.0.producto.0.precio']
            ],
            // Caso 9: Enlaces inválidos
            [
                'payload' => [
                    'enlaces_sociales' => [
                        [
                            'plataforma' => '',
                            'url' => 'invalid'
                        ]
                    ]
                ],
                'expectedInvalid' => ['enlaces_sociales.0.plataforma', 'enlaces_sociales.0.url']
            ],
            // Caso 10: Enlaces propios inválidos
            [
                'payload' => [
                    'enlaces_propios' => [
                        [
                            '' => '',
                        ]
                    ]
                ],
                'expectedInvalid' => ['enlaces_propios.0']
            ],
            // Caso 11: Enlaces propios inválidos
            [
                'payload' => [
                    'enlaces_propios' => [
                        [
                            'key' => 'invalid',
                        ]
                    ]
                ],
                'expectedInvalid' => ['enlaces_propios.0']
            ],
            // Caso 12: Valor de suscripcion inválido
            [
                'payload' => [
                    'suscripcion' => 9999,
                ],
                'expectedInvalid' => ['suscripcion']
            ],
            [
                'payload' => [
                    'categorias' => [],
                ],
                'expectedInvalid' => ['categorias'], // si tu resource requiere al menos 1
            ],
        ];

        $rol = $this->_getRolesPositivos(['negocio.create']);
        if (is_array($rol)) {
            $rol = $rol[0] ?? null;
        }
        if (!$rol) {
            return;
        }

        foreach ($testCases as $case) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->postJson("{$this->api_slug}", $case['payload']);
        }

        if (isset($case['expectedForbidden']) && $case['expectedForbidden']) {
            $response->assertForbidden(); // Verificar acceso denegado
        } else {
            $response->assertInvalid($case['expectedInvalid']);
        }

        // verificar que no se creo ningun registro en la base de datos
        $this->assertDatabaseCount(Negocio::class, 0);
        $this->assertDatabaseCount(Suscripcion::class, 0);
    }


    // Test: Usuario autenticado puede editar un negocio que le pertenece con datos válidos
    public function test_solo_usuarios_con_permiso_pueden_editar_negocios()
    {
        $cat1 = Categoria::factory()->create();
        $cat2 = Categoria::factory()->create();
        $cat3 = Categoria::factory()->create();

        $user = User::factory()->create();

        $this->usuarios['usuario_propietario']['usuario'] = $user;
        $this->usuarios['usuario_propietario']['token'] = $user->createToken('auth_token_usuario_propietario')->plainTextToken;

        // 🟢 Test positivos

        $test_roles = $this->_getRolesPositivos(['negocio.update']);
        $test_roles[] = 'usuario_propietario';

        foreach ($test_roles as $rol) {
            Negocio::truncate();

            $this->actingAs($this->usuarios[$rol]['usuario']);
            $negocio = Negocio::factory()
                ->conSuscripcion()
                ->create(['user_id' => $user->id]);

            // Datos actualizados para el negocio
            $updatedData = Negocio::factory()->make(['user_id' => $user->id])->toArray();
            $updatedData['categorias'] = [$cat1->id, $cat3->id];

            $statusValue = $this->faker->randomElement([
                EstadoSuscripcion::INACTIVE->value,
                EstadoSuscripcion::ACTIVE->value,
            ]);

            $updatedData['suscripcion'] = $statusValue;

            // Preparar la fecha esperada
            // si es "active" => endOfYear, si es "inactive" => now()
            if ($statusValue === EstadoSuscripcion::ACTIVE->value) {
                // endOfYear sin microsegundos
                $expectedEndsAt = now()->endOfYear()->startOfDay();
            } else {
                // forzamos sin microsegundos
                $expectedEndsAt = now()->copy()->startOfSecond();
            }

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->putJson("{$this->api_slug}/{$negocio->id}", $updatedData);

            // Verificar estructura de respuesta y valores básicos
            $response
                ->assertStatus(200)
                ->assertJsonStructure([
                    'id',
                    'nombre',
                    'descripcion',
                    'direccion',
                    'ubicacion',
                    'horario',
                    'enlaces_sociales',
                    'enlaces_propios',
                    'contacto',
                    'precios',
                    'zona_id',
                    'user_id',
                    'created_at',
                    'updated_at',
                ])
                ->assertJson([
                    'id' => $negocio->id,
                    'nombre' => $updatedData['nombre'],
                    'descripcion' => $updatedData['descripcion'],
                    'direccion' => $updatedData['direccion'],
                    'ubicacion' => $updatedData['ubicacion'],
                    'horario' => $updatedData['horario'],
                    'enlaces_sociales' => $updatedData['enlaces_sociales'],
                    'enlaces_propios' => $updatedData['enlaces_propios'],
                    'precios' => $updatedData['precios'],
                    'contacto' => $updatedData['contacto'],
                    'zona_id' => $updatedData['zona_id'],
                    'user_id' => $updatedData['user_id'],
                ]);

            // Verificar que los cambios se reflejan en la base de datos
            $this->assertDatabaseHas('negocios', [
                'id' => $negocio->id,
                'nombre' => $updatedData['nombre'],
                'descripcion' => $updatedData['descripcion'],
                'direccion' => $updatedData['direccion'],
                'contacto' => $updatedData['contacto'],
                'zona_id' => $updatedData['zona_id'],
                'user_id' => $updatedData['user_id'],
            ]);
            // Verificar las categorías en la tabla pivote
            foreach ($updatedData['categorias'] as $catId) {
                $this->assertDatabaseHas('categoria_negocio', [
                    'negocio_id' => $negocio->id,
                    'categoria_id' => $catId,
                ]);
            }

            // Verificar que existe un suscripcion en la base de datos
            $this->assertDatabaseHas('suscripciones', [
                'negocio_id' => $negocio->id,
                'status' => $statusValue,
            ]);

            // Verificar campos JSON directamente desde el modelo
            $negocioActualizado = Negocio::find($negocio->id);
            $suscripcion = $negocioActualizado->suscripcion;
            $this->assertTrue(
                $suscripcion->ends_at->isSameDay($expectedEndsAt),
                "ends_at no coincide con la fecha esperada. ends_at=" . $suscripcion->ends_at
            );

            $this->assertEquals(
                $updatedData['ubicacion'],
                $negocioActualizado->ubicacion,
                'El campo ubicación no coincide después de la actualización'
            );

            $this->assertEquals(
                $updatedData['horario'],
                $negocioActualizado->horario,
                'El campo horario no coincide después de la actualización'
            );

            $this->assertEquals(
                $updatedData['precios'],
                $negocioActualizado->precios,
                'El campo precios no coincide después de la actualización'
            );

            $this->assertEquals(
                $updatedData['enlaces_sociales'],
                $negocioActualizado->enlaces_sociales,
                'El campo enlaces_sociales no coincide después de la actualización'
            );

            $this->assertEquals(
                $updatedData['enlaces_propios'],
                $negocioActualizado->enlaces_propios,
                'El campo enlaces_propios no coincide después de la actualización'
            );
        }

        // 🔴 Test negativo
        $test_roles = $this->_getRolesNegativos(['negocio.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];
            $token = $this->usuarios[$rol]['token'];

            $this->actingAs($user);

            $negocio = Negocio::factory()->create(['user_id' => $this->usuarios['admin']['usuario']->id]);
            $data = Negocio::factory()->make()->toArray();
            $data['categorias'] = [$cat2->id];

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->putJson("{$this->api_slug}/{$negocio->id}", $data);

            $response->assertStatus(403);
        }
    }

    public function test_el_sistema_no_actualiza_negocio_con_datos_inválidos()
    {
        // Crear un negocio asociado al usuario autenticado
        $negocio = Negocio::factory()->create();

        // Casos de datos inválidos
        $testCases = [
            // Caso 1: Enviar datos vacíos
            [
                'payload' => [],
                'expectedInvalid' => ['user_id'],
            ],
            // Caso 2: Enviar un nombre demasiado largo
            [
                'payload' => [
                    'nombre' => str_repeat('A', 101), // 101 caracteres
                    'ubicacion' => ['latitud' => 10, 'longitud' => 20],
                    'contacto' => '<EMAIL>',
                    'user_id' => $negocio->user_id,
                ],
                'expectedInvalid' => ['nombre'],
            ],
            // Caso 3: Coordenadas fuera de rango
            [
                'payload' => [
                    'nombre' => 'Negocio Test',
                    'ubicacion' => ['latitud' => -95, 'longitud' => 200],
                    'contacto' => '<EMAIL>',
                    'user_id' => $negocio->user_id,
                ],
                'expectedInvalid' => ['ubicacion.latitud', 'ubicacion.longitud'],
            ],
            // // Caso 4: Contacto faltante
            // [
            //     'payload' => [
            //         'nombre' => 'Negocio Test',
            //         'ubicacion' => ['latitud' => 10, 'longitud' => 20],
            //         'user_id' => $negocio->user_id,
            //     ],
            //     'expectedInvalid' => ['contacto'],
            // ],
            // Caso 5: Categoría inexistente
            // [
            //     'payload' => [
            //         'categoria_id' => 9999,  // ID que no existe en la base de datos
            //         'user_id' => $negocio->user_id,
            //     ],
            //     'expectedInvalid' => ['categoria_id']
            // ],
            // Caso 6: Actualización para otro usuario (sin permisos)
            [
                'payload' => [
                    'user_id' => 9999,  // ID de usuario que no existe
                ],
                'expectedInvalid' => ['user_id'],
            ],
            // Caso 7: direccion demasiado largo
            [
                'payload' => [
                    'direccion' => str_repeat('A', 256),
                ],
                'expectedInvalid' => ['direccion'],
            ],
            // Caso 8: Precios inválidos
            [
                'payload' => [
                    'precios' => [
                        [
                            'categoria' => '',
                            'productos' => [
                                ['nombre' => '', 'precio' => '']
                            ]
                        ]
                    ]
                ],
                'expectedInvalid' => [
                    'precios.0.categoria',
                    'precios.0.productos.0',
                ]
            ],
            // Caso 9: Enlaces inválidos
            [
                'payload' => [
                    'enlaces_sociales' => [
                        [
                            'plataforma' => '',
                            'url' => 'invalid'
                        ]
                    ]
                ],
                'expectedInvalid' => ['enlaces_sociales.0.plataforma', 'enlaces_sociales.0.url']
            ],
            // Caso 10: Enlaces propios inválidos
            [
                'payload' => [
                    'enlaces_propios' => [
                        [
                            '' => '',
                        ]
                    ]
                ],
                'expectedInvalid' => ['enlaces_propios.0']
            ],
            // Caso 11: Enlaces propios inválidos
            [
                'payload' => [
                    'enlaces_propios' => [
                        [
                            'key' => 'invalid',
                        ]
                    ]
                ],
                'expectedInvalid' => ['enlaces_propios.0']
            ],
            // Caso 12: Valor de suscripcion inválido
            [
                'payload' => [
                    'suscripcion' => 9999,
                    'user_id' => $negocio->user_id,
                ],
                'expectedInvalid' => ['suscripcion']
            ],
        ];

        $rol = $this->_getRolesPositivos(['negocio.update']);
        if (is_array($rol)) {
            $rol = $rol[0] ?? null;
        }
        if (!$rol) {
            return;
        }
        $token = $this->usuarios[$rol]['token'];

        foreach ($testCases as $case) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->putJson("{$this->api_slug}/{$negocio->id}", $case['payload']);
        }
        if (isset($case['expectedForbidden']) && $case['expectedForbidden']) {
            $response->assertForbidden(); // Verificar acceso denegado
        } else {
            $response->assertInvalid($case['expectedInvalid']);
        }
    }


    // Test: Usuario no puede actualizar negocios de otros
    public function test_usuario_no_puede_editar_negocio_de_otro_usuario()
    {
        $categoria = Categoria::factory()->create();

        // crear dos usuarios clientes
        $user1 = User::factory()->create();
        $user1->assignRole('cliente');
        $user1_token = $user1->createToken('auth_token_usuario1')->plainTextToken;

        $user2 = User::factory()->create();

        $negocio = Negocio::factory()->create(['user_id' => $user2->id]);

        // Datos de actualización
        $data = [
            'nombre' => 'Nuevo Nombre No Autorizado',
            'ubicacion' => ['latitud' => 45.67, 'longitud' => 78.90],
            'contacto' => '<EMAIL>',
            'categorias' => [$categoria->id],
        ];

        // Intentar actualizar el negocio del otro usuario
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $user1_token,
        ])->putJson("{$this->api_slug}/{$negocio->id}", $data);

        // Verificar que el acceso fue denegado
        $response->assertStatus(403);
    }

    public function test_solo_usuarios_con_permiso_pueden_borrar_negocios()
    {
        // 🟢 Test positivos
        $test_roles = $this->_getRolesPositivos(['negocio.delete']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];
            $token = $this->usuarios[$rol]['token'];

            $this->actingAs($user);

            $negocio = Negocio::factory()->create(['user_id' => $user->id]);

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->deleteJson("{$this->api_slug}/{$negocio->id}");

            $response->assertNoContent();

            // Verificar que el negocio no existe en la base de datos
            $this->assertDatabaseMissing(Negocio::class, [
                'id' => $negocio->id,
            ]);
        }

        // 🔴 Test negativos
        $test_roles = $this->_getRolesNegativos(['negocio.delete']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];
            $token = $this->usuarios[$rol]['token'];

            $this->actingAs($user);

            $negocio = Negocio::factory()->create(['user_id' => $user->id]);

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->deleteJson("{$this->api_slug}/{$negocio->id}");

            $response->assertForbidden();

            // Verificar que el negocio existe en la base de datos
            $this->assertDatabaseHas(Negocio::class, [
                'id' => $negocio->id,
            ]);
        }
    }
}
