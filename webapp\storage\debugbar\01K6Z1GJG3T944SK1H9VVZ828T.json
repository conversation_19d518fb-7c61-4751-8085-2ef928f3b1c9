{"__meta": {"id": "01K6Z1GJG3T944SK1H9VVZ828T", "datetime": "2025-10-07 09:54:46", "utime": **********.916067, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1759830884.707212, "end": **********.916094, "duration": 2.2088820934295654, "duration_str": "2.21s", "measures": [{"label": "Booting", "start": 1759830884.707212, "relative_start": 0, "end": **********.573957, "relative_end": **********.573957, "duration": 0.****************, "duration_str": "867ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.573992, "relative_start": 0.****************, "end": **********.916097, "relative_end": 2.86102294921875e-06, "duration": 1.****************, "duration_str": "1.34s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.746924, "relative_start": 2.****************, "end": **********.749204, "relative_end": **********.749204, "duration": 0.002279996871948242, "duration_str": "2.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament.widgets.payment-summary", "start": **********.884821, "relative_start": 2.****************, "end": **********.884821, "relative_end": **********.884821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2a817ac5b713cc341d6095c506a99fdd", "start": **********.891976, "relative_start": 2.****************, "end": **********.891976, "relative_end": **********.891976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d2b27f499c5f47b7f077d6781417d93d", "start": **********.894364, "relative_start": 2.187152147293091, "end": **********.894364, "relative_end": **********.894364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da60d970b8aeb247c8eec4a1bf65027f", "start": **********.897344, "relative_start": 2.1901321411132812, "end": **********.897344, "relative_end": **********.897344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::22ebec865223eccfbe3f9931e31c212a", "start": **********.899306, "relative_start": 2.192094087600708, "end": **********.899306, "relative_end": **********.899306, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da60d970b8aeb247c8eec4a1bf65027f", "start": **********.90036, "relative_start": 2.193148136138916, "end": **********.90036, "relative_end": **********.90036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.912043, "relative_start": 2.204831123352051, "end": **********.913299, "relative_end": **********.913299, "duration": 0.0012559890747070312, "duration_str": "1.26ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 46093992, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "mia.test", "Timezone": "UTC", "Locale": "es"}}, "views": {"count": 6, "nb_templates": 6, "templates": [{"name": "filament.widgets.payment-summary", "param_count": null, "params": [], "start": **********.884735, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\resources\\views/filament/widgets/payment-summary.blade.phpfilament.widgets.payment-summary", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fpayment-summary.blade.php&line=1", "ajax": false, "filename": "payment-summary.blade.php", "line": "?"}}, {"name": "__components::2a817ac5b713cc341d6095c506a99fdd", "param_count": null, "params": [], "start": **********.891913, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/2a817ac5b713cc341d6095c506a99fdd.blade.php__components::2a817ac5b713cc341d6095c506a99fdd", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F2a817ac5b713cc341d6095c506a99fdd.blade.php&line=1", "ajax": false, "filename": "2a817ac5b713cc341d6095c506a99fdd.blade.php", "line": "?"}}, {"name": "__components::d2b27f499c5f47b7f077d6781417d93d", "param_count": null, "params": [], "start": **********.894228, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/d2b27f499c5f47b7f077d6781417d93d.blade.php__components::d2b27f499c5f47b7f077d6781417d93d", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2Fd2b27f499c5f47b7f077d6781417d93d.blade.php&line=1", "ajax": false, "filename": "d2b27f499c5f47b7f077d6781417d93d.blade.php", "line": "?"}}, {"name": "__components::da60d970b8aeb247c8eec4a1bf65027f", "param_count": null, "params": [], "start": **********.897237, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/da60d970b8aeb247c8eec4a1bf65027f.blade.php__components::da60d970b8aeb247c8eec4a1bf65027f", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2Fda60d970b8aeb247c8eec4a1bf65027f.blade.php&line=1", "ajax": false, "filename": "da60d970b8aeb247c8eec4a1bf65027f.blade.php", "line": "?"}}, {"name": "__components::22ebec865223eccfbe3f9931e31c212a", "param_count": null, "params": [], "start": **********.899198, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/22ebec865223eccfbe3f9931e31c212a.blade.php__components::22ebec865223eccfbe3f9931e31c212a", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F22ebec865223eccfbe3f9931e31c212a.blade.php&line=1", "ajax": false, "filename": "22ebec865223eccfbe3f9931e31c212a.blade.php", "line": "?"}}, {"name": "__components::da60d970b8aeb247c8eec4a1bf65027f", "param_count": null, "params": [], "start": **********.90022, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/da60d970b8aeb247c8eec4a1bf65027f.blade.php__components::da60d970b8aeb247c8eec4a1bf65027f", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2Fda60d970b8aeb247c8eec4a1bf65027f.blade.php&line=1", "ajax": false, "filename": "da60d970b8aeb247c8eec4a1bf65027f.blade.php", "line": "?"}}]}, "queries": {"count": 12, "nb_statements": 12, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02925, "accumulated_duration_str": "29.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'cdl2cENjcuWNzf6gG8eyM59qVcY8AHRkO8X6P3aw' limit 1", "type": "query", "params": [], "bindings": ["cdl2cENjcuWNzf6gG8eyM59qVcY8AHRkO8X6P3aw"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.7577548, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "proyecto-mba", "explain": null, "start_percent": 0, "width_percent": 7.453}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7766688, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "proyecto-mba", "explain": null, "start_percent": 7.453, "width_percent": 2.701}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.7830079, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "proyecto-mba", "explain": null, "start_percent": 10.154, "width_percent": 2.085}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (1) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.7957232, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "proyecto-mba", "explain": null, "start_percent": 12.239, "width_percent": 3.316}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.798354, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "proyecto-mba", "explain": null, "start_percent": 15.556, "width_percent": 3.145}, {"sql": "select count(*) as aggregate from `pago_suscripcions` where `fecha_pago` between '2025-04-01 00:00:00' and '2026-03-31 23:59:59' and `estado` = 'pendiente'", "type": "query", "params": [], "bindings": ["2025-04-01 00:00:00", "2026-03-31 23:59:59", "pendiente"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/PaymentSummary.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\PaymentSummary.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.815207, "duration": 0.01072, "duration_str": "10.72ms", "memory": 0, "memory_str": null, "filename": "PaymentSummary.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/PaymentSummary.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\PaymentSummary.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FWidgets%2FPaymentSummary.php&line=36", "ajax": false, "filename": "PaymentSummary.php", "line": "36"}, "connection": "proyecto-mba", "explain": null, "start_percent": 18.701, "width_percent": 36.65}, {"sql": "select count(*) as aggregate from `pago_suscripcions` where `fecha_pago` between '2025-04-01 00:00:00' and '2026-03-31 23:59:59' and `estado` = 'completado'", "type": "query", "params": [], "bindings": ["2025-04-01 00:00:00", "2026-03-31 23:59:59", "completado"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/PaymentSummary.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\PaymentSummary.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.83019, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "PaymentSummary.php:37", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/PaymentSummary.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\PaymentSummary.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FWidgets%2FPaymentSummary.php&line=37", "ajax": false, "filename": "PaymentSummary.php", "line": "37"}, "connection": "proyecto-mba", "explain": null, "start_percent": 55.35, "width_percent": 2.496}, {"sql": "select count(*) as aggregate from `pago_suscripcions` where `estado` = 'rechazado'", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON>"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/PaymentSummary.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\PaymentSummary.php", "line": 38}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.8343809, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "PaymentSummary.php:38", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/PaymentSummary.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\PaymentSummary.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FWidgets%2FPaymentSummary.php&line=38", "ajax": false, "filename": "PaymentSummary.php", "line": "38"}, "connection": "proyecto-mba", "explain": null, "start_percent": 57.846, "width_percent": 3.111}, {"sql": "select * from `suscripciones` where `plan` != 'free' and `status` = 1 and not exists (select * from `pago_suscripcions` where `suscripciones`.`id` = `pago_suscripcions`.`suscripcion_id` and `fecha_pago` between '2025-04-01 00:00:00' and '2026-03-31 23:59:59' and `estado` != 'rechazado')", "type": "query", "params": [], "bindings": ["free", 1, "2025-04-01 00:00:00", "2026-03-31 23:59:59", "<PERSON><PERSON><PERSON><PERSON>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/PaymentSummary.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\PaymentSummary.php", "line": 41}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.84351, "duration": 0.00937, "duration_str": "9.37ms", "memory": 0, "memory_str": null, "filename": "PaymentSummary.php:41", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/PaymentSummary.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\PaymentSummary.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FWidgets%2FPaymentSummary.php&line=41", "ajax": false, "filename": "PaymentSummary.php", "line": "41"}, "connection": "proyecto-mba", "explain": null, "start_percent": 60.957, "width_percent": 32.034}, {"sql": "select sum(`importe`) as aggregate from `pago_suscripcions` where `fecha_pago` between '2025-04-01 00:00:00' and '2026-03-31 23:59:59' and `estado` = 'pendiente'", "type": "query", "params": [], "bindings": ["2025-04-01 00:00:00", "2026-03-31 23:59:59", "pendiente"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/PaymentSummary.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\PaymentSummary.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.85675, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "PaymentSummary.php:47", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/PaymentSummary.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\PaymentSummary.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FWidgets%2FPaymentSummary.php&line=47", "ajax": false, "filename": "PaymentSummary.php", "line": "47"}, "connection": "proyecto-mba", "explain": null, "start_percent": 92.991, "width_percent": 3.385}, {"sql": "select sum(`importe`) as aggregate from `pago_suscripcions` where `fecha_pago` between '2025-04-01 00:00:00' and '2026-03-31 23:59:59' and `estado` = 'completado'", "type": "query", "params": [], "bindings": ["2025-04-01 00:00:00", "2026-03-31 23:59:59", "completado"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/PaymentSummary.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\PaymentSummary.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.8613918, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "PaymentSummary.php:48", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/PaymentSummary.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\PaymentSummary.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FWidgets%2FPaymentSummary.php&line=48", "ajax": false, "filename": "PaymentSummary.php", "line": "48"}, "connection": "proyecto-mba", "explain": null, "start_percent": 96.376, "width_percent": 2.051}, {"sql": "select sum(`importe`) as aggregate from `pago_suscripcions` where `estado` = 'rechazado'", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON>"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/PaymentSummary.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\PaymentSummary.php", "line": 49}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.865011, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "PaymentSummary.php:49", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/PaymentSummary.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\PaymentSummary.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FWidgets%2FPaymentSummary.php&line=49", "ajax": false, "filename": "PaymentSummary.php", "line": "49"}, "connection": "proyecto-mba", "explain": null, "start_percent": 98.427, "width_percent": 1.573}]}, "models": {"data": {"App\\Models\\Suscripcion": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FModels%2FSuscripcion.php&line=1", "ajax": false, "filename": "Suscripcion.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 13, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.payment-summary #uDiAhEytKZVmgZ1qw5fa": "array:4 [\n  \"data\" => array:12 [\n    \"pagosPendientes\" => 3\n    \"pagosCompletados\" => 5\n    \"pagosRechazados\" => 3\n    \"pagosTotales\" => 8\n    \"suscripcionesSinPagosEnTemporadaActual\" => Illuminate\\Database\\Eloquent\\Collection {#3177\n      #items: array:10 [\n        0 => App\\Models\\Suscripcion {#3122\n          #connection: \"mysql\"\n          #table: \"suscripciones\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 8\n            \"negocio_id\" => 8\n            \"plan\" => \"basic\"\n            \"status\" => 1\n            \"started_at\" => \"2025-10-02 10:39:08\"\n            \"ends_at\" => \"2025-12-21 00:29:04\"\n            \"created_at\" => \"2025-10-02 10:39:08\"\n            \"updated_at\" => \"2025-10-02 10:39:08\"\n            \"precio\" => \"0.00\"\n          ]\n          #original: array:9 [\n            \"id\" => 8\n            \"negocio_id\" => 8\n            \"plan\" => \"basic\"\n            \"status\" => 1\n            \"started_at\" => \"2025-10-02 10:39:08\"\n            \"ends_at\" => \"2025-12-21 00:29:04\"\n            \"created_at\" => \"2025-10-02 10:39:08\"\n            \"updated_at\" => \"2025-10-02 10:39:08\"\n            \"precio\" => \"0.00\"\n          ]\n          #changes: []\n          #casts: array:4 [\n            \"status\" => \"App\\Enums\\EstadoSuscripcion\"\n            \"plan\" => \"App\\Enums\\TipoSuscripcion\"\n            \"started_at\" => \"datetime\"\n            \"ends_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"negocio_id\"\n            1 => \"plan\"\n            2 => \"status\"\n            3 => \"precio\"\n            4 => \"started_at\"\n            5 => \"ends_at\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        1 => App\\Models\\Suscripcion {#3253\n          #connection: \"mysql\"\n          #table: \"suscripciones\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 10\n            \"negocio_id\" => 10\n            \"plan\" => \"basic\"\n            \"status\" => 1\n            \"started_at\" => \"2025-10-02 10:39:13\"\n            \"ends_at\" => \"2026-02-15 11:04:59\"\n            \"created_at\" => \"2025-10-02 10:39:13\"\n            \"updated_at\" => \"2025-10-02 10:39:13\"\n            \"precio\" => \"0.00\"\n          ]\n          #original: array:9 [\n            \"id\" => 10\n            \"negocio_id\" => 10\n            \"plan\" => \"basic\"\n            \"status\" => 1\n            \"started_at\" => \"2025-10-02 10:39:13\"\n            \"ends_at\" => \"2026-02-15 11:04:59\"\n            \"created_at\" => \"2025-10-02 10:39:13\"\n            \"updated_at\" => \"2025-10-02 10:39:13\"\n            \"precio\" => \"0.00\"\n          ]\n          #changes: []\n          #casts: array:4 [\n            \"status\" => \"App\\Enums\\EstadoSuscripcion\"\n            \"plan\" => \"App\\Enums\\TipoSuscripcion\"\n            \"started_at\" => \"datetime\"\n            \"ends_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"negocio_id\"\n            1 => \"plan\"\n            2 => \"status\"\n            3 => \"precio\"\n            4 => \"started_at\"\n            5 => \"ends_at\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        2 => App\\Models\\Suscripcion {#3179\n          #connection: \"mysql\"\n          #table: \"suscripciones\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 17\n            \"negocio_id\" => 17\n            \"plan\" => \"basic\"\n            \"status\" => 1\n            \"started_at\" => \"2025-10-02 10:39:38\"\n            \"ends_at\" => \"2025-12-14 15:17:04\"\n            \"created_at\" => \"2025-10-02 10:39:38\"\n            \"updated_at\" => \"2025-10-02 10:39:38\"\n            \"precio\" => \"0.00\"\n          ]\n          #original: array:9 [\n            \"id\" => 17\n            \"negocio_id\" => 17\n            \"plan\" => \"basic\"\n            \"status\" => 1\n            \"started_at\" => \"2025-10-02 10:39:38\"\n            \"ends_at\" => \"2025-12-14 15:17:04\"\n            \"created_at\" => \"2025-10-02 10:39:38\"\n            \"updated_at\" => \"2025-10-02 10:39:38\"\n            \"precio\" => \"0.00\"\n          ]\n          #changes: []\n          #casts: array:4 [\n            \"status\" => \"App\\Enums\\EstadoSuscripcion\"\n            \"plan\" => \"App\\Enums\\TipoSuscripcion\"\n            \"started_at\" => \"datetime\"\n            \"ends_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"negocio_id\"\n            1 => \"plan\"\n            2 => \"status\"\n            3 => \"precio\"\n            4 => \"started_at\"\n            5 => \"ends_at\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        3 => App\\Models\\Suscripcion {#3180\n          #connection: \"mysql\"\n          #table: \"suscripciones\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 19\n            \"negocio_id\" => 19\n            \"plan\" => \"basic\"\n            \"status\" => 1\n            \"started_at\" => \"2025-10-02 10:39:46\"\n            \"ends_at\" => \"2025-11-17 21:39:02\"\n            \"created_at\" => \"2025-10-02 10:39:46\"\n            \"updated_at\" => \"2025-10-02 10:39:46\"\n            \"precio\" => \"0.00\"\n          ]\n          #original: array:9 [\n            \"id\" => 19\n            \"negocio_id\" => 19\n            \"plan\" => \"basic\"\n            \"status\" => 1\n            \"started_at\" => \"2025-10-02 10:39:46\"\n            \"ends_at\" => \"2025-11-17 21:39:02\"\n            \"created_at\" => \"2025-10-02 10:39:46\"\n            \"updated_at\" => \"2025-10-02 10:39:46\"\n            \"precio\" => \"0.00\"\n          ]\n          #changes: []\n          #casts: array:4 [\n            \"status\" => \"App\\Enums\\EstadoSuscripcion\"\n            \"plan\" => \"App\\Enums\\TipoSuscripcion\"\n            \"started_at\" => \"datetime\"\n            \"ends_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"negocio_id\"\n            1 => \"plan\"\n            2 => \"status\"\n            3 => \"precio\"\n            4 => \"started_at\"\n            5 => \"ends_at\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        4 => App\\Models\\Suscripcion {#3181\n          #connection: \"mysql\"\n          #table: \"suscripciones\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 22\n            \"negocio_id\" => 22\n            \"plan\" => \"basic\"\n            \"status\" => 1\n            \"started_at\" => \"2025-10-02 10:39:53\"\n            \"ends_at\" => \"2026-02-25 22:44:58\"\n            \"created_at\" => \"2025-10-02 10:39:53\"\n            \"updated_at\" => \"2025-10-02 10:39:53\"\n            \"precio\" => \"0.00\"\n          ]\n          #original: array:9 [\n            \"id\" => 22\n            \"negocio_id\" => 22\n            \"plan\" => \"basic\"\n            \"status\" => 1\n            \"started_at\" => \"2025-10-02 10:39:53\"\n            \"ends_at\" => \"2026-02-25 22:44:58\"\n            \"created_at\" => \"2025-10-02 10:39:53\"\n            \"updated_at\" => \"2025-10-02 10:39:53\"\n            \"precio\" => \"0.00\"\n          ]\n          #changes: []\n          #casts: array:4 [\n            \"status\" => \"App\\Enums\\EstadoSuscripcion\"\n            \"plan\" => \"App\\Enums\\TipoSuscripcion\"\n            \"started_at\" => \"datetime\"\n            \"ends_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"negocio_id\"\n            1 => \"plan\"\n            2 => \"status\"\n            3 => \"precio\"\n            4 => \"started_at\"\n            5 => \"ends_at\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        5 => App\\Models\\Suscripcion {#3182\n          #connection: \"mysql\"\n          #table: \"suscripciones\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 27\n            \"negocio_id\" => 27\n            \"plan\" => \"basic\"\n            \"status\" => 1\n            \"started_at\" => \"2025-10-02 10:40:08\"\n            \"ends_at\" => \"2026-02-15 10:09:02\"\n            \"created_at\" => \"2025-10-02 10:40:08\"\n            \"updated_at\" => \"2025-10-02 10:40:08\"\n            \"precio\" => \"0.00\"\n          ]\n          #original: array:9 [\n            \"id\" => 27\n            \"negocio_id\" => 27\n            \"plan\" => \"basic\"\n            \"status\" => 1\n            \"started_at\" => \"2025-10-02 10:40:08\"\n            \"ends_at\" => \"2026-02-15 10:09:02\"\n            \"created_at\" => \"2025-10-02 10:40:08\"\n            \"updated_at\" => \"2025-10-02 10:40:08\"\n            \"precio\" => \"0.00\"\n          ]\n          #changes: []\n          #casts: array:4 [\n            \"status\" => \"App\\Enums\\EstadoSuscripcion\"\n            \"plan\" => \"App\\Enums\\TipoSuscripcion\"\n            \"started_at\" => \"datetime\"\n            \"ends_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"negocio_id\"\n            1 => \"plan\"\n            2 => \"status\"\n            3 => \"precio\"\n            4 => \"started_at\"\n            5 => \"ends_at\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        6 => App\\Models\\Suscripcion {#3185\n          #connection: \"mysql\"\n          #table: \"suscripciones\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 29\n            \"negocio_id\" => 29\n            \"plan\" => \"basic\"\n            \"status\" => 1\n            \"started_at\" => \"2025-10-02 10:40:15\"\n            \"ends_at\" => \"2026-01-12 15:01:06\"\n            \"created_at\" => \"2025-10-02 10:40:15\"\n            \"updated_at\" => \"2025-10-02 10:40:15\"\n            \"precio\" => \"0.00\"\n          ]\n          #original: array:9 [\n            \"id\" => 29\n            \"negocio_id\" => 29\n            \"plan\" => \"basic\"\n            \"status\" => 1\n            \"started_at\" => \"2025-10-02 10:40:15\"\n            \"ends_at\" => \"2026-01-12 15:01:06\"\n            \"created_at\" => \"2025-10-02 10:40:15\"\n            \"updated_at\" => \"2025-10-02 10:40:15\"\n            \"precio\" => \"0.00\"\n          ]\n          #changes: []\n          #casts: array:4 [\n            \"status\" => \"App\\Enums\\EstadoSuscripcion\"\n            \"plan\" => \"App\\Enums\\TipoSuscripcion\"\n            \"started_at\" => \"datetime\"\n            \"ends_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"negocio_id\"\n            1 => \"plan\"\n            2 => \"status\"\n            3 => \"precio\"\n            4 => \"started_at\"\n            5 => \"ends_at\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        7 => App\\Models\\Suscripcion {#3186\n          #connection: \"mysql\"\n          #table: \"suscripciones\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 31\n            \"negocio_id\" => 31\n            \"plan\" => \"basic\"\n            \"status\" => 1\n            \"started_at\" => \"2025-10-02 10:40:23\"\n            \"ends_at\" => \"2025-11-09 20:29:20\"\n            \"created_at\" => \"2025-10-02 10:40:23\"\n            \"updated_at\" => \"2025-10-02 10:40:23\"\n            \"precio\" => \"0.00\"\n          ]\n          #original: array:9 [\n            \"id\" => 31\n            \"negocio_id\" => 31\n            \"plan\" => \"basic\"\n            \"status\" => 1\n            \"started_at\" => \"2025-10-02 10:40:23\"\n            \"ends_at\" => \"2025-11-09 20:29:20\"\n            \"created_at\" => \"2025-10-02 10:40:23\"\n            \"updated_at\" => \"2025-10-02 10:40:23\"\n            \"precio\" => \"0.00\"\n          ]\n          #changes: []\n          #casts: array:4 [\n            \"status\" => \"App\\Enums\\EstadoSuscripcion\"\n            \"plan\" => \"App\\Enums\\TipoSuscripcion\"\n            \"started_at\" => \"datetime\"\n            \"ends_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"negocio_id\"\n            1 => \"plan\"\n            2 => \"status\"\n            3 => \"precio\"\n            4 => \"started_at\"\n            5 => \"ends_at\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        8 => App\\Models\\Suscripcion {#3187\n          #connection: \"mysql\"\n          #table: \"suscripciones\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 42\n            \"negocio_id\" => 42\n            \"plan\" => \"basic\"\n            \"status\" => 1\n            \"started_at\" => \"2025-10-02 10:41:08\"\n            \"ends_at\" => \"2025-12-01 06:35:58\"\n            \"created_at\" => \"2025-10-02 10:41:08\"\n            \"updated_at\" => \"2025-10-02 10:41:08\"\n            \"precio\" => \"0.00\"\n          ]\n          #original: array:9 [\n            \"id\" => 42\n            \"negocio_id\" => 42\n            \"plan\" => \"basic\"\n            \"status\" => 1\n            \"started_at\" => \"2025-10-02 10:41:08\"\n            \"ends_at\" => \"2025-12-01 06:35:58\"\n            \"created_at\" => \"2025-10-02 10:41:08\"\n            \"updated_at\" => \"2025-10-02 10:41:08\"\n            \"precio\" => \"0.00\"\n          ]\n          #changes: []\n          #casts: array:4 [\n            \"status\" => \"App\\Enums\\EstadoSuscripcion\"\n            \"plan\" => \"App\\Enums\\TipoSuscripcion\"\n            \"started_at\" => \"datetime\"\n            \"ends_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"negocio_id\"\n            1 => \"plan\"\n            2 => \"status\"\n            3 => \"precio\"\n            4 => \"started_at\"\n            5 => \"ends_at\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        9 => App\\Models\\Suscripcion {#3188\n          #connection: \"mysql\"\n          #table: \"suscripciones\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 44\n            \"negocio_id\" => 44\n            \"plan\" => \"basic\"\n            \"status\" => 1\n            \"started_at\" => \"2025-10-02 10:41:13\"\n            \"ends_at\" => \"2025-11-08 19:01:47\"\n            \"created_at\" => \"2025-10-02 10:41:13\"\n            \"updated_at\" => \"2025-10-02 10:41:13\"\n            \"precio\" => \"0.00\"\n          ]\n          #original: array:9 [\n            \"id\" => 44\n            \"negocio_id\" => 44\n            \"plan\" => \"basic\"\n            \"status\" => 1\n            \"started_at\" => \"2025-10-02 10:41:13\"\n            \"ends_at\" => \"2025-11-08 19:01:47\"\n            \"created_at\" => \"2025-10-02 10:41:13\"\n            \"updated_at\" => \"2025-10-02 10:41:13\"\n            \"precio\" => \"0.00\"\n          ]\n          #changes: []\n          #casts: array:4 [\n            \"status\" => \"App\\Enums\\EstadoSuscripcion\"\n            \"plan\" => \"App\\Enums\\TipoSuscripcion\"\n            \"started_at\" => \"datetime\"\n            \"ends_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:6 [\n            0 => \"negocio_id\"\n            1 => \"plan\"\n            2 => \"status\"\n            3 => \"precio\"\n            4 => \"started_at\"\n            5 => \"ends_at\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"numSuscripcionesSinPagosEnTemporadaActual\" => 10\n    \"importeDeSuscripcionesSinPagosEnTemporadaActual\" => 0.0\n    \"importePendiente\" => \"201.02\"\n    \"importeCompletado\" => \"238.95\"\n    \"importeRechazado\" => \"127.95\"\n    \"importeTotal\" => 439.97\n    \"importeEsperado\" => 439.97\n  ]\n  \"name\" => \"app.filament.widgets.payment-summary\"\n  \"component\" => \"App\\Filament\\Widgets\\PaymentSummary\"\n  \"id\" => \"uDiAhEytKZVmgZ1qw5fa\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[\n  ability => system.access-panel,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-126407930 data-indent-pad=\"  \"><span class=sf-dump-note>system.access-panel </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">system.access-panel</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-126407930\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.80341, "xdebug_link": null}, {"message": "[\n  ability => system.access-panel,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-66639544 data-indent-pad=\"  \"><span class=sf-dump-note>system.access-panel </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">system.access-panel</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-66639544\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.810977, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://mia.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "1.81s", "peak_memory": "52MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-848241306 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-848241306\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1068508204 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4PRWeGkl9dXE3lGBGnX1QHKto4RhiSjRNABI0INv</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"661 characters\">{&quot;data&quot;:{&quot;pagosPendientes&quot;:null,&quot;pagosCompletados&quot;:null,&quot;pagosRechazados&quot;:null,&quot;pagosTotales&quot;:null,&quot;suscripcionesSinPagosEnTemporadaActual&quot;:null,&quot;numSuscripcionesSinPagosEnTemporadaActual&quot;:null,&quot;importeDeSuscripcionesSinPagosEnTemporadaActual&quot;:null,&quot;importePendiente&quot;:null,&quot;importeCompletado&quot;:null,&quot;importeRechazado&quot;:null,&quot;importeTotal&quot;:null,&quot;importeEsperado&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;uDiAhEytKZVmgZ1qw5fa&quot;,&quot;name&quot;:&quot;app.filament.widgets.payment-summary&quot;,&quot;path&quot;:&quot;admin&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;errors&quot;:[],&quot;locale&quot;:&quot;es&quot;},&quot;checksum&quot;:&quot;f8f220601029772a0497f7dbb6dd95e226b307c216eb629731955571838bf3a8&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"248 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbW10seyJzIjoiYXJyIn1dfSwibWVtbyI6eyJpZCI6IllTYVR5WnZwWlJpZGtucjJIOThGIiwibmFtZSI6Il9fbW91bnRQYXJhbXNDb250YWluZXIifSwiY2hlY2tzdW0iOiIxOGU5ZWQ5Mzc5ZWFhYTMyYzUyYzU5ZTJjNjlhYTdiZmMwNThmYTdiMzIyYzE0M2VlMzgzMmMzOTEzY2Y4ZjA5In0=</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1068508204\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1459943497 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mia.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1128</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/141.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Brave&quot;;v=&quot;141&quot;, &quot;Not?A_Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;141&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">es-ES,es;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">https://mia.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">https://mia.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"709 characters\">XSRF-TOKEN=eyJpdiI6Iit2QmZKQk9BMlZ2QlZ3eGV5a0NPSUE9PSIsInZhbHVlIjoiR1RKeGpxb1hNVExUb2pJSkNoaVlRLzVaVU55RkFWczJ3Q2ljRVlhZHdCRU16SkxyZG1HakxqYWJYMUo1N0ZnVnhpWis2TDRGcStTd3hlaVB2bGJrTWJyczZHSHg4d1dhVThRZ21qYzRTaHNpNEZPdXBOM2VTajkyaFp6Umg5YnEiLCJtYWMiOiI3NmY5N2Q4MGYyMGM5ZjY4NjQxODBkODk4ZDAyYTFmNmY5YmZhMjZjZGI2OTYyZjNjM2UyNTE2OGVlM2RjODAzIiwidGFnIjoiIn0%3D; mia_session=eyJpdiI6Ikg5ME96YlBtTHpjdktIZDBtS2lneHc9PSIsInZhbHVlIjoibVF5ZUhyKzAzYi96blUzeUhUSFhmODNFbTlHa0FHcC83UUFBdDdITkNlZm84Qk1waytZT2hxSGtGRkFVUWZHM0JBeThEN0JpRzVJQnlBS0Z2NWhiNzRtT3V6LzFPNFd3SmVFU2pWY0tmcDZMbVpOckRQQlFQWFVMYTZFWVN5ckEiLCJtYWMiOiI4MDg4NjgzM2M4NGRiMmQ2MzVmZjVlNDA0ODRiNjVkMzRiZWJhZGZkNWUzODZiODI2ODM1ZjZmODIxM2M4OWM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1459943497\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1744371869 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4PRWeGkl9dXE3lGBGnX1QHKto4RhiSjRNABI0INv</span>\"\n  \"<span class=sf-dump-key>mia_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cdl2cENjcuWNzf6gG8eyM59qVcY8AHRkO8X6P3aw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1744371869\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1390063780 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 07 Oct 2025 09:54:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1390063780\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2054892920 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4PRWeGkl9dXE3lGBGnX1QHKto4RhiSjRNABI0INv</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">https://mia.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$U1SX/Qb9C93URsHU5yfcFeXmR7kMnbxhP1QLfQ515hyyR3FN9qt7a</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2054892920\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://mia.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}