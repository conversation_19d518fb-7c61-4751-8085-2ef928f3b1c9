#!/bin/bash

#chmod +x ./scripts/build-flutter.sh  # Primera vez para dar permisos de ejecución
#./scripts/build-flutter.sh staging android --release


# Colores para mensajes
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Verificar argumentos
if [ "$#" -lt 2 ]; then
    echo -e "${RED}Uso: $0 <environment> <platform> [--release]${NC}"
    echo -e "  environment: local, staging, production"
    echo -e "  platform: android, ios, windows, macos, linux, web, all"
    echo -e "  --release: opcional, para compilar en modo release"
    exit 1
fi

ENVIRONMENT=$1
PLATFORM=$2
RELEASE=false

# Verificar si se solicitó modo release
if [ "$3" == "--release" ]; then
    RELEASE=true
fi

# Validar environment
if [[ ! "$ENVIRONMENT" =~ ^(local|staging|production)$ ]]; then
    echo -e "${RED}Error: Environment debe ser local, staging o production${NC}"
    exit 1
fi

# Validar platform
if [[ ! "$PLATFORM" =~ ^(android|ios|windows|macos|linux|web|all)$ ]]; then
    echo -e "${RED}Error: Platform debe ser android, ios, windows, macos, linux, web o all${NC}"
    exit 1
fi

# Configuración de directorios
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
ROOT_DIR="$(dirname "$SCRIPT_DIR")"
ENV_SOURCE_FILE="$ROOT_DIR/.env.$ENVIRONMENT"
ENV_TARGET_FILE="$ROOT_DIR/.env"

# Mensaje informativo
echo -e "${CYAN}Generando build para entorno: $ENVIRONMENT en plataforma: $PLATFORM${NC}"

# Copiar archivo de entorno
if [ -f "$ENV_SOURCE_FILE" ]; then
    cp "$ENV_SOURCE_FILE" "$ENV_TARGET_FILE"
    echo -e "${GREEN}Archivo de entorno copiado: $ENV_SOURCE_FILE -> $ENV_TARGET_FILE${NC}"
else
    echo -e "${RED}Error: No se encontró el archivo de entorno $ENV_SOURCE_FILE${NC}"
    exit 1
fi

# Determinar modo de compilación
BUILD_MODE=""
if [ "$RELEASE" = true ]; then
    BUILD_MODE="--release"
fi

# Ejecutar comando de build según la plataforma
cd "$ROOT_DIR" || exit 1

build_android() {
    echo -e "${YELLOW}Generando APK para $ENVIRONMENT...${NC}"
    flutter build apk $BUILD_MODE --dart-define=FLAVOR=$ENVIRONMENT -t lib/main.dart
    
    if [ "$ENVIRONMENT" == "production" ] && [ "$RELEASE" = true ]; then
        echo -e "${YELLOW}Generando App Bundle para producción...${NC}"
        flutter build appbundle $BUILD_MODE --dart-define=FLAVOR=$ENVIRONMENT -t lib/main.dart
    fi
}

build_ios() {
    IOS_FLAGS="$BUILD_MODE"
    if [ "$RELEASE" = true ]; then
        IOS_FLAGS="$BUILD_MODE --no-codesign"
    fi
    echo -e "${YELLOW}Generando build para iOS...${NC}"
    flutter build ios $IOS_FLAGS --dart-define=FLAVOR=$ENVIRONMENT
}

case "$PLATFORM" in
    "android")
        build_android
        ;;
    "ios")
        build_ios
        ;;
    "windows")
        echo -e "${YELLOW}Generando build para Windows...${NC}"
        flutter build windows $BUILD_MODE --dart-define=FLAVOR=$ENVIRONMENT
        ;;
    "macos")
        echo -e "${YELLOW}Generando build para macOS...${NC}"
        flutter build macos $BUILD_MODE --dart-define=FLAVOR=$ENVIRONMENT
        ;;
    "linux")
        echo -e "${YELLOW}Generando build para Linux...${NC}"
        flutter build linux $BUILD_MODE --dart-define=FLAVOR=$ENVIRONMENT
        ;;
    "web")
        echo -e "${YELLOW}Generando build para Web...${NC}"
        flutter build web $BUILD_MODE --dart-define=FLAVOR=$ENVIRONMENT
        ;;
    "all")
        # Detectar sistema operativo
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            build_ios
            echo -e "${YELLOW}Generando build para macOS...${NC}"
            flutter build macos $BUILD_MODE --dart-define=FLAVOR=$ENVIRONMENT
        fi
        
        # Android funciona en todos los sistemas con SDK instalado
        build_android
        
        if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            echo -e "${YELLOW}Generando build para Linux...${NC}"
            flutter build linux $BUILD_MODE --dart-define=FLAVOR=$ENVIRONMENT
        fi
        
        if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
            echo -e "${YELLOW}Generando build para Windows...${NC}"
            flutter build windows $BUILD_MODE --dart-define=FLAVOR=$ENVIRONMENT
        fi
        
        echo -e "${YELLOW}Generando build para Web...${NC}"
        flutter build web $BUILD_MODE --dart-define=FLAVOR=$ENVIRONMENT
        ;;
esac

echo -e "${GREEN}Build completado exitosamente para $ENVIRONMENT en $PLATFORM${NC}"