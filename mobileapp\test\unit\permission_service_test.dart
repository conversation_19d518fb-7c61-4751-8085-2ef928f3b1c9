import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:mia/services/permission_service.dart';

// Create a mock class
class MockPermissionHandler extends Mock implements IPermissionHandler {}

void main() {
  late MockPermissionHandler mockHandler;
  late PermissionService service;

  setUp(() {
    mockHandler = MockPermissionHandler();
    service = PermissionService(handler: mockHandler);
  });

  test('hasLocationPermission returns true when status is granted', () async {
    when(() => mockHandler.status())
        .thenAnswer((_) async => PermissionStatus.granted);

    final result = await service.hasLocationPermission();
    expect(result, isTrue);
    verify(() => mockHandler.status()).called(1);
  });

  test('hasLocationPermission returns false when status is denied', () async {
    when(() => mockHandler.status())
        .thenAnswer((_) async => PermissionStatus.denied);

    final result = await service.hasLocationPermission();
    expect(result, isFalse);
    verify(() => mockHandler.status()).called(1);
  });

  test('requestLocationPermission returns true when request grants', () async {
    when(() => mockHandler.request())
        .thenAnswer((_) async => PermissionStatus.granted);

    final result = await service.requestLocationPermission();
    expect(result, isTrue);
    verify(() => mockHandler.request()).called(1);
  });

  test('requestLocationPermission returns false when request denied', () async {
    when(() => mockHandler.request())
        .thenAnswer((_) async => PermissionStatus.denied);

    final result = await service.requestLocationPermission();
    expect(result, isFalse);
    verify(() => mockHandler.request()).called(1);
  });
}
