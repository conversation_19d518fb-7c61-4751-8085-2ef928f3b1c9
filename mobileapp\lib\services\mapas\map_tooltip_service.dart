// lib/services/mapas/mapa_tooltip_service.dart

import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import 'package:mia/models/negocio.dart';
import 'package:mia/widgets/mapas/negocio_tooltip.dart';

/// Servicio para mostrar tooltips sobre el mapa al pulsar en marcadores
class MapaTooltipService {
  /// Singleton instance
  static final MapaTooltipService _instance = MapaTooltipService._internal();

  /// Constructor factory que devuelve la instancia singleton
  factory MapaTooltipService() => _instance;

  /// Constructor privado para el singleton
  MapaTooltipService._internal();

  /// El overlay entry actual, si hay alguno
  OverlayEntry? _tooltipOverlay;

  /// El negocio que se está mostrando actualmente
  Negocio? _tooltipNegocio;

  /// Ubicación actual del usuario (si está disponible)
  LatLng? _ubicacionUsuario;

  /// Establece la ubicación actual del usuario
  set ubicacionUsuario(LatLng? ubicacion) {
    _ubicacionUsuario = ubicacion;
  }

  /// Muestra un tooltip con la información del negocio en la posición especificada
  void mostrarTooltip(BuildContext context, Negocio negocio, Offset position) {
    // Ocultar tooltip anterior si existe
    ocultarTooltip();

    _tooltipNegocio = negocio;

    // Calcular posición óptima para el tooltip
    // Ajustamos los valores para que el tooltip quede centrado horizontalmente
    // y encima del marcador, con un margen suficiente
    _tooltipOverlay = OverlayEntry(
      builder: (context) {
        return Positioned(
          left: position.dx - 110, // Centrar tooltip (ancho del tooltip / 2)
          top: position.dy -
              160, // Colocar tooltip encima del marcador con margen
          child: NegocioTooltip(
            negocio: negocio,
            ubicacionUsuario: _ubicacionUsuario,
            onClose: ocultarTooltip,
          ),
        );
      },
    );

    Overlay.of(context).insert(_tooltipOverlay!);
  }

  /// Oculta cualquier tooltip que esté actualmente visible
  void ocultarTooltip() {
    if (_tooltipOverlay != null) {
      _tooltipOverlay!.remove();
      _tooltipOverlay = null;
      _tooltipNegocio = null;
    }
  }

  /// Verifica si hay un tooltip visible
  bool get tieneTooltipVisible => _tooltipOverlay != null;

  /// Obtiene el negocio que se está mostrando actualmente
  Negocio? get negocioActual => _tooltipNegocio;
}
