plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file("../auth/android/keystore.properties")
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
} else {
    println "Warning: keystore.properties file not found in ${keystorePropertiesFile.absolutePath}"
}

android {
    namespace = "es.lbcdev.mia"
    // compileSdk = flutter.compileSdkVersion
    compileSdk = 35
    // ndkVersion = flutter.ndkVersion
    ndkVersion = "28.2.13676358"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "es.lbcdev.mia"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        // minSdk = flutter.minSdkVersion
        minSdk = 21
        // targetSdk = flutter.targetSdkVersion
        targetSdk = 35
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    signingConfigs {
        release {
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
        }
    }

    buildTypes {
        release {
            signingConfig = signingConfigs.release
            // Signing with the debug keys for now, so `flutter run --release` works.
            // signingConfig = signingConfigs.debug

            // Configuración para generar símbolos de depuración para código nativo
            ndk {
                // 'FULL' genera un archivo completo de símbolos, mientras que 'SYMBOL_TABLE' produce uno mínimo.
                debugSymbolLevel 'FULL'
            }
        }
    }
}

flutter {
    source = "../.."
}
