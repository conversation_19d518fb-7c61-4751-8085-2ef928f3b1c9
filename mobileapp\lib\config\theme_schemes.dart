import 'package:flutter/material.dart';

/// Renombrado para no colisionar con Flutter ColorScheme
enum AppTheme {
  claro,
  oscuro,

  // sunset,
  // midnight,
  // lavanda,
  // ocean,
  // beach,
  // forest,
  // mountain
}

/// Clase que define todos los colores y gradientes de un tema
class AppColorScheme {
  // Colores base
  final Color primaryColor;
  final Color secondaryColor;
  final Color tertiaryColor;
  final Color accentColor;
  final Color textColor;
  final Color surfaceColor;

  // Gradientes (start/end)
  final Color gradientStart;
  final Color gradientEnd;
  final Color reverseStart;
  final Color reverseEnd;
  final Color accentStart;
  final Color accentEnd;

  // Colores de estado con valores por defecto
  final Color successColor;
  final Color errorColor;
  final Color infoColor;
  final Color warningColor;

  /// Constructor principal: los cuatro colores de estado son opcionales
  /// y usan valores por defecto si no se proporcionan.
  const AppColorScheme({
    required this.primaryColor,
    required this.secondaryColor,
    required this.tertiaryColor,
    required this.accentColor,
    required this.textColor,
    required this.surfaceColor,
    required this.gradientStart,
    required this.gradientEnd,
    required this.reverseStart,
    required this.reverseEnd,
    required this.accentStart,
    required this.accentEnd,
    Color? successColor,
    Color? errorColor,
    Color? infoColor,
    Color? warningColor,
  })  : successColor = successColor ?? const Color(0xFF10C010),
        errorColor = errorColor ?? const Color(0xFFBE1212),
        infoColor = infoColor ?? const Color(0xFF1212C4),
        warningColor = warningColor ?? const Color(0xFFC9C911);

  /// Gradiente principal de fondo
  LinearGradient get mainGradient => LinearGradient(
        colors: [gradientStart, gradientEnd],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      );

  /// Gradiente inverso (por ejemplo para textos sobre fondo)
  LinearGradient get reverseGradient => LinearGradient(
        colors: [reverseStart, reverseEnd],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      );

  /// Gradiente de acento (por ejemplo para botones)
  LinearGradient get accentGradient => LinearGradient(
        colors: [accentStart, accentEnd],
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
      );
}

/// Definición centralizada de todos los temas disponibles
final Map<AppTheme, AppColorScheme> appThemeData = {
  AppTheme.claro: const AppColorScheme(
    primaryColor: Color(0xFFFFA500),
    secondaryColor: Color(0xFFFFFFFF),
    tertiaryColor: Color(0xFFFF6347),
    accentColor: Color(0xFFFF6347),
    textColor: Color(0xFF000000),
    surfaceColor: Color.fromARGB(255, 255, 255, 255),
    gradientStart: Color(0xFFFFA500),
    gradientEnd: Color(0xFFFF6347),
    reverseStart: Color(0xFF8B4513),
    reverseEnd: Color(0xFFFFA500),
    accentStart: Color(0xFFFF8C00),
    accentEnd: Color(0xFFFFA500),
  ),
  AppTheme.oscuro: const AppColorScheme(
    primaryColor: Color(0xFF110427),
    secondaryColor: Color(0xFFEBBC61),
    tertiaryColor: Color(0xFF480FA7),
    accentColor: Color(0xFFEBBC61),
    textColor: Color(0xFFE3E3E8),
    surfaceColor: Color(0xFF110427),
    gradientStart: Color(0xFF110427),
    gradientEnd: Color(0xFF480FA7),
    reverseStart: Color(0xFF000000),
    reverseEnd: Color(0xFF110427),
    accentStart: Color(0xFFEBBC61),
    accentEnd: Color(0xFFFCEDB2),
    // los colores de estado quedarán por defecto
  ),
  // AppTheme.sunset: const AppColorScheme(
  //   primaryColor: Color(0xFFFFA500),
  //   secondaryColor: Color(0xFFFFFFFF),
  //   tertiaryColor: Color(0xFFFF6347),
  //   textColor: Color(0xFF000000),
  //   gradientStart: Color(0xFFFFA500),
  //   gradientEnd: Color(0xFFFF6347),
  //   reverseStart: Color(0xFF8B4513),
  //   reverseEnd: Color(0xFFFFA500),
  //   accentStart: Color(0xFFFF8C00),
  //   accentEnd: Color(0xFFFFA500),
  // ),
  // AppTheme.midnight: const AppColorScheme(
  //   primaryColor: Color(0xFF110427),
  //   secondaryColor: Color(0xFFEBBC61),
  //   tertiaryColor: Color(0xFF480FA7),
  //   textColor: Color(0xFFE3E3E8),
  //   gradientStart: Color(0xFF110427),
  //   gradientEnd: Color(0xFF480FA7),
  //   reverseStart: Color(0xFF000000),
  //   reverseEnd: Color(0xFF110427),
  //   accentStart: Color(0xFFEBBC61),
  //   accentEnd: Color(0xFFFCEDB2),
  // ),
  // AppTheme.lavanda: const AppColorScheme(
  //   primaryColor: Color(0xFFF0F0FA),
  //   secondaryColor: Color(0xFFE6961E),
  //   tertiaryColor: Color(0xFF6432DC),
  //   textColor: Color(0xFF14141E),
  //   gradientStart: Color(0xFFF0F0FA),
  //   gradientEnd: Color(0xFF6432DC),
  //   reverseStart: Color(0xFFFFFFFF),
  //   reverseEnd: Color(0xFFF0F0FA),
  //   accentStart: Color(0xFFE6A500),
  //   accentEnd: Color(0xFFFFE5A2),
  // ),
  // AppTheme.ocean: const AppColorScheme(
  //   primaryColor: Color(0xFF002B5B),
  //   secondaryColor: Color(0xFF00ADEF),
  //   tertiaryColor: Color(0xFF007396),
  //   textColor: Color(0xFFF0F8FF),
  //   gradientStart: Color(0xFF000F1E),
  //   gradientEnd: Color(0xFF002B5B),
  //   reverseStart: Color(0xFF000F1E),
  //   reverseEnd: Color(0xFF00ADEF),
  //   accentStart: Color(0xFF00BCD4),
  //   accentEnd: Color(0xFF009688),
  // ),
  // AppTheme.beach: const AppColorScheme(
  //   primaryColor: Color(0xFFFF9D00),
  //   secondaryColor: Color(0xFF4467FF),
  //   tertiaryColor: Color(0xFFFFA500),
  //   textColor: Color(0xFFFFFFFF),
  //   gradientStart: Color(0xFF8B4513),
  //   gradientEnd: Color(0xFFFFA500),
  //   reverseStart: Color(0xFF8B4513),
  //   reverseEnd: Color(0xFFFF9D00),
  //   accentStart: Color(0xFFFF8C00),
  //   accentEnd: Color(0xFFFFA500),
  // ),
  // AppTheme.forest: const AppColorScheme(
  //   primaryColor: Color(0xFF006400),
  //   secondaryColor: Color(0xFF008000),
  //   tertiaryColor: Color(0xFF009600),
  //   textColor: Color(0xFFFFFFFF),
  //   gradientStart: Color(0xFF003200),
  //   gradientEnd: Color(0xFF006400),
  //   reverseStart: Color(0xFF003200),
  //   reverseEnd: Color(0xFF009600),
  //   accentStart: Color(0xFF008000),
  //   accentEnd: Color(0xFF009600),
  // ),
  // AppTheme.mountain: const AppColorScheme(
  //   primaryColor: Color(0xFF8B4513),
  //   secondaryColor: Color(0xFFA52A2A),
  //   tertiaryColor: Color(0xFF696969),
  //   textColor: Color(0xFFFFFFFF),
  //   gradientStart: Color(0xFF000000),
  //   gradientEnd: Color(0xFF8B4513),
  //   reverseStart: Color(0xFF000000),
  //   reverseEnd: Color(0xFFA52A2A),
  //   accentStart: Color(0xFFA52A2A),
  //   accentEnd: Color(0xFF696969),
  // ),
};
