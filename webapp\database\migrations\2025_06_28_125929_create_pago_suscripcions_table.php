<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pago_suscripcions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('suscripcion_id');
            $table->foreign('suscripcion_id')
                ->references('id')
                ->on('suscripciones');
            $table->string('metodo_pago');
            $table->decimal('importe', 10, 2);
            $table->date('fecha_pago');
            $table->string('transaccion_id')->nullable();
            $table->string('estado')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pago_suscripcions');
    }
};
