# Backend - Laravel

## Estructura del Proyecto

```
webapp/
├── app/
│   ├── Console/
│   │   └── Commands/          # Comandos Artisan personalizados
│   ├── Enums/                 # Enumeraciones (EstadoPago, MetodoPago, etc.)
│   ├── Filament/              # Panel de administración
│   │   ├── Pages/             # Páginas personalizadas
│   │   ├── Resources/         # Recursos CRUD
│   │   └── Widgets/           # Widgets del dashboard
│   ├── Http/
│   │   ├── Controllers/
│   │   │   └── Api/           # Controladores de API
│   │   ├── Middleware/        # Middleware personalizado
│   │   └── Requests/          # Form Requests (validación)
│   ├── Models/                # Modelos Eloquent
│   ├── Observers/             # Observers de modelos
│   ├── Policies/              # Políticas de autorización
│   ├── Providers/             # Service Providers
│   └── Services/              # Servicios de negocio
├── config/                    # Archivos de configuración
├── database/
│   ├── factories/             # Factories para testing
│   ├── migrations/            # Migraciones de BD
│   └── seeders/               # Seeders de datos
├── routes/
│   ├── api.php                # Rutas de API
│   ├── web.php                # Rutas web
│   └── console.php            # Comandos de consola
├── tests/
│   ├── Feature/               # Tests de integración
│   └── Unit/                  # Tests unitarios
└── storage/                   # Almacenamiento de archivos
```

## Modelos y Relaciones

### Diagrama de Relaciones

```
User (Usuario)
  ├── hasMany → Negocio
  └── belongsToMany → Role (Spatie)

Localidad
  ├── hasMany → Zona
  └── hasManyThrough → Negocio

Zona
  ├── belongsTo → Localidad
  └── hasMany → Negocio

Negocio
  ├── belongsTo → Zona
  ├── belongsTo → User
  ├── belongsToMany → Categoria
  ├── hasOne → Suscripcion
  ├── hasMany → Evento
  └── morphMany → Media (Spatie)

Categoria
  ├── belongsTo → Categoria (parent)
  ├── hasMany → Categoria (subcategorias)
  ├── belongsToMany → Negocio
  └── morphMany → Media (Spatie)

Suscripcion
  ├── belongsTo → Negocio
  └── hasMany → PagoSuscripcion

PagoSuscripcion
  ├── belongsTo → Suscripcion
  └── hasOneThrough → Negocio

Evento
  ├── belongsTo → Negocio
  ├── hasOneThrough → User
  └── morphMany → Media (Spatie)
```

### Modelos Principales

Ver documentación detallada:
- [Modelos](./modelos.md)
- [Enums](./enums.md)
- [Relaciones](./relaciones.md)

## API REST

### Estructura de Rutas

```php
// routes/api.php

// Autenticación
POST   /api/login
POST   /api/logout (auth)

// API v1
/api/v1/
  ├── negocios/
  │   ├── GET    /              # Listar todos
  │   ├── GET    /{id}          # Ver uno
  │   ├── POST   /       (auth) # Crear
  │   ├── PUT    /{id}   (auth) # Actualizar
  │   └── DELETE /{id}   (auth) # Eliminar
  │
  ├── categorias/
  │   ├── GET    /              # Listar todas
  │   └── GET    /{id}          # Ver una
  │
  ├── zonas/
  │   ├── GET    /              # Listar todas
  │   └── GET    /{id}          # Ver una
  │
  ├── localidades/
  │   ├── GET    /              # Listar todas
  │   └── GET    /{id}          # Ver una
  │
  └── eventos/
      ├── GET    /              # Listar todos
      ├── GET    /{id}          # Ver uno
      ├── POST   /       (auth) # Crear
      ├── PUT    /{id}   (auth) # Actualizar
      └── DELETE /{id}   (auth) # Eliminar
```

### Controladores

Todos los controladores de API siguen el patrón:

```php
class NegocioController extends Controller
{
    public function index()     // GET /api/v1/negocios
    public function show($id)   // GET /api/v1/negocios/{id}
    public function store()     // POST /api/v1/negocios (auth)
    public function update($id) // PUT /api/v1/negocios/{id} (auth)
    public function destroy($id)// DELETE /api/v1/negocios/{id} (auth)
}
```

Ver más: [API Documentation](../05-api/README.md)

## Sistema de Permisos

### Roles Definidos

```php
'admin'    // Acceso total al sistema
'owner'    // Propietario de negocios
'cliente'  // Cliente con acceso limitado
'usuario'  // Usuario básico
```

### Permisos por Recurso

Cada modelo tiene permisos granulares:

```
{modelo}.list          # Ver listado
{modelo}.read          # Ver detalle
{modelo}.create        # Crear
{modelo}.update        # Actualizar
{modelo}.delete        # Eliminar
{modelo}.force-delete  # Eliminar permanentemente
{modelo}.restore       # Restaurar eliminado
```

Ejemplo para Negocio:
- `negocio.list`
- `negocio.read`
- `negocio.create`
- `negocio.update`
- `negocio.delete`

### Permisos de Sistema

```
system.access-panel      # Acceder al panel Filament
system.settings          # Configuración del sistema
system.admin             # Funciones de administrador
system.users             # Gestión de usuarios
system.roles             # Gestión de roles
system.permissions       # Gestión de permisos
system.admin-dashboard   # Ver dashboard completo
```

### Políticas (Policies)

Las políticas definen la lógica de autorización:

```php
// app/Policies/NegocioPolicy.php

class NegocioPolicy
{
    public function viewAny(User $user): bool
    {
        return $user->can('negocio.list');
    }

    public function view(User $user, Negocio $negocio): bool
    {
        return $user->can('negocio.read') 
            || $user->id === $negocio->user_id;
    }

    public function create(User $user): bool
    {
        return $user->can('negocio.create');
    }

    public function update(User $user, Negocio $negocio): bool
    {
        return $user->can('negocio.update') 
            || $user->id === $negocio->user_id;
    }

    public function delete(User $user, Negocio $negocio): bool
    {
        return $user->can('negocio.delete');
    }
}
```

Ver más: [Sistema de Permisos](./permisos.md)

## Panel de Administración (Filament)

### Estructura

```
app/Filament/
├── Pages/
│   └── Dashboard.php          # Dashboard personalizado
├── Resources/
│   ├── NegocioResource.php    # CRUD de Negocios
│   ├── CategoriaResource.php  # CRUD de Categorías
│   ├── ZonaResource.php       # CRUD de Zonas
│   ├── UserResource.php       # CRUD de Usuarios
│   ├── RoleResource.php       # CRUD de Roles
│   └── ...
└── Widgets/
    ├── GlobalSummary.php      # Resumen global
    ├── PaymentSummary.php     # Resumen de pagos
    ├── NegociosMap.php        # Mapa de negocios
    └── ...
```

### Recursos Filament

Cada recurso define:
- **Form**: Formulario de creación/edición
- **Table**: Tabla de listado con filtros
- **Actions**: Acciones disponibles
- **Pages**: Páginas personalizadas

Ejemplo simplificado:

```php
class NegocioResource extends Resource
{
    protected static ?string $model = Negocio::class;
    protected static ?string $navigationIcon = 'heroicon-s-calculator';
    protected static ?string $navigationGroup = 'Gestión';

    public static function form(Form $form): Form
    {
        return $form->schema([
            TextInput::make('nombre')->required(),
            Textarea::make('descripcion'),
            Select::make('zona_id')
                ->relationship('zona', 'nombre')
                ->required(),
            // ... más campos
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('nombre'),
                TextColumn::make('zona.nombre'),
                // ... más columnas
            ])
            ->filters([
                SelectFilter::make('zona'),
                // ... más filtros
            ]);
    }
}
```

Ver más: [Panel Filament](./filament.md)

## Servicios

### CacheService

Gestiona la invalidación de caché:

```php
class CacheService
{
    public function invalidateCache(string $modelClass, string $cacheKey): void
    {
        Cache::forget($cacheKey);
        // Lógica adicional
    }
}
```

### DataGeneratorService

Genera datos de prueba y configuración inicial.

## Observers

Los observers escuchan eventos de modelos:

```php
class NegocioObserver
{
    public function created(Negocio $negocio): void
    {
        // Invalidar caché cuando se crea un negocio
        app(CacheService::class)->invalidateCache(
            Negocio::class, 
            'negocios_all'
        );
    }

    public function updated(Negocio $negocio): void
    {
        // Invalidar caché cuando se actualiza
    }

    public function deleted(Negocio $negocio): void
    {
        // Invalidar caché cuando se elimina
    }
}
```

## Validación

### Form Requests

Cada operación de escritura tiene su Form Request:

```php
class StoreNegocioRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->can('negocio.create');
    }

    public function rules(): array
    {
        return [
            'nombre' => 'required|string|max:100',
            'descripcion' => 'nullable|string',
            'direccion' => 'required|string|max:255',
            'ubicacion' => 'required|array',
            'ubicacion.latitud' => 'required|numeric|between:-90,90',
            'ubicacion.longitud' => 'required|numeric|between:-180,180',
            'zona_id' => 'required|exists:zonas,id',
            // ... más reglas
        ];
    }
}
```

## Próximos Pasos

- [Modelos Detallados](./modelos.md)
- [Enums](./enums.md)
- [Sistema de Permisos](./permisos.md)
- [Panel Filament](./filament.md)

