// lib/providers/settings_provider.dart

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:mia/config/distance_options.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:mia/config/map_schemes.dart';

class SettingsProvider with ChangeNotifier {
  static const String _mapKey = 'mapPlatform';
  static const String _distanceKey = 'distanceKm';

  /// Getter estático para quien necesite la clave
  static String get mapKey => _mapKey;
  static String get distanceKey => _distanceKey;

  MapPlatform _mapPlatform =
      Platform.isIOS ? MapPlatform.apple : MapPlatform.google;
  DistanceOption _distanceOption = DistanceOption.twoPointFiveKm;

  MapPlatform get mapPlatform => _mapPlatform;
  double get distanceKm => _distanceOption.km;
  DistanceOption get distanceOption => _distanceOption;

  SettingsProvider() {
    _load();
  }

  Future<void> _load() async {
    final prefs = await SharedPreferences.getInstance();

    // Carga mapa
    final rawMap = prefs.getString(_mapKey);
    _mapPlatform = rawMap != null
        ? MapPlatform.values.firstWhere(
            (e) => e.toString() == rawMap,
            orElse: _defaultPlatform,
          )
        : _defaultPlatform();

    // Leemos el double y buscamos la opción que lo representa
    final stored = prefs.getDouble(_distanceKey);
    _distanceOption = DistanceOption.values.firstWhere(
      (opt) => opt.km == stored,
      orElse: () => DistanceOption.twoPointFiveKm,
    );

    notifyListeners();
  }

  Future<void> setMapPlatform(MapPlatform p) async {
    _mapPlatform = p;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_mapKey, p.toString());
    notifyListeners();
  }

  Future<void> setDistanceKm(double km) async {
    _distanceOption = DistanceOption.values.firstWhere((opt) => opt.km == km);

    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble(_distanceKey, km);
    notifyListeners();
  }

  /// Por defecto según la plataforma nativa
  MapPlatform _defaultPlatform() {
    if (Platform.isIOS) return MapPlatform.apple;
    return MapPlatform.google;
  }
}
