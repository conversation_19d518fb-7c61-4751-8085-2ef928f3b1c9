<?php

namespace Database\Seeders;

use App\Models\Categoria;
use Illuminate\Database\Seeder;
use App\Services\DataGeneratorService;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class CategoriaSeeder extends Seeder
{
    public function run(): void
    {
        $categorias = DataGeneratorService::getCategorias();

        foreach ($categorias as $categoria) {
            $parent = $categoria['parent']
                ? Categoria::where('nombre', $categoria['parent'])->first()
                : null;

            $cat = Categoria::create([
                'nombre' => $categoria['nombre'],
                'descripcion' => fake()->sentence(),
                'parent_id' => $parent?->id
            ]);

            if ($categoria['icono'] ?? false) {
                $cat->addMedia(Storage::disk('default-images')->path('iconos_cat/' . $categoria['icono']))
                    ->preservingOriginal()
                    ->toMediaCollection('iconos');
            }
        }
    }
}
