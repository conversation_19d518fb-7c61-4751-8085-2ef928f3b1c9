import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mia/config/colors.dart';
import 'package:mia/config/styles.dart';
import 'package:mia/models/media.dart';
import 'package:mia/models/negocio.dart';
import 'package:mia/services/core_service.dart';
import 'package:mia/widgets/botones/fav_button.dart';
import 'package:mia/widgets/imagenes/horizontal_image_slider.dart';

class CardNegocio extends StatelessWidget {
  final Negocio negocio;

  const CardNegocio({super.key, required this.negocio});

  @override
  Widget build(BuildContext context) {
    String zona = negocio.zona?.nombre ?? '';

    return InkWell(
      onTap: () {
        Navigator.pushNamed(
          context,
          '/negocio',
          arguments: negocio, // Pasar el objeto Negocio
        );
      },
      child: Card(
        shape:
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(10.0)),
        child: Stack(
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 4.0, vertical: 8.0),
              decoration: BoxDecoration(
                  // gradient: AppColors.current.reverseGradient,
                  color: AppColors.current.surfaceColor,
                  borderRadius: BorderRadius.circular(10.0)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildImagen(negocio),
                  // Título y subtítulo
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 4.0, vertical: 8.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(negocio.nombre,
                                      style: AppStyles.getStyle(context, 'h6',
                                          fontWeight: 'bold')),
                                  Padding(
                                    padding: EdgeInsets.symmetric(
                                        vertical: 4.0, horizontal: 0.0),
                                    child: CoreService.buildTagList(
                                        backgroundColorDefault:
                                            Colors.transparent,
                                        textColorDefault:
                                            AppColors.current.textColor,
                                        tags: negocio.categorias
                                            ?.map((cat) => cat.nombre)
                                            .toList(),
                                        borderWidth: 1.0,
                                        borderColorDefault:
                                            AppColors.current.accentColor),
                                  ),
                                ],
                              ),
                            ),
                            FavButton(
                              negocio: negocio,
                              size: 32,
                            ),
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  _buildWidgetRowDireccion(context, zona),
                                  SizedBox(height: 10),
                                  negocio.horario == null
                                      ? Container()
                                      : _buildHeaderHorario(
                                          negocio.isOpenNow(),
                                          negocio.getAllHorariosHoy() ?? '',
                                          context,
                                          mostarLink: false),
                                  SizedBox(height: 10),
                                ],
                              ),
                            ),
                            Icon(Icons.arrow_circle_right_rounded,
                                size: 64, color: AppColors.current.accentColor),
                          ],
                        )
                      ],
                    ),
                  ),
                  // Tags en lugar del botón
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Row _buildWidgetRowDireccion(BuildContext context, String zona) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Icon(Icons.location_on, size: 16, color: AppColors.current.accentColor),
        SizedBox(width: 4),
        Expanded(
          child: Text(
            "${negocio.direccion} ($zona)",
            style: AppStyles.getStyle(context, 'base'),
          ),
        ),
      ],
    );
  }

  Widget _buildImagen(Negocio negocio) {
    Widget imagen;

    if (negocio.imagenes != null && negocio.imagenes!.isNotEmpty) {
      // imagen = Carousel(
      //   imagenes: negocio.imagenes ?? [],
      //   // tags: negocio.categorias?.map((cat) => cat.nombre).toList(),
      //   tags: [],
      // );
      imagen = HorizontalImageSlider(mediaList: negocio.imagenes ?? []);
    } else {
      imagen = Media.getDefaultImage(
        width: double.infinity,
        height: 200,
      );
    }

    return ClipRRect(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10.0),
          topRight: Radius.circular(10.0),
        ),
        child: imagen);
  }

  Widget _buildHeaderHorario(
      bool isOpen, String horarioHoy, BuildContext context,
      {bool mostarLink = true}) {
    // Si el horario de hoy no está vacío, obtenemos el día actual y lo agregamos
    String horarioConDia = horarioHoy;
    if (horarioHoy.isNotEmpty) {
      final List<String> weekdays = [
        'lunes',
        'martes',
        'miércoles',
        'jueves',
        'viernes',
        'sábado',
        'domingo'
      ];
      final now = DateTime.now();
      final String currentDay = weekdays[now.weekday - 1];
      // Anteponer el día actual (capitalizado) seguido de " - " y los tramos horarios
      horarioConDia = '${_capitalize(currentDay)} - $horarioHoy';
    }

    // Obtener la hora actual formateada solo si estamos en debug
    String debugTime = '';
    if (kDebugMode) {
      final now = DateTime.now();
      // Formatear la hora actual en HH:mm:ss
      debugTime =
          ' (${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')})';
    }

    return Wrap(
      crossAxisAlignment: WrapCrossAlignment.center,
      alignment: WrapAlignment.start,
      spacing: 4,
      runSpacing: 4,
      children: [
        Icon(Icons.access_time, size: 16, color: AppColors.current.accentColor),
        isOpen
            ? Text(
                'Abierto',
                style: AppStyles.getStyle(context, 'base',
                    fontWeight: 'bold', color: AppColors.current.successColor),
              )
            : Text(
                'Cerrado',
                style: AppStyles.getStyle(context, 'base',
                    fontWeight: 'bold', color: AppColors.current.errorColor),
              ),
        // Mostrar el tiempo actual entre paréntesis en modo debug
        if (kDebugMode)
          Text(
            debugTime,
            style: AppStyles.getStyle(context, 'base',
                color: AppColors.current.infoColor),
          ),
        Text(
          horarioConDia,
          style: AppStyles.getStyle(context, 'base'),
        ),
      ],
    );
  }

  String _capitalize(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1);
  }
}
