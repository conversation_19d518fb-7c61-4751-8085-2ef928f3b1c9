import 'package:flutter/foundation.dart';

class Localidad {
  final int id;
  final String nombre;
  final Map<String, double>? ubicacion;
  // final Map<String, double>? limites;

  Localidad({
    required this.id,
    required this.nombre,
    this.ubicacion,
    // this.limites,
  });

  factory Localidad.fromJson(Map<String, dynamic> json) {
    try {
      // Validar campos requeridos y su tipo
      if (json['id'] == null || json['id'] is! int) {
        throw Exception('El campo id en Localidad debe ser un entero');
      }
      if (json['nombre'] == null || json['nombre'] is! String) {
        throw Exception(
            'El campo nombre en Localidad es requerido y debe ser una cadena');
      }

      return Localidad(
        id: json['id'],
        nombre: json['nombre'],
        ubicacion: json['ubicacion']?.cast<String, double>(),
        // limites: json['limites']?.cast<String, double>(),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error al procesar el JSON para Localidad: $json');
        print('Error: $e');
      }
      // Asegurar que siempre lanzamos Exception (no TypeError u otros)
      if (e is! Exception) {
        throw Exception('Error al procesar JSON: $e');
      }
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nombre': nombre,
      'ubicacion': ubicacion,
      // 'limites': limites,
    };
  }

  @override
  String toString() {
    return 'Localidad(id: $id, nombre: $nombre, ubicacion: $ubicacion)';
  }
}
