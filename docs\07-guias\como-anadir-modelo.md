# Guía: Cómo Añadir un Nuevo Modelo

Esta guía te llevará paso a paso para añadir un nuevo modelo al proyecto, tanto en backend como frontend.

## Ejemplo: Añadir modelo "Promocion"

Vamos a crear un modelo de ejemplo llamado "Promocion" que representa ofertas especiales de negocios.

---

## Backend (Laravel)

### 1. <PERSON>rear <PERSON>

```bash
php artisan make:migration create_promociones_table
```

**Archivo**: `database/migrations/YYYY_MM_DD_HHMMSS_create_promociones_table.php`

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('promociones', function (Blueprint $table) {
            $table->id();
            $table->string('titulo');
            $table->text('descripcion')->nullable();
            $table->decimal('descuento', 5, 2)->nullable(); // Ej: 15.50%
            $table->date('fecha_inicio');
            $table->date('fecha_fin');
            $table->boolean('activa')->default(true);
            
            // Relaciones
            $table->foreignId('negocio_id')
                ->constrained('negocios')
                ->onDelete('cascade');
            
            $table->timestamps();
            
            // Índices
            $table->index('negocio_id');
            $table->index('fecha_inicio');
            $table->index('fecha_fin');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('promociones');
    }
};
```

```bash
php artisan migrate
```

### 2. Crear Modelo

```bash
php artisan make:model Promocion
```

**Archivo**: `app/Models/Promocion.php`

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Promocion extends Model
{
    use HasFactory;

    protected $table = 'promociones';

    protected $fillable = [
        'titulo',
        'descripcion',
        'descuento',
        'fecha_inicio',
        'fecha_fin',
        'activa',
        'negocio_id',
    ];

    protected $casts = [
        'fecha_inicio' => 'date',
        'fecha_fin' => 'date',
        'activa' => 'boolean',
        'descuento' => 'decimal:2',
    ];

    // Relaciones
    public function negocio(): BelongsTo
    {
        return $this->belongsTo(Negocio::class);
    }

    // Scopes
    public function scopeActivas($query)
    {
        return $query->where('activa', true)
            ->where('fecha_inicio', '<=', now())
            ->where('fecha_fin', '>=', now());
    }

    public function scopeVigentes($query)
    {
        return $query->where('fecha_fin', '>=', now());
    }
}
```

### 3. Actualizar Modelo Relacionado (Negocio)

**Archivo**: `app/Models/Negocio.php`

```php
// Añadir relación
public function promociones(): HasMany
{
    return $this->hasMany(Promocion::class);
}
```

### 4. Crear Factory

```bash
php artisan make:factory PromocionFactory
```

**Archivo**: `database/factories/PromocionFactory.php`

```php
<?php

namespace Database\Factories;

use App\Models\Negocio;
use Illuminate\Database\Eloquent\Factories\Factory;

class PromocionFactory extends Factory
{
    public function definition(): array
    {
        $fechaInicio = $this->faker->dateTimeBetween('now', '+1 month');
        $fechaFin = $this->faker->dateTimeBetween($fechaInicio, '+3 months');

        return [
            'titulo' => $this->faker->sentence(3),
            'descripcion' => $this->faker->paragraph(),
            'descuento' => $this->faker->randomFloat(2, 5, 50),
            'fecha_inicio' => $fechaInicio,
            'fecha_fin' => $fechaFin,
            'activa' => $this->faker->boolean(80),
            'negocio_id' => Negocio::factory(),
        ];
    }

    public function activa(): static
    {
        return $this->state(fn (array $attributes) => [
            'activa' => true,
            'fecha_inicio' => now()->subDays(1),
            'fecha_fin' => now()->addDays(30),
        ]);
    }
}
```

### 5. Crear Policy

```bash
php artisan make:policy PromocionPolicy --model=Promocion
```

**Archivo**: `app/Policies/PromocionPolicy.php`

```php
<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Promocion;

class PromocionPolicy
{
    public function viewAny(User $user): bool
    {
        return $user->can('promocion.list');
    }

    public function view(User $user, Promocion $promocion): bool
    {
        return $user->can('promocion.read');
    }

    public function create(User $user): bool
    {
        return $user->can('promocion.create');
    }

    public function update(User $user, Promocion $promocion): bool
    {
        return $user->can('promocion.update') 
            || $user->id === $promocion->negocio->user_id;
    }

    public function delete(User $user, Promocion $promocion): bool
    {
        return $user->can('promocion.delete');
    }
}
```

### 6. Registrar Policy

**Archivo**: `app/Providers/AppServiceProvider.php`

```php
use App\Models\Promocion;
use App\Policies\PromocionPolicy;

public function boot(): void
{
    Gate::policy(Promocion::class, PromocionPolicy::class);
}
```

### 7. Crear Form Requests

```bash
php artisan make:request StorePromocionRequest
php artisan make:request UpdatePromocionRequest
```

**Archivo**: `app/Http/Requests/StorePromocionRequest.php`

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StorePromocionRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->can('create', \App\Models\Promocion::class);
    }

    public function rules(): array
    {
        return [
            'titulo' => 'required|string|max:255',
            'descripcion' => 'nullable|string',
            'descuento' => 'nullable|numeric|min:0|max:100',
            'fecha_inicio' => 'required|date|after_or_equal:today',
            'fecha_fin' => 'required|date|after:fecha_inicio',
            'activa' => 'boolean',
            'negocio_id' => 'required|exists:negocios,id',
        ];
    }
}
```

### 8. Crear Controlador API

```bash
php artisan make:controller Api/PromocionController --api
```

**Archivo**: `app/Http/Controllers/Api/PromocionController.php`

```php
<?php

namespace App\Http\Controllers\Api;

use App\Models\Promocion;
use App\Http\Controllers\Controller;
use App\Http\Requests\StorePromocionRequest;
use App\Http\Requests\UpdatePromocionRequest;

class PromocionController extends Controller
{
    public function index()
    {
        $promociones = Promocion::with(['negocio'])
            ->activas()
            ->get();

        return response()->json($promociones);
    }

    public function show(Promocion $promocion)
    {
        $promocion->load('negocio');
        return response()->json($promocion);
    }

    public function store(StorePromocionRequest $request)
    {
        $promocion = Promocion::create($request->validated());
        return response()->json($promocion, 201);
    }

    public function update(UpdatePromocionRequest $request, Promocion $promocion)
    {
        $promocion->update($request->validated());
        return response()->json($promocion);
    }

    public function destroy(Promocion $promocion)
    {
        $this->authorize('delete', $promocion);
        $promocion->delete();
        return response()->json(null, 204);
    }
}
```

### 9. Añadir Rutas API

**Archivo**: `routes/api.php`

```php
Route::prefix('v1')->group(function () {
    // ... otras rutas
    
    Route::prefix('promociones')->group(function () {
        Route::get('/', [PromocionController::class, 'index']);
        Route::get('/{promocion}', [PromocionController::class, 'show']);
        
        Route::middleware('auth:sanctum')->group(function () {
            Route::post('/', [PromocionController::class, 'store']);
            Route::put('/{promocion}', [PromocionController::class, 'update']);
            Route::delete('/{promocion}', [PromocionController::class, 'destroy']);
        });
    });
});
```

### 10. Crear Recurso Filament

```bash
php artisan make:filament-resource Promocion --generate
```

**Archivo**: `app/Filament/Resources/PromocionResource.php`

```php
<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\Promocion;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use App\Filament\Resources\PromocionResource\Pages;

class PromocionResource extends Resource
{
    protected static ?string $model = Promocion::class;
    protected static ?string $navigationIcon = 'heroicon-o-tag';
    protected static ?string $navigationGroup = 'Gestión';

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('titulo')
                ->required()
                ->maxLength(255),
            Forms\Components\Textarea::make('descripcion'),
            Forms\Components\TextInput::make('descuento')
                ->numeric()
                ->suffix('%')
                ->minValue(0)
                ->maxValue(100),
            Forms\Components\DatePicker::make('fecha_inicio')
                ->required(),
            Forms\Components\DatePicker::make('fecha_fin')
                ->required()
                ->after('fecha_inicio'),
            Forms\Components\Toggle::make('activa')
                ->default(true),
            Forms\Components\Select::make('negocio_id')
                ->relationship('negocio', 'nombre')
                ->searchable()
                ->required(),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('titulo'),
                Tables\Columns\TextColumn::make('negocio.nombre'),
                Tables\Columns\TextColumn::make('descuento')
                    ->suffix('%'),
                Tables\Columns\IconColumn::make('activa')
                    ->boolean(),
                Tables\Columns\TextColumn::make('fecha_inicio')
                    ->date(),
                Tables\Columns\TextColumn::make('fecha_fin')
                    ->date(),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('activa'),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPromocions::route('/'),
            'create' => Pages\CreatePromocion::route('/create'),
            'edit' => Pages\EditPromocion::route('/{record}/edit'),
        ];
    }
}
```

### 11. Crear Tests

Ver: [Plantilla API Test](../06-testing/plantilla-api-test.md)

```bash
php artisan test --filter PromocionApiTest
```

---

## Frontend (Flutter)

### 1. Crear Modelo

**Archivo**: `lib/models/promocion.dart`

```dart
import 'package:flutter/foundation.dart';

class Promocion {
  final int id;
  final String titulo;
  final String? descripcion;
  final double? descuento;
  final DateTime fechaInicio;
  final DateTime fechaFin;
  final bool activa;
  final int negocioId;

  Promocion({
    required this.id,
    required this.titulo,
    this.descripcion,
    this.descuento,
    required this.fechaInicio,
    required this.fechaFin,
    required this.activa,
    required this.negocioId,
  });

  factory Promocion.fromJson(Map<String, dynamic> json) {
    try {
      return Promocion(
        id: json['id'] as int,
        titulo: json['titulo'] as String,
        descripcion: json['descripcion'] as String?,
        descuento: json['descuento'] != null 
            ? double.parse(json['descuento'].toString()) 
            : null,
        fechaInicio: DateTime.parse(json['fecha_inicio']),
        fechaFin: DateTime.parse(json['fecha_fin']),
        activa: json['activa'] as bool,
        negocioId: json['negocio_id'] as int,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error al procesar Promocion: $json');
        print('Error: $e');
      }
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'titulo': titulo,
      'descripcion': descripcion,
      'descuento': descuento,
      'fecha_inicio': fechaInicio.toIso8601String(),
      'fecha_fin': fechaFin.toIso8601String(),
      'activa': activa,
      'negocio_id': negocioId,
    };
  }

  bool get esVigente {
    final now = DateTime.now();
    return activa && 
           fechaInicio.isBefore(now) && 
           fechaFin.isAfter(now);
  }
}
```

### 2. Añadir al ApiService

**Archivo**: `lib/services/api_service.dart`

```dart
Future<List<Promocion>> getPromociones() {
  return fetchData<Promocion>(
    endpoint: 'promociones',
    fromJson: Promocion.fromJson,
  );
}
```

### 3. Crear Tests

Ver: [Plantilla Model Test](../06-testing/plantilla-model-test.md)

### 4. Crear Fixtures

**Archivo**: `test/fixture/json/promocion/valid_promocion_completo.json`

```json
{
  "id": 1,
  "titulo": "Promoción de Verano",
  "descripcion": "Descuento especial",
  "descuento": 15.50,
  "fecha_inicio": "2025-06-01",
  "fecha_fin": "2025-08-31",
  "activa": true,
  "negocio_id": 1
}
```

---

## Checklist Final

### Backend
- [ ] Migración creada y ejecutada
- [ ] Modelo creado con relaciones
- [ ] Factory creado
- [ ] Policy creada y registrada
- [ ] Form Requests creados
- [ ] Controlador API creado
- [ ] Rutas API añadidas
- [ ] Recurso Filament creado
- [ ] Tests creados y pasando
- [ ] Permisos añadidos al seeder

### Frontend
- [ ] Modelo Dart creado
- [ ] Método en ApiService añadido
- [ ] Tests creados y pasando
- [ ] Fixtures JSON creados

### Documentación
- [ ] Modelo documentado en docs/02-backend/modelos.md
- [ ] API documentada en docs/05-api/README.md

