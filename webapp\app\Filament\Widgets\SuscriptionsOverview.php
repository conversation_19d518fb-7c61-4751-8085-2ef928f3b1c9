<?php

namespace App\Filament\Widgets;

use App\Models\Negocio;
use App\Enums\EstadoSuscripcion;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;

class SuscriptionsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        $negociosActivos = Negocio::whereHas('suscripcion', function ($query) {
            $query->where('status', EstadoSuscripcion::ACTIVE->value);
        })->count();

        $negociosInactivos = Negocio::whereHas('suscripcion', function ($query) {
            $query->where('status', EstadoSuscripcion::INACTIVE->value);
        })->count();

        $negociosTotales = $negociosActivos + $negociosInactivos;

        $descripcion = "🟢 Activos: {$negociosActivos}" . PHP_EOL .
            "🔴 Inactivos: {$negociosInactivos}" . PHP_EOL .
            "🔵 Totales: {$negociosTotales}";

        return [
            Stat::make('Resumen de Suscripciones', $negociosActivos . '/' . $negociosTotales)
                ->description($descripcion)
                ->color('primary'),
        ];
    }
}
