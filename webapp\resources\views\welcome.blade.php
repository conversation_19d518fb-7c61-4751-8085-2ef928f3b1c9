<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>Matalascañas G<PERSON></title>

        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
        <link rel="manifest" href="/site.webmanifest">
        
        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400,600,700,800&display=swap" rel="stylesheet">

        <!-- Styles / Scripts -->
        @if (file_exists(public_path('build/manifest.json')) || file_exists(public_path('hot')))
            @vite(['resources/css/app.css', 'resources/js/app.js'])
        @else
            
        @endif
        
        <style>
            body {
                background: linear-gradient(45deg, #FFA500, #FF6347);
                background-attachment: fixed;
                min-height: 100vh;
                font-family: 'Nunito', sans-serif;
            }
            
            .main-heading {
                font-size: 8rem;
                font-weight: 800;
                text-align: center;
                color: white;
                line-height: 1;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
                margin-bottom: 0.5rem;
            }
            
            .sub-heading {
                font-size: 2rem;
                font-weight: 600;
                text-align: center;
                color: white;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
            }

            .download-section {
                margin-top: 5rem;
                text-align: center;
                padding: 2rem;
                background-color: rgba(255, 255, 255, 0.15);
                border-radius: 16px;
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
            }

            .download-heading {
                font-size: 2.5rem;
                font-weight: 700;
                color: white;
                margin-bottom: 1.5rem;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
            }

            .download-subheading {
                font-size: 1.3rem;
                color: white;
                margin-bottom: 2rem;
                max-width: 600px;
                margin-left: auto;
                margin-right: auto;
            }

            .store-buttons {
                display: flex;
                justify-content: center;
                gap: 2rem;
                flex-wrap: wrap;
                align-items: flex-start;
            }

            .store-button {
                transition: transform 0.3s ease;
                display: inline-block;
                height: 75px; 
            }

            .store-button img {
                height: 75px; /* Altura fija para la imagen */
            }
            
            .store-button:hover {
                transform: scale(1.05);
            }

            @media (max-width: 640px) {
                .main-heading {
                    font-size: 4rem;
                }
                
                .sub-heading {
                    font-size: 1.5rem;
                }
                
                .download-heading {
                    font-size: 2rem;
                }
            }
        </style>
    </head>
    <body class="font-sans antialiased">
        <div class="text-black/80 dark:text-white/80">
            <div class="relative min-h-screen flex flex-col items-center justify-center selection:bg-[#FF2D20] selection:text-white">
                <div class="relative min-h-screen flex flex-col justify-center w-full max-w-2xl px-6 lg:max-w-7xl">
                    <header class="grid grid-cols-2 items-center gap-2 py-10 lg:grid-cols-3 absolute top-0 w-full">
                        <div class="flex lg:justify-center lg:col-start-2">
                            
                        </div>
                        @if (Route::has('login'))
                            <nav class="-mx-3 flex flex-1 justify-end hidden">
                                @auth
                                    
                                        href="{{ url('/dashboard') }}"
                                        class="rounded-md px-3 py-2 text-black ring-1 ring-transparent transition hover:text-black/70 focus:outline-none focus-visible:ring-[#FF2D20] dark:text-white dark:hover:text-white/80 dark:focus-visible:ring-white"
                                    >
                                        Dashboard
                                    </a>
                                @else
                                    
                                        href="{{ route('login') }}"
                                        class="rounded-md px-3 py-2 text-black ring-1 ring-transparent transition hover:text-black/70 focus:outline-none focus-visible:ring-[#FF2D20] dark:text-white dark:hover:text-white/80 dark:focus-visible:ring-white"
                                    >
                                        Log in
                                    </a>

                                    @if (Route::has('register'))
                                        
                                            href="{{ route('register') }}"
                                            class="rounded-md px-3 py-2 text-black ring-1 ring-transparent transition hover:text-black/70 focus:outline-none focus-visible:ring-[#FF2D20] dark:text-white dark:hover:text-white/80 dark:focus-visible:ring-white"
                                        >
                                            Register
                                        </a>
                                    @endif
                                @endauth
                            </nav>
                        @endif
                    </header>

                    <!-- Heading y Subheading centrados -->
                    <div class="text-center">
                        <h1 class="main-heading">MIA</h1>
                        <h2 class="sub-heading">Matalascañas Info Agenda</h2>
                    </div>
                    
                    <!-- Sección de descarga de app -->
                    <div class="download-section">
                        <h3 class="download-heading">Descarga nuestra app</h3>
                        <p class="download-subheading">Toda la información de Matalascañas en tu bolsillo. Eventos, restaurantes, actividades y mucho más.</p>
                        <div class="store-buttons">
                            <!-- App Store Button -->
                            <a href="#" class="store-button">
                                <img src="https://developer.apple.com/app-store/marketing/guidelines/images/badge-download-on-the-app-store.svg" alt="Download on the App Store" height="50">
                            </a>
                            
                            <!-- Google Play Button -->
                            <a href="#" class="store-button">
                                <img src="https://play.google.com/intl/es-419/badges/static/images/badges/es-419_badge_web_generic.png" alt="Get it on Google Play" height="50">
                            </a>
                        </div>
                    </div>

                    <footer class="py-16 text-center text-sm text-white/70 hidden absolute bottom-0 w-full">
                        Laravel v{{ Illuminate\Foundation\Application::VERSION }} (PHP v{{ PHP_VERSION }})
                    </footer>
                </div>
            </div>
        </div>
    </body>
</html>