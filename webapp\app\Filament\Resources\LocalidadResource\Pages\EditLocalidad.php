<?php

namespace App\Filament\Resources\LocalidadResource\Pages;

use App\Models\Zona;
use Filament\Actions;
use App\Models\Negocio;
use App\Models\Localidad;
use App\Services\CacheService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Resources\LocalidadResource;

class EditLocalidad extends EditRecord
{
    protected static string $resource = LocalidadResource::class;

    public static bool $isEditMode = true;

    protected CacheService $cacheService;

    public function boot(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    public function getView(): string
    {
        return 'filament.pages.record-form-with-map';
    }

    protected function handleRecodrUpdate(Model $record, array $data): Model
    {
        $record->update($data);

        // $this->cacheService->invalidateCache(Localidad::class, 'localidades_all');
        // $this->cacheService->invalidateCache(Zona::class, 'zonas_all');
        // $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');

        return $record;
    }

    public function isEdit(): bool
    {
        return true;
    }
}
