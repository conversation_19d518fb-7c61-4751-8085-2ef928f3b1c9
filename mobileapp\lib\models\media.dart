import 'package:flutter/material.dart';
import 'package:mia/config/environment.dart';

class Media {
  final int id;
  final String nombre;
  final String url;
  final String thumb;

  Media({
    required this.id,
    required this.nombre,
    required this.url,
    required this.thumb, // Nivel requerido en el constructor
  });

  factory Media.fromJson(Map<String, dynamic> json) {
    // Validar campos requeridos y su tipo
    if (json['id'] == null || json['id'] is! int) {
      throw Exception('El campo id en Media debe ser un entero');
    }
    if (json['name'] == null || json['name'] is! String) {
      throw Exception(
          'El campo name en Media es requerido y debe ser un String');
    }
    if (json['url'] == null || json['url'] is! String) {
      throw Exception(
          'El campo url en Media es requerido y debe ser un String');
    }
    if (json['thumb'] == null || json['thumb'] is! String) {
      throw Exception(
          'El campo thumb en Media es requerido y debe ser un String');
    }

    String parseUrl(String url) {
      if (EnvironmentConfig.isLocal) {
        return url.replaceFirst('https://mia.test', 'http://********/mia');
      }
      return url;
    }

    return Media(
      id: json['id'],
      nombre: json['name'],
      url: parseUrl(json['url']),
      thumb: parseUrl(json['thumb']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': nombre,
      'url': url.replaceFirst('http://********/mia', 'https://mia.test'),
      'thumb': thumb.replaceFirst('http://********/mia', 'https://mia.test'),
    };
  }

  Widget buildImage({
    double width = 50,
    double height = 50,
    BoxFit fit = BoxFit.cover,
    Widget? loadingPlaceholder,
    Widget? errorPlaceholder,
  }) {
    return Image.network(
      thumb, // Usar la URL de la miniatura
      width: width,
      height: height,
      fit: fit,
      loadingBuilder: (BuildContext context, Widget child,
          ImageChunkEvent? loadingProgress) {
        if (loadingProgress == null) return child;
        return loadingPlaceholder ?? // Usar loadingPlaceholder si se proporciona
            Center(
              // Marcador de posición por defecto
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
              ),
            );
      },
      errorBuilder: (context, error, stackTrace) =>
          errorPlaceholder ??
          getDefaultImage(
            width: width,
            height: height,
            fit: fit,
          ), // Widget para mostrar en caso de error
    );
  }

  static Widget getDefaultImage({
    double width = 50,
    double height = 50,
    BoxFit fit = BoxFit.cover,
  }) {
    return Image.asset(
      // Mostrar imagen por defecto desde assets
      'assets/mia-splash.png',
      width: width,
      height: height,
      fit: fit,
    );
  }
}
