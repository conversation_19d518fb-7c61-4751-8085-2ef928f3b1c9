# Plantilla: Test de API (Backend)

## Plantilla Base

```php
<?php

namespace Tests\Feature\Api;

use Tests\BaseTest;
use App\Models\{ModelName};
use App\Models\User;
use Spatie\Permission\Models\Role;
use PHPUnit\Framework\Attributes\Group;
use Database\Seeders\RolesPermissionSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;

#[Group('api')]
#[Group('{model_slug}')]
class {ModelName}ApiTest extends BaseTest
{
    use RefreshDatabase;

    private array $usuarios;
    private string $api_slug = '/api/v1/{model_slug_plural}';

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed(RolesPermissionSeeder::class);

        // Crear los usuarios
        foreach (Role::all() as $rol) {
            $user = User::factory()->create();
            $user->assignRole($rol->name);

            $this->usuarios[$rol->name] = [
                'usuario' => $user,
                'token' => $user->createToken('auth_token_' . $rol->name)->plainTextToken,
            ];
        }
    }

    // ========================================
    // TESTS DE ACCESO PÚBLICO
    // ========================================

    public function test_el_listado_es_accesible_publicamente(): void
    {
        // Arrange
        {ModelName}::factory(3)->create();

        // Act
        $response = $this->getJson($this->api_slug);

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(3);
    }

    public function test_mostrar_un_recurso_es_accesible_publicamente(): void
    {
        // Arrange
        $model = {ModelName}::factory()->create();

        // Act
        $response = $this->getJson("{$this->api_slug}/{$model->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJson(['id' => $model->id])
            ->assertJsonStructure([
                'id',
                'nombre',
                // ... más campos
            ]);
    }

    // ========================================
    // TESTS DE CREACIÓN (CREATE)
    // ========================================

    public function test_solo_usuarios_con_permiso_pueden_crear(): void
    {
        // 🟢 Test positivos: usuarios CON permiso
        $rolesConPermiso = $this->_getRolesPositivos(['{model_slug}.create']);

        foreach ($rolesConPermiso as $role) {
            $this->actingAs($this->usuarios[$rol]['usuario']);;
            $data = {ModelName}::factory()->make()->toArray();
            $token = $this->usuarios[$rol]['token'];


            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->postJson("{$this->api_slug}", $data);


            $response->assertStatus(201)
                ->assertJsonStructure([
                    // ... comprobar que la estructura del JSON es correcta
                    // Ejemplo: 'id', 'nombre', 'descripcion', 'created_at', 'updated_at'
                ])
                ->assertJson([
                    // ... comprobar que los campos tienen los valores de $data
                    // Ejemplo: 'nombre' => $data['nombre'], 'descripcion' => $data['descripcion'],
                ]);

            $this->assertDatabaseHas('{table_name}', [
                // ... comprobar que los datos están en la base de datos
                // Ejemplo: 'nombre' => $data['nombre'], 'descripcion' => $data['descripcion'],
            ]);
        }

        // 🔴 Test negativos: usuarios SIN permiso

        $test_roles = $this->_getRolesNegativos(['{model_slug}.create']);

        foreach ($test_roles as $rol) {
            $this->actingAs($this->usuarios[$rol]['usuario']);;
            $data = {ModelName}::factory()->make()->toArray();

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->usuarios[$rol]['token'],
            ])->postJson("{$this->api_slug}", $data);

            $response->assertStatus(403);
        }
    }

    public function test_usuarios_no_autenticados_no_pueden_crear(): void
    {
        $data = {ModelName}::factory()->make()->toArray();
        $response = $this->postJson($this->api_slug, $data);

        $response->assertStatus(401);
    }

    public function test_el_sistema_no_crea_nuevo_modelo_con_datos_inválidos(): void
    {
        // Añadir al array todos los casos de datos inválidos
        $testCases = [
            // Caso 1: Enviar datos vacíos
            [
                'payload' => [],
                'expectedInvalid' => ['nombre']
            ],

        ];

        $rol = $this->_getRolesPositivos(['{model_slug}.create']);

        if (is_array($rol)) {
            $rol = $rol[0] ?? null;
        }

        if (!$rol) {
            return;
        }

        $token = $this->usuarios[$rol]['token'];

        foreach ($testCases as $case) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->postJson("{$this->api_slug}", $case['payload']);
        }

        if (isset($case['expectedForbidden']) && $case['expectedForbidden']) {
            $response->assertForbidden(); // Verificar acceso denegado
        } else {
            $response->assertInvalid($case['expectedInvalid']);
        }
    }

    // ========================================
    // TESTS DE ACTUALIZACIÓN (UPDATE)
    // ========================================

    public function test_solo_usuarios_con_permiso_pueden_actualizar(): void
    {
        $model = {ModelName}::factory()->create();

        // 🟢 Test positivos

        $test_roles = $this->_getRolesPositivos(['{model_slug}.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];
            $token = $this->usuarios[$rol]['token'];

            $this->actingAs($user);

            $model->refresh();
            $data = {ModelName}::factory()->make()->toArray();

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->putJson("{$this->api_slug}/{$model->id}", $data);

            $response->assertStatus(200)
                ->assertJsonStructure([
                    // ... comprobar que la estructura del JSON es correcta
                    // Ejemplo: 'id', 'nombre', 'descripcion', 'created_at', 'updated_at'
                ])
                ->assertJson([
                    // ... comprobar que los campos tienen los valores de $data
                    // Ejemplo: 'nombre' => $data['nombre'], 'descripcion' => $data['descripcion'],
                ]);

            $this->assertDatabaseHas('{table_name}', [
                // ... comprobar que los datos están en la base de datos
                // Ejemplo: 'nombre' => $data['nombre'], 'descripcion' => $data['descripcion'],
            ]);
        }

        // 🔴 Test negativos

        $test_roles = $this->_getRolesNegativos(['{model_slug}.update']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];
            $token = $this->usuarios[$rol]['token'];

            $this->actingAs($user);

            $model = {ModelName}::factory()->create();
            $data = {ModelName}::factory()->make()->toArray();

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->putJson("{$this->api_slug}/{$model->id}", $data);

            $response->assertStatus(403);
        }
    }

    public function test_usuarios_no_autenticados_no_pueden_actualizar(): void
    {
        $model = {ModelName}::factory()->create();

        $response = $this->putJson("{$this->api_slug}/{$model->id}", [
            'nombre' => 'Nombre Actualizado',
        ]);

        $response->assertStatus(401);
    }


    public function test_el_sistema_no_edita_un_modelo_con_datos_invalidos(): void
    {
        $model = {ModelName}::factory()->create();

        // Casos de datos inválidos
        $testCases = [
            // Caso 1: Enviar datos vacíos
            [
                'payload' => [],
                'expectedInvalid' => ['nombre']
            ],
        ];

        $rol = $this->_getRolesPositivos(['{model_slug}.update']);

        if (is_array($rol)) {
            $rol = $rol[0] ?? null;
        }

        if (!$rol) {
            return;
        }

        $token = $this->usuarios[$rol]['token'];

        foreach ($testCases as $case) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])
                ->putJson("{$this->api_slug}/{$model->id}", $case['payload']);
        }

        if (isset($case['expectedForbidden']) && $case['expectedForbidden']) {
            $response->assertForbidden(); // Verificar acceso denegado
        } else {
            $response->assertInvalid($case['expectedInvalid']);
        }
    }

    // ========================================
    // TESTS DE ELIMINACIÓN (DELETE)
    // ========================================

    public function test_solo_usuarios_con_permiso_pueden_eliminar(): void
    {
        // 🟢 Test positivos

        $test_roles = $this->_getRolesPositivos(['{model_slug}.delete']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];
            $token = $this->usuarios[$rol]['token'];

            $this->actingAs($user);

            $model = {ModelName}::factory()->create();

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->deleteJson("{$this->api_slug}/{$model->id}");

            $response->assertNoContent();

            // Verificar que el negocio no existe en la base de datos
            $this->assertDatabaseMissing({ModelName}::class, [
                'id' => $model->id,
            ]);
        }


        // 🔴 Test negativos

        $test_roles = $this->_getRolesNegativos(['{model_slug}.delete']);

        foreach ($test_roles as $rol) {
            $user = $this->usuarios[$rol]['usuario'];
            $token = $this->usuarios[$rol]['token'];

            $this->actingAs($user);

            $model = {ModelName}::factory()->create();

            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->deleteJson("{$this->api_slug}/{$model->id}");

            $response->assertStatus(403);

            // Verificar que la categoría existe en la base de datos
            $this->assertDatabaseHas({ModelName}::class, [
                'id' => $model->id,
            ]);
        }
    }

    public function test_usuarios_no_autenticados_no_pueden_eliminar(): void
    {
        $model = {ModelName}::factory()->create();

        $response = $this->deleteJson("{$this->api_slug}/{$model->id}");

        $response->assertStatus(401);
    }

    // ========================================
    // TESTS DE CASOS EDGE
    // ========================================

    public function test_retorna_404_cuando_el_recurso_no_existe(): void
    {
        $response = $this->getJson("{$this->api_slug}/99999");

        $response->assertStatus(404);
    }
}
```

## Checklist de Tests

Al crear tests para un nuevo endpoint, asegúrate de cubrir:

### Acceso Público (si aplica)

-  [ ] Listar recursos es público
-  [ ] Ver un recurso es público
-  [ ] Estructura JSON correcta

### Creación (POST)

-  [ ] Solo usuarios con permiso pueden crear
-  [ ] Usuarios sin permiso reciben 403
-  [ ] Usuarios no autenticados reciben 401
-  [ ] Validación de campos requeridos
-  [ ] Validación de tipos de datos
-  [ ] Validación de relaciones existentes

### Actualización (PUT/PATCH)

-  [ ] Solo usuarios con permiso pueden actualizar
-  [ ] Usuarios sin permiso reciben 403
-  [ ] Usuarios no autenticados reciben 401
-  [ ] Validación de campos
-  [ ] Propietarios pueden actualizar sus recursos (parcial o totalmente)

### Eliminación (DELETE)

-  [ ] Solo usuarios con permiso pueden eliminar
-  [ ] Usuarios sin permiso reciben 403
-  [ ] Usuarios no autenticados reciben 401
-  [ ] Recurso se elimina de la BD

### Casos Edge

-  [ ] 404 cuando recurso no existe
-  [ ] Manejo de datos duplicados (si aplica)

## Reemplazos Necesarios

Al usar esta plantilla, reemplaza:

-  `{ModelName}` → Nombre del modelo (ej: `Negocio`, `Evento`)
-  `{model_slug}` → Slug del modelo (ej: `negocio`, `evento`)
-  `{model_slug_plural}` → Plural del slug (ej: `negocios`, `eventos`)
-  `{table_name}` → Nombre de la tabla (ej: `negocios`, `eventos`)

## Ejemplo de Uso

```bash
# Copiar plantilla
cp docs/06-testing/plantilla-api-test.md tests/Feature/Api/EventoApiTest.php

# Reemplazar valores
# {ModelName} → Evento
# {model_slug} → evento
# {model_slug_plural} → eventos
# {table_name} → eventos

# Ejecutar tests
php artisan test --filter EventoApiTest
```
