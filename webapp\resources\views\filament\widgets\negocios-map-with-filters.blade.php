<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            Mapa de Negocios
        </x-slot>

        <x-slot name="headerEnd">
            <x-filament::button
                wire:click="clearFilters"
                size="sm"
                color="gray"
                outlined
            >
                Limpiar filtros
            </x-filament::button>
        </x-slot>

        {{-- Formulario de filtros --}}
        <div class="mb-4">
            {{ $this->filtersForm }}
        </div>

        {{-- Widget del mapa --}}
        <div class="mt-4">
            @livewire(\App\Filament\Widgets\NegociosMap::class, [
                'negocios' => $negocios,
                'filters' => $filters
            ], key('negocios-map-' . md5(serialize($filters))))
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
