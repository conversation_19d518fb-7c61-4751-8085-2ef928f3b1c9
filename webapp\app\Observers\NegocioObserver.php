<?php

namespace App\Observers;

use App\Models\Negocio;
use App\Services\CacheService;
use App\Enums\EstadoSuscripcion;

class NegocioObserver
{
    protected CacheService $cache;

    public function __construct(CacheService $cache)
    {
        $this->cache = $cache;
    }

    /**
     * Handle the Negocio "created" event.
     */
    public function created(Negocio $negocio): void
    {
        $this->invalidate();
    }

    /**
     * Handle the Negocio "updated" event.
     */
    public function updated(Negocio $negocio): void
    {
        $this->invalidate();
    }

    /**
     * Handle the Negocio "deleted" event.
     */
    public function deleted(Negocio $negocio): void
    {
        $this->invalidate();
    }

    /**
     * Handle the Negocio "restored" event.
     */
    public function restored(Negocio $negocio): void
    {
        $this->invalidate();
    }

    /**
     * Handle the Negocio "force deleted" event.
     */
    public function forceDeleted(Negocio $negocio): void
    {
        $this->invalidate();
    }

    protected function invalidate()
    {
        $this->cache->invalidateCache(Negocio::class, 'negocios_all');
    }
}
