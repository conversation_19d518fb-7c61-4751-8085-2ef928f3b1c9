import 'package:flutter_test/flutter_test.dart';
import 'package:mia/models/zona.dart';
import '../../helpers/base_test_helper.dart';
import 'utils/model_test_utils.dart';

void main() async {
  TestWidgetsFlutterBinding.ensureInitialized();
  await BaseTest.initialize();

  group('Zona Model Tests', () {
    ModelTestUtils.runCommonModelTests<Zona>(
      validFixturePaths: [
        'zona/valid_zona_1.json',
        'zona/valid_zona_2.json',
      ],
      invalidFixturePaths: [
        'zona/invalid_zona_type.json',
        'zona/invalid_zona_missing.json',
      ],
      fromJson: Zona.fromJson,
      toJson: (zona) => zona.toJson(),
      getExpectedValues: (fixturePath) => _getZonaExpectedValues(fixturePath),
      customModel: {
        'id': 99,
        'nombre': 'Zona de Prueba',
        'descripcion': 'Descripción de prueba',
      },
      requiredFields: ['id', 'nombre'],
    );

    _runSpecificTests();
  });
}

void _runSpecificTests() {
  group('Funciones específicas de Zona:', () {
    // test('find encuentra una zona por su ID', () {
    //   final zonas = [
    //     Zona(id: 1, nombre: 'Centro', descripcion: 'Zona céntrica de la ciudad'),
    //     Zona(id: 2, nombre: 'Playa', descripcion: 'Zona costera'),
    //   ];

    //   final zonaEncontrada = Zona.find(zonas, 1);
    //   expect(zonaEncontrada, isNotNull);
    //   expect(zonaEncontrada!.id, 1);
    // });
  });
}

Map<String, dynamic> _getZonaExpectedValues(String fixturePath) {
  return {
    'valid_zona_1.json': {
      'id': 1,
      'nombre': 'Centro',
      'descripcion': 'Zona céntrica de la ciudad'
    },
    'valid_zona_2.json': {
      'id': 2,
      'nombre': 'Playa',
      'descripcion': 'Zona costera'
    }
  }[fixturePath.split('/').last]!;
}
