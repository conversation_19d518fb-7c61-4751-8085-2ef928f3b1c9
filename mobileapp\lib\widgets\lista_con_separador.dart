import 'package:flutter/material.dart';

/// Define los tipos de layout disponibles para el widget ListaConSeparador
enum TipoLayout {
  column,
  row,
  wrap,
  stack,
}

/// Un widget que toma una lista de widgets y los separa con otro widget especificado.
/// Similar a las funciones explode/implode en otros lenguajes.
class ListaConSeparador extends StatelessWidget {
  /// La lista de widgets que se van a mostrar.
  final List<Widget> children;

  /// El widget separador que se colocará entre cada widget de la lista `children`.
  final Widget separator;

  /// El tipo de layout a utilizar para mostrar los widgets.
  final TipoLayout layoutType;

  /// Dirección en la que se muestran los widgets, vertical u horizontal.
  final Axis direction;

  /// Controla cómo se alinean los widgets a lo largo del eje principal.
  final MainAxisAlignment mainAxisAlignment;

  /// Controla cómo se alinean los widgets a lo largo del eje secundario.
  final CrossAxisAlignment crossAxisAlignment;

  /// Controla si los widgets se ajustan al espacio disponible o no.
  final MainAxisSize mainAxisSize;

  /// Propiedades específicas para Wrap
  final double? spacing;
  final double? runSpacing;
  final WrapAlignment wrapAlignment;
  final WrapAlignment runAlignment;
  final WrapCrossAlignment wrapCrossAlignment;

  const ListaConSeparador({
    super.key,
    required this.children,
    required this.separator,
    this.layoutType = TipoLayout.column,
    this.direction = Axis.vertical,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
    this.spacing,
    this.runSpacing,
    this.wrapAlignment = WrapAlignment.start,
    this.runAlignment = WrapAlignment.start,
    this.wrapCrossAlignment = WrapCrossAlignment.start,
  });

  @override
  Widget build(BuildContext context) {
    // Si no hay elementos, devolver un contenedor vacío
    if (children.isEmpty) {
      return Container();
    }

    // Si solo hay un elemento, devolver ese elemento sin separadores
    if (children.length == 1) {
      return children.first;
    }

    // Crear la lista intercalada con separadores
    final List<Widget> implodedWidgets = [];

    for (int i = 0; i < children.length; i++) {
      // Agregar el widget actual
      implodedWidgets.add(children[i]);

      // Agregar el separador después de cada widget excepto el último
      if (i < children.length - 1) {
        implodedWidgets.add(separator);
      }
    }

    // Devolver el widget correspondiente según el tipo de layout
    switch (layoutType) {
      case TipoLayout.column:
        return Column(
          mainAxisAlignment: mainAxisAlignment,
          crossAxisAlignment: crossAxisAlignment,
          mainAxisSize: mainAxisSize,
          children: implodedWidgets,
        );
      case TipoLayout.row:
        return Row(
          mainAxisAlignment: mainAxisAlignment,
          crossAxisAlignment: crossAxisAlignment,
          mainAxisSize: mainAxisSize,
          children: implodedWidgets,
        );
      case TipoLayout.wrap:
        return Wrap(
          direction: direction,
          alignment: wrapAlignment,
          crossAxisAlignment: wrapCrossAlignment,
          spacing: spacing ?? 0.0,
          runSpacing: runSpacing ?? 0.0,
          runAlignment: runAlignment,
          children: implodedWidgets,
        );
      case TipoLayout.stack:
        return Stack(
          alignment: Alignment.center,
          children: implodedWidgets,
        );
    }
  }
}
