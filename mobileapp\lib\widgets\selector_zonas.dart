import 'package:flutter/material.dart';
// import 'package:mia/config/styles.dart';
import 'package:mia/models/zona.dart';
import 'package:mia/widgets/tag.dart';

class SelectorZonas extends StatefulWidget {
  final List<Zona>? zonas;
  final void Function(List<Zona>)? onChanged;

  const SelectorZonas({super.key, required this.zonas, this.onChanged});

  @override
  State<SelectorZonas> createState() => _SelectorZonasState();
}

class _SelectorZonasState extends State<SelectorZonas> {
  final List<Zona> _zonasSeleccionadas = [];
  List<Zona> _zonasPrincipales = []; // Lista para las zonas principales

  @override
  void initState() {
    super.initState();
    _cargarZonas();
  }

  Future<void> _cargarZonas() async {
    if (widget.zonas != null) {
      _zonasPrincipales =
          widget.zonas!; // Todas las zonas son principales por ahora
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 8.0,
      runSpacing: 4.0,
      children: _zonasPrincipales.map((zona) {
        final isSelected = _zonasSeleccionadas.any((z) => z.id == zona.id);
        return _buildZonaTag(zona, isSelected);
      }).toList(),
    );
  }

  Tag _buildZonaTag(Zona zona, bool isSelected) {
    return Tag(
      label: zona.nombre,
      description: zona.descripcion,
      isSelected: isSelected,
      onTap: () {
        setState(() {
          if (isSelected) {
            _zonasSeleccionadas.removeWhere((z) => z.id == zona.id);
          } else {
            _zonasSeleccionadas.add(zona);
          }

          if (widget.onChanged != null) {
            widget.onChanged!(_zonasSeleccionadas);
          }
        });
        // debugPrint(
        //     "Zonas seleccionadas: ${_zonasSeleccionadas.map((z) => z.nombre).toList()}");
      },
      onClose: isSelected
          ? () {
              setState(() {
                _zonasSeleccionadas.removeWhere((z) => z.id == zona.id);
                if (widget.onChanged != null) {
                  widget.onChanged!(_zonasSeleccionadas);
                }
              });
            }
          : null,
    );
  }

  // Exponer las zonas seleccionadas
  List<Zona> get zonasSeleccionadas => _zonasSeleccionadas;
}
